group 'iot-cloud-backend'
version 'v2020.07.01-PATCH'
description 'This is the Backend Repo for Optim'

buildscript {
    ext {
        springBootVersion = '2.0.5.RELEASE'
    }
    repositories {
        mavenCentral()
        maven {
            url "https://plugins.gradle.org/m2/"
        }
    }
    dependencies {
        classpath("org.springframework.boot:spring-boot-gradle-plugin:${springBootVersion}")
    }
}

apply plugin: 'java'
apply plugin: 'org.springframework.boot'
apply plugin: 'maven'
apply plugin: 'maven-publish'
apply plugin: "io.spring.dependency-management"
apply plugin: 'project-report'

// Task to clone the open-api-document repository, put the openapi-external.yaml under resources/static/swagger
task deployOpenApiDocs {
    doLast {
        // Determine target branch: check environment variable first, then properties file
        def checkoutTarget = "main" // Default value

        // Check system environment variable first
        def envBranch = System.getenv("apidoc_build_branch")
        if (envBranch) {
            checkoutTarget = envBranch
            println "Using apidoc_build_branch from environment variable: ${checkoutTarget}"
        } else {
            // If no environment variable, check properties file
            def propsFile = new File("openapi-document.properties")
            def props = new Properties()

            if (propsFile.exists()) {
                propsFile.withInputStream { props.load(it) }
                if (props.getProperty("apidoc_build_branch")) {
                    checkoutTarget = props.getProperty("apidoc_build_branch")
                    println "Using apidoc_build_branch from properties file: ${checkoutTarget}"
                } else {
                    println "No apidoc_build_branch property found in properties file, using default: ${checkoutTarget}"
                }
            } else {
                println "Properties file not found, using default branch: ${checkoutTarget}"
            }
        }

        // Define the directory where the repository will be cloned
        def repoDir = new File("${project.buildDir}/open-api-document")

        // Delete the directory if it already exists
        if (repoDir.exists()) {
            repoDir.deleteDir()
        }

        // Create the directory
        repoDir.mkdirs()

        // Clone the repository using ProcessBuilder
        def process = new ProcessBuilder(
                "git",
                "clone",
                "*****************:Actiontec/open-api-document.git",
                repoDir.absolutePath
        )
        process.environment().putAll(System.getenv())
        process.environment().put("GIT_SSH_COMMAND", "ssh -o StrictHostKeyChecking=no")
        process.redirectErrorStream(true)
        def proc = process.start()

        // Log the output
        def reader = new BufferedReader(new InputStreamReader(proc.getInputStream()))
        String line
        while ((line = reader.readLine()) != null) {
            println line
        }

        // Wait for the process to complete
        def exitCode = proc.waitFor()
        if (exitCode != 0) {
            throw new GradleException("Git clone failed with exit code: ${exitCode}")
        }

        println "Successfully cloned open-api-document repository to ${repoDir}"

        println "Git checkout target: ${checkoutTarget}"

        // Checkout the specified branch
        def checkoutProcess = new ProcessBuilder(
                "git",
                "checkout",
                checkoutTarget
        )
        checkoutProcess.directory(repoDir)
        checkoutProcess.redirectErrorStream(true)
        def checkoutProc = checkoutProcess.start()

        // Log the checkout output
        def checkoutReader = new BufferedReader(new InputStreamReader(checkoutProc.getInputStream()))
        while ((line = checkoutReader.readLine()) != null) {
            println line
        }

        // Wait for the checkout process to complete
        def checkoutExitCode = checkoutProc.waitFor()
        if (checkoutExitCode != 0) {
            throw new GradleException("Git checkout ${checkoutTarget} failed with exit code: ${checkoutExitCode}")
        }

        println "Successfully checked out ${checkoutTarget} branch"

        // Define the versions to copy
        def versions = ["v2", "v5", "v6"]

        // Check if all required files exist before copying
        def missingFiles = []
        versions.each { version ->
            def sourceFile = new File("${repoDir}/build/optim/${version}/openapi-external.yaml")
            if (!sourceFile.exists()) {
                missingFiles.add("${version}/openapi-external.yaml")
            }
        }

        // Fail build if any files are missing
        if (!missingFiles.isEmpty()) {
            throw new GradleException("Build failed: Missing required OpenAPI files: ${missingFiles.join(', ')}")
        }

        // Process each version
        versions.each { version ->
            // Create target directory for each version
            def targetDir = new File("${project.buildDir}/resources/main/static/swagger/${version}")
            targetDir.mkdirs()

            // Define source and target files
            def sourceFile = new File("${repoDir}/build/optim/${version}/openapi-external.yaml")
            def targetFile = new File("${targetDir}/openapi-external.yaml")

            // Copy the file
            targetFile.bytes = sourceFile.bytes
            println "Successfully copied ${version}/openapi-external.yaml to ${targetFile.absolutePath}"
        }

        // Delete the cloned repository after copying the files
        println "Deleting cloned repository: ${repoDir}"
        try {
            if (repoDir.exists()) {
                repoDir.deleteDir()
                println "Successfully deleted cloned repository: ${repoDir}"
            }
        } catch (Exception e) {
            println "Warning: Failed to delete cloned repository: ${e.message}"
        }
    }
}

if (project.hasProperty('enableApiDocs')) {
    tasks.getByName('bootJar').dependsOn('deployOpenApiDocs')
    tasks.getByName('deployOpenApiDocs').mustRunAfter('classes')
}


sourceCompatibility = 1.8
targetCompatibility = 1.8

tasks.withType(JavaCompile) {
    options.encoding = 'UTF-8'
}

repositories {
    mavenCentral()
    maven {
        credentials {
             username optim_nexus_username
             password optim_nexus_password
            // username "admin1"
            // password "LTpSSr76juZxvPGY"
        }
        url "https://aws-nexus.optimdev.io/repository/libs-optim"
    }
//    maven {
//        credentials {
//            username "admin"
//            password "lodfw94@dgploq234\$"
//        }
//        url "http://jfrog.dc1-iot83.com/artifactory/libs-release-local"
//    }
//    maven {
//        url "https://repository.cloudera.com/artifactory/libs-release-local/"
//    }
//    maven {
//        url "https://repository.cloudera.com/artifactory/cloudera-repos"
//    }
//    maven {
//        credentials {
//            username "admin"
//            password "lodfw94@dgploq234\$"
//        }
//        url "http://jfrog.dc1-iot83.com/artifactory/libs-snapshot-local"
//    }
}

dependencies {

    compile("org.springframework.boot:spring-boot-starter-web") {
        exclude module: 'org.springframework.boot:spring-boot-starter-logging'
        exclude module: "spring-boot-starter-tomcat"
    }
    compile group: 'org.springframework.security', name: 'spring-security-config', version: '4.2.4.RELEASE'
    compile group: 'org.springframework.security', name: 'spring-security-web', version: '4.2.4.RELEASE'
    compile group: 'org.springframework.boot', name: 'spring-boot-starter-aop', version: '2.0.5.RELEASE'
    compile group: 'com.stormpath.sdk', name: 'stormpath-sdk-impl', version: '1.0.RC3'
    compile group: 'org.apache.commons', name: 'commons-lang3', version: '3.0'
    compile group: 'io.jsonwebtoken', name: 'jjwt', version: '0.6.0'
    compile group: 'com.hazelcast', name: 'hazelcast-client', version: '3.10.4'
    compile group: 'com.hazelcast', name: 'hazelcast', version: '3.10.4'
    compile group: 'org.eclipse.paho', name: 'org.eclipse.paho.client.mqttv3', version: '1.2.2'
    compile group: 'com.fasterxml.jackson.datatype', name: 'jackson-datatype-jsr310', version: '2.9.8'
    compile group: 'com.fasterxml.jackson.core', name: 'jackson-databind', version: '2.9.7'
    compile group: 'com.fasterxml.jackson.core', name: 'jackson-annotations', version: '2.9.7'
    compile group: 'com.fasterxml.jackson.core', name: 'jackson-core', version: '2.9.7'
    compile group: 'io.springfox', name: 'springfox-swagger-ui', version: '2.9.2'
    compile group: 'io.springfox', name: 'springfox-swagger2', version: '2.9.2'
    compile group: 'org.springdoc', name: 'springdoc-openapi-ui', version: '1.8.0', {
        exclude group: 'org.webjars', module: 'swagger-ui'
    }
    compile group: 'org.webjars', name: 'swagger-ui', version: '5.25.3'
    compile group: 'com.google.guava', name: 'guava', version: '24.1.1-jre'
    compile group: 'org.apache.poi', name: 'poi-ooxml', version: '3.9'
    compile group: 'org.mongodb', name: 'mongo-java-driver', version: '3.4.3'
    compile group: 'org.json', name: 'json', version: '20180813'
    compile 'org.apache.poi:poi-ooxml:3.15'
    compile group: 'com.itextpdf', name: 'itextpdf', version: '5.5.13'
    compile group: 'net.sf.opencsv', name: 'opencsv', version: '2.3'
    compile group: 'org.apache.kafka', name: 'kafka-clients', version: '2.2.1'
    compile group: 'org.quartz-scheduler', name: 'quartz', version: '2.2.1'
    compile group: 'org.springframework.kafka', name: 'spring-kafka'
    compile(group: 'com.actiontec', name: 'module-method83', version: '1.0.10')
    compile group: 'org.springframework.boot', name: 'spring-boot-starter-log4j2', version: '2.0.5.RELEASE'
    compile group: 'org.apache.logging.log4j', name: 'log4j-core', version: '2.17.0'
    compile group: 'org.apache.logging.log4j', name: 'log4j-api', version: '2.17.0'
    compile group: 'org.apache.logging.log4j', name: 'log4j-slf4j-impl', version: '2.17.0'
    compile group: 'org.springframework', name: 'spring-core', version: '5.1.0.RELEASE'
    compile group: 'org.springframework.data', name: 'spring-data-commons', version: '2.1.0.RELEASE'
    compile group: 'org.springframework.boot', name: 'spring-boot-starter-mail'
    compile 'commons-io:commons-io:2.4'

    implementation 'org.mapstruct:mapstruct:1.4.1.Final'
    annotationProcessor 'org.mapstruct:mapstruct-processor:1.4.1.Final'

//    implementation platform('software.amazon.awssdk:bom:2.15.0')

    compile group: 'com.datastax.cassandra', name: 'cassandra-driver-core', version: '3.6.0'
    compile group: 'com.datastax.cassandra', name: 'cassandra-driver-mapping', version: '3.6.0'

    compile group: 'com.amazonaws', name: 'aws-java-sdk-ec2', version: '1.11.560'
    compile group: 'javax.validation', name: 'validation-api', version: '2.0.1.Final'

    compile group: 'io.micrometer', name: 'micrometer-registry-prometheus', version: '1.3.2'
    compile group: 'io.micrometer', name: 'micrometer-core', version: '1.3.2'

    compile group: 'org.apache.thrift', name: 'libthrift', version: '0.12.0'
//    compile (group: 'org.apache.hive', name: 'hive-jdbc', version: '1.1.0-cdh5.15.1'){
//        exclude module: 'jasper-compiler'
//        exclude module: 'zookeeper'
//        exclude module: 'commons-compress'
//        exclude module: 'commons-dbcp'
//        exclude module: 'jersey-client'
//        exclude module: 'jersey-guice'
//        exclude module: 'curator-client'
//        exclude module: 'curator-framework'
//        exclude module: 'jersey-json'
//        exclude module: 'curator-recipes'
//        exclude module: 'jersey-servlet'
//        exclude module: 'jersey-core'
//        exclude module: 'jersey-server'
//    }
    compile group: 'org.elasticsearch.client', name: 'elasticsearch-rest-high-level-client', version: '6.5.4'
    compile group: 'org.elasticsearch', name: 'elasticsearch', version: '6.5.4'
    compile 'org.elasticsearch.client:elasticsearch-rest-client:6.5.4'
    compile group: 'com.amazonaws', name: 'aws-java-sdk-cloudwatch', version: '1.11.525'
    compile group: 'org.springframework.boot', name: 'spring-boot-starter-actuator', version: '2.0.5.RELEASE'
    implementation group: 'com.zaxxer', name: 'HikariCP', version: "3.4.5"
    implementation group: 'org.apache.commons', name: 'commons-csv', version: '1.9.0'
    implementation group: 'commons-codec', name: 'commons-codec', version: '1.9'
    implementation group: 'com.auth0', name: 'java-jwt', version: '3.3.0'
    implementation group: 'io.github.erdtman', name: 'java-json-canonicalization', version: '1.1'

    // Lombok
    compileOnly 'org.projectlombok:lombok:1.18.32'
    annotationProcessor 'org.projectlombok:lombok:1.18.32'

    testCompileOnly 'org.projectlombok:lombok:1.18.12'
    testAnnotationProcessor 'org.projectlombok:lombok:1.18.12'
    
    testCompile 'org.springframework.boot:spring-boot-starter-test'
}
configurations {
    compile.exclude module: ("spring-boot-starter-tomcat")
    all*.exclude module: 'spring-boot-starter-logging'
//    all*.exclude module: 'commons-codec'
    all*.exclude module: "log4j-slf4j-impl"
    all*.exclude module: 'commons-cli'
    all*.exclude module: 'commons-lang'
}
bootRun {
    systemProperties = System.properties
    systemProperty "spring.profiles.active", "dev"
}

tasks.test {
    enabled = false
}