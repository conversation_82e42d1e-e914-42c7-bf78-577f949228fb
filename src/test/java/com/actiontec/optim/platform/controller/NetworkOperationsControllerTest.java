package com.actiontec.optim.platform.controller;

import static org.junit.Assert.assertEquals;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.patch;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import java.util.List;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import com.actiontec.optim.platform.BaseTest;
import com.actiontec.optim.platform.api.v5.model.EquipmentActionRequest;
import com.actiontec.optim.platform.api.v6.dto.NCSEquipmentForAppCloudDTO;
import com.actiontec.optim.platform.api.v6.dto.NCSEquipmentRequest;
import com.fasterxml.jackson.core.type.TypeReference;

public class NetworkOperationsControllerTest  extends BaseTest{

    @Autowired
    private MockMvc mockMvc;

    final String TOKEN_HEADER = "X-AUTHORIZATION";
    
    final String networkTokenHeader = "networkTokenId";
    final String networkToken = "fa3d4d94f1af4dd084cf083a47a5694f";

    final String BASE_URL = "/actiontec/api/v6/networks";

    final String networkId = "e5bf53689bdf487385ef13a32610be04";
    final String equipmentId = "00617869556140189e8aa15edd386bc0";
    final String notExistEquipmentId = "1234567890";

    /**
     * Test case for GET /actiontec/api/v6/networks/{networkId}/equipment
     * Expected: 200 OK
     * @throws Exception
     */
    @Test
    public void testGetNetworkEquipment() throws Exception {

        final String url = BASE_URL + "/{networkId}/equipment";

        String body = mockMvc.perform(
                get(url, networkId)
                .header("Origin","*")
                .header(TOKEN_HEADER, token)
                .header(networkTokenHeader, networkToken)
            )
            .andDo(print())
            .andExpect(status().isOk())
            .andReturn().getResponse().getContentAsString();

        List<NCSEquipmentForAppCloudDTO> response = 
            objectMapper.readValue(body, new TypeReference<List<NCSEquipmentForAppCloudDTO>>() {});
        
        assertEquals(2, response.size());
    }

    /**
     * Test case for GET /actiontec/api/v6/networks/{networkId}/equipment
     * Expected: 401 Unauthorized
     * @throws Exception
     */
    @Test
    public void testGetNetworkEquipmentWithUnauthorizedHeader() throws Exception {
        final String url = BASE_URL + "/{networkId}/equipment";
        
        mockMvc.perform(
                get(url, networkId, equipmentId)
                .header("Origin","*")
                .header(networkTokenHeader, networkToken)
            )
            .andDo(print())
            .andExpect(status().isUnauthorized())
            .andReturn().getResponse().getContentAsString();
    }

    /**
     * Test case for GET /actiontec/api/v6/networks/{networkId}/equipment/{equipmentId}
     * Expected: 200 OK
     * @throws Exception
     */
    @Test
    public void testGetNetworkEquipmentByEquipmentId() throws Exception {

        final String url = BASE_URL + "/{networkId}/equipment/{equipmentId}";
        
        String body = mockMvc.perform(
                get(url, networkId, equipmentId)
                .header("Origin","*")
                .header(TOKEN_HEADER, token)
                .header(networkTokenHeader, networkToken)
            )
            .andDo(print())
            .andExpect(status().isOk())
            .andReturn().getResponse().getContentAsString();

        NCSEquipmentForAppCloudDTO response =
            objectMapper.readValue(body, NCSEquipmentForAppCloudDTO.class);

        assertEquals(equipmentId, response.getId());
    }


    /**
     * Test case for GET /actiontec/api/v6/networks/{networkId}/equipment/{equipmentId}
     * Expected: 401 Unauthorized
     * @throws Exception
     */
    @Test
    public void testGetNetworkEquipmentByEquipmentIdWithUnauthorizedHeader() throws Exception {
        final String url = BASE_URL + "/{networkId}/equipment/{equipmentId}";
        
        mockMvc.perform(
                get(url, networkId, equipmentId)
                .header("Origin","*")
                .header(networkTokenHeader, networkToken)
            )
            .andDo(print())
            .andExpect(status().isUnauthorized())
            .andReturn().getResponse().getContentAsString();
    }

    /**
     * Test case for GET /actiontec/api/v6/networks/{networkId}/equipment/{equipmentId}
     * Expected: 404 Not Found
     * @throws Exception
     */
    @Test
    public void testGetNetworkEquipmentByEquipmentIdNotFound() throws Exception {
        final String url = BASE_URL + "/{networkId}/equipment/{equipmentId}";
        
        mockMvc.perform(
                get(url, networkId, notExistEquipmentId)
                .header("Origin","*")
                .header(TOKEN_HEADER, token)
                .header(networkTokenHeader, networkToken)
            )
            .andDo(print())
            .andExpect(status().isNotFound())
            .andReturn().getResponse().getContentAsString();
    }

    /**
     * Test case for PATCH /actiontec/api/v6/networks/{networkId}/equipment/{equipmentId}
     * Expected: 200 OK
     * @throws Exception
     */
    @Test
    public void testUpdateNetworkEquipment() throws Exception {
        final String url = BASE_URL + "/{networkId}/equipment/{equipmentId}";

        NCSEquipmentRequest request = new NCSEquipmentRequest();
        request.setName("Spring Test Equipment");
        
        mockMvc.perform(
                patch(url, networkId, equipmentId)
                .header("Origin","*")
                .header(TOKEN_HEADER, token)
                .header(networkTokenHeader, networkToken)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request))
            )
            .andDo(print())
            .andExpect(status().isOk())
            .andReturn().getResponse().getContentAsString();
    }


    /**
     * Test case for PATCH /actiontec/api/v6/networks/{networkId}/equipment/{equipmentId}
     * Expected: 400 Bad Request
     * @throws Exception
     */
    @Test
    public void testUpdateNetworkEquipmentWithBadRequest() throws Exception {
        final String url = BASE_URL + "/{networkId}/equipment/{equipmentId}";

        NCSEquipmentRequest request = new NCSEquipmentRequest();
        request.setName("");
        
        mockMvc.perform(
                patch(url, networkId, equipmentId)
                .header("Origin","*")
                .header(TOKEN_HEADER, token)
                .header(networkTokenHeader, networkToken)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request))
            )
            .andDo(print())
            .andExpect(status().isBadRequest())
            .andReturn().getResponse().getContentAsString();
    }

    /**
     * Test case for PATCH /actiontec/api/v6/networks/{networkId}/equipment/{equipmentId}
     * Expected: 401 Unauthorized
     * @throws Exception
     */
    @Test
    public void testUpdateNetworkEquipmentWithUnauthorizedHeader() throws Exception {
        final String url = BASE_URL + "/{networkId}/equipment/{equipmentId}";

        NCSEquipmentRequest request = new NCSEquipmentRequest();
        request.setName("Spring Test Equipment");
        
        mockMvc.perform(
                patch(url, networkId, equipmentId)
                .header("Origin","*")
                .header(networkTokenHeader, networkToken)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request))
            )
            .andDo(print())
            .andExpect(status().isUnauthorized())
            .andReturn().getResponse().getContentAsString();
    }

    /**
     * Test case for PATCH /actiontec/api/v6/networks/{networkId}/equipment/{equipmentId}
     * Expected: 404 Not Found
     * @throws Exception
     */
    @Test
    public void testUpdateNetworkEquipmentNotFound() throws Exception {
        final String url = BASE_URL + "/{networkId}/equipment/{equipmentId}";

        NCSEquipmentRequest request = new NCSEquipmentRequest();
        request.setName("Spring Test Equipment");
        
        mockMvc.perform(
                patch(url, networkId, notExistEquipmentId)
                .header("Origin","*")
                .header(TOKEN_HEADER, token)
                .header(networkTokenHeader, networkToken)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request))
            )
            .andDo(print())
            .andExpect(status().isNotFound())
            .andReturn().getResponse().getContentAsString();
    }


    /**
     * Test case for POST /actiontec/api/v6/networks/{networkId}/equipment/{equipmentId}/actions
     * Expected: 400 Bad Request
     * @throws Exception
     */
    @Test
    public void testPostNetworkEquipmentActionWithInvalidRequest() throws Exception {
        final String url = BASE_URL + "/{networkId}/equipment/{equipmentId}/actions";

        EquipmentActionRequest request = new EquipmentActionRequest();
        request.setAction("test");
        
        mockMvc.perform(
                post(url, networkId, equipmentId)
                .header("Origin","*")
                .header(TOKEN_HEADER, token)
                .header(networkTokenHeader, networkToken)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request))
            )
            .andDo(print())
            .andExpect(status().isBadRequest())
            .andReturn().getResponse().getContentAsString();
    }

    /**
     * Test case for POST /actiontec/api/v6/networks/{networkId}/equipment/{equipmentId}/actions
     * Expected: 401 Unauthorized
     * @throws Exception
     */
    @Test
    public void testPostNetworkEquipmentActionWithUnauthorizedHeader() throws Exception {
        final String url = BASE_URL + "/{networkId}/equipment/{equipmentId}/actions";
        
        EquipmentActionRequest request = new EquipmentActionRequest();
        request.setAction("reboot");
        
        mockMvc.perform(
                post(url, networkId, equipmentId)
                .header("Origin","*")
                .header(networkTokenHeader, networkToken)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request))
            )
            .andDo(print())
            .andExpect(status().isUnauthorized())
            .andReturn().getResponse().getContentAsString();
    }

    /**
     * Test case for POST /actiontec/api/v6/networks/{networkId}/equipment/{equipmentId}/actions
     * Expected: 404 Not Found
     * @throws Exception
     */
    @Test
    public void testPostNetworkEquipmentActionByEquipmentIdNotFound() throws Exception {
        final String url = BASE_URL + "/{networkId}/equipment/{equipmentId}/actions";
        
        EquipmentActionRequest request = new EquipmentActionRequest();
        request.setAction("reboot");
        
        mockMvc.perform(
                post(url, networkId, notExistEquipmentId)
                .header("Origin","*")
                .header(TOKEN_HEADER, token)
                .header(networkTokenHeader, networkToken)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request))
            )
            .andDo(print())
            .andExpect(status().isNotFound())
            .andReturn().getResponse().getContentAsString();
    }
    

    /**
     * Test case for POST /actiontec/api/v6/networks/{networkId}/equipment/{equipmentId}/actions
     * Expected: 503 Service Unavailable
     * @throws Exception
     */
    @Test
    public void testPostNetworkEquipmentActionWithServiceUnavailable() throws Exception {
        final String url = BASE_URL + "/{networkId}/equipment/{equipmentId}/actions";

        EquipmentActionRequest request = new EquipmentActionRequest();
        request.setAction("reboot");
        
        mockMvc.perform(
                post(url, networkId, equipmentId)
                .header("Origin","*")
                .header(TOKEN_HEADER, token)
                .header(networkTokenHeader, networkToken)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request))
            )
            .andDo(print())
            .andExpect(status().isServiceUnavailable())
            .andReturn().getResponse().getContentAsString();
    }
}
