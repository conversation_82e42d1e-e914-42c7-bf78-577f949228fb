package com.actiontec.optim.platform.controller;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.delete;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.patch;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.ResultMatcher;

import com.actiontec.optim.platform.BaseTest;
import com.actiontec.optim.platform.api.v6.dto.EquipmentModelIspConfigDTO;
import com.actiontec.optim.platform.api.v6.dto.EquipmentModelIspConfigRequest;
import com.actiontec.optim.platform.api.v6.dto.IdListDTO;
import com.actiontec.optim.platform.api.v6.dto.PaginationResponse;
import com.actiontec.optim.util.CustomStringUtils;
import com.fasterxml.jackson.core.type.TypeReference;

public class EquipmentModelIspConfigControllerTest extends BaseTest {

    final String TOKEN_HEADER = "X-AUTHORIZATION";

    final String BASE_URL = "/actiontec/api/v6/custom/profiles";
    final String CONFIG_ID_1 = "25550822fa094b5c9a8895399b8b669c";
    final String CONFIG_ID_2 = "c90634c0f51b44bcae558a9c6b14fbf4";

    final String ISP_ID = "3b8b6e29-c306-4206-b363-646b887b0a3d";
    final String MODEL_ID_1 = "a3d7a6ff5db94a828b9decb75ca9230d";
    final String FIRMWARE_ID_1 = "55d4be387b694ceb9e081da7399e0a22";

    final String MODEL_ID_2 = "7ec85a3d1c6143fbafeeadf0f57ae741";
    final String FIRMWARE_ID_2 = "f717a7ad377f402494edc5421fa09b90";

    final String MODEL_ID_FOR_CREATED = "caad369ba1554c9f9e08988c80fc3658";
    final String FIRMWARE_ID_FOR_CREATED = "7f1a6dcab0234f9ca03347d428578424";

    // Non-existing
    final String NOT_EXIST_CONFIG_ID = "1234567890";
    final String NOT_EXIST_ISP_ID = "1234567890";
    final String NOT_EXIST_MODEL_ID = "1234567890";
    final String NOT_EXIST_FIRMWARE_ID = "1234567890";

    @Autowired
    private MockMvc mockMvc;

    /**
     * Test case for GET /actiontec/api/v6/customer/profiles
     * Expected: 200 OK
     * 
     * @throws Exception
     */
    @Test
    public void testGetAllEquipmentModelIspConfigs() throws Exception {
        MvcResult result = mockMvc.perform(
                get(BASE_URL)
                        .header("Origin", "*")
                        .header(TOKEN_HEADER, token))
                .andExpect(status().isOk())
                .andReturn();

        PaginationResponse<EquipmentModelIspConfigDTO> equipmentModelIspConfigResponse = objectMapper
                .readValue(result.getResponse().getContentAsString(),
                        new TypeReference<PaginationResponse<EquipmentModelIspConfigDTO>>() {
                        });

        equipmentModelIspConfigResponse.getData().forEach(System.out::println);
        Assert.assertTrue(equipmentModelIspConfigResponse.getData().size() > 0);
    }

    /**
     * Test case for GET /actiontec/api/v6/customer/profiles/{profileId}
     * Expected: 200 OK
     * 
     * @throws Exception
     */
    @Test
    public void testGetEquipmentModelIspConfigById() throws Exception {
        final String url = BASE_URL + "/{profileId}";

        MvcResult result = mockMvc.perform(
                get(url, CONFIG_ID_1)
                        .header("Origin", "*")
                        .header(TOKEN_HEADER, token))
                .andExpect(status().isOk())
                .andReturn();

        EquipmentModelIspConfigDTO equipmentModelIspConfigResponse = objectMapper
                .readValue(result.getResponse().getContentAsString(), EquipmentModelIspConfigDTO.class);

        System.out.println(equipmentModelIspConfigResponse);
        Assert.assertNotNull(equipmentModelIspConfigResponse);
    }

    /**
     * Test case for GET /actiontec/api/v6/customer/profiles/{profileId}
     * Expected: 404 Not Found
     * 
     * @throws Exception
     */
    @Test
    public void testGetEquipmentModelIspConfigByIdNotFound() throws Exception {
        final String url = BASE_URL + "/{profileId}";

        MvcResult result = mockMvc.perform(
                get(url, NOT_EXIST_CONFIG_ID)
                        .header("Origin", "*")
                        .header(TOKEN_HEADER, token))
                .andExpect(status().isNotFound())
                .andReturn();
    }

    /**
     * Test case for POST /actiontec/api/v6/customer/profiles
     * Expected: 201 Created
     * 
     * @throws Exception
     */
    @Test
    public void testCreateEquipmentModelIspConfig() throws Exception {
        final String url = BASE_URL;

        EquipmentModelIspConfigRequest equipmentModelIspConfigRequest = EquipmentModelIspConfigRequest.builder()
                .ispId(ISP_ID)
                .modelId(MODEL_ID_FOR_CREATED)
                .requiredFirmwareId(FIRMWARE_ID_FOR_CREATED)
                .build();

        List<EquipmentModelIspConfigRequest> equipmentModelIspConfigRequests = new ArrayList<>();
        equipmentModelIspConfigRequests.add(equipmentModelIspConfigRequest);

        MvcResult result = mockMvc.perform(
                post(url)
                        .header("Origin", "*")
                        .header(TOKEN_HEADER, token)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(equipmentModelIspConfigRequests)))
                .andExpect(status().isCreated())
                .andReturn();

        IdListDTO idDTOList = objectMapper
                .readValue(result.getResponse().getContentAsString(), new TypeReference<IdListDTO>() {
                });

        // Clean up
        EquipmentModelIspConfigRequest deleteRequest = EquipmentModelIspConfigRequest.builder()
                .ids(idDTOList.getIds())
                .build();
        testDeleteEquipmentModelIspConfigList(deleteRequest);
    }

    /**
     * Test case for POST /actiontec/api/v6/customer/profiles
     * Expected: 400 Bad Request
     * 
     * @throws Exception
     */
    @Test
    public void testCreateEquipmentModelIspConfigBadRequest() throws Exception {
        final String url = BASE_URL;

        // Case 0: null ispId, null modelId, null firmwareId
        performRequest(url, null, null, null, "POST", status().isBadRequest());

        // Case 1: not exist ispId, modelId, firmwareId
        performRequest(url, NOT_EXIST_ISP_ID, MODEL_ID_1, FIRMWARE_ID_1, "POST", status().isBadRequest());

        // Case 2: ispId, not exist modelId, firmwareId
        performRequest(url, ISP_ID, NOT_EXIST_MODEL_ID, FIRMWARE_ID_1, "POST", status().isBadRequest());

        // Case 3: ispId, modelId, not exist firmwareId
        performRequest(url, ISP_ID, MODEL_ID_1, NOT_EXIST_FIRMWARE_ID, "POST", status().isBadRequest());
    }

    /**
     * Test case for POST /actiontec/api/v6/customer/profiles
     * Expected: 409 Conflict
     * 
     * @throws Exception
     */
    @Test
    public void testPostEquipmentModelIspConfigWithConflict() throws Exception {
        final String url = BASE_URL;

        List<EquipmentModelIspConfigRequest> equipmentModelIspConfigRequests = getEquipmentModelIspConfigRequests(
                ISP_ID, MODEL_ID_1,
                FIRMWARE_ID_1);

        mockMvc.perform(
                post(url)
                        .header("Origin", "*")
                        .header(TOKEN_HEADER, token)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(equipmentModelIspConfigRequests)))
                .andExpect(status().isConflict())
                .andReturn();
    }

    /**
     * Test case for PATCH /actiontec/api/v6/customer/profiles/{profileId}
     * Expected: 200 OK
     * 
     * @throws Exception
     */
    @Test
    public void testUpdateEquipmentModelIspConfig() throws Exception {
        final String url = BASE_URL + "/{profileId}";

        EquipmentModelIspConfigRequest equipmentModelIspConfigRequest = EquipmentModelIspConfigRequest.builder()
                .ispId(ISP_ID)
                .modelId(MODEL_ID_1)
                .requiredFirmwareId(FIRMWARE_ID_1)
                .build();

        mockMvc.perform(
                patch(url, CONFIG_ID_1)
                        .header("Origin", "*")
                        .header(TOKEN_HEADER, token)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(equipmentModelIspConfigRequest)))
                .andExpect(status().isOk())
                .andReturn();
    }

    /**
     * Test case for PATCH /actiontec/api/v6/customer/profiles/{profileId}
     * Expected: 400 Bad Request
     * 
     * @throws Exception
     */
    @Test
    public void testUpdateEquipmentModelIspConfigBadRequest() throws Exception {
        final String url = BASE_URL + "/{profileId}";

        performRequest(url, null, null, null, "PATCH", status().isBadRequest());

        performRequest(url, NOT_EXIST_ISP_ID, MODEL_ID_1, FIRMWARE_ID_1, "PATCH", status().isBadRequest());

        performRequest(url, ISP_ID, NOT_EXIST_MODEL_ID, FIRMWARE_ID_1, "PATCH", status().isBadRequest());

        performRequest(url, ISP_ID, MODEL_ID_1, NOT_EXIST_FIRMWARE_ID, "PATCH", status().isBadRequest());
    }

    /**
     * Test case for PATCH /actiontec/api/v6/customer/profiles/{profileId}
     * Expected: 404 Not Found
     * 
     * @throws Exception
     */
    @Test
    public void testUpdateEquipmentModelIspConfigNotFound() throws Exception {
        final String url = BASE_URL + "/{profileId}";

        mockMvc.perform(
                patch(url, NOT_EXIST_CONFIG_ID)
                        .header("Origin", "*")
                        .header(TOKEN_HEADER, token)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(EquipmentModelIspConfigRequest.builder()
                                .ispId(ISP_ID)
                                .modelId(MODEL_ID_1)
                                .requiredFirmwareId(FIRMWARE_ID_1)
                                .build())))
                .andExpect(status().isNotFound())
                .andReturn();
    }

    /**
     * Test case for PATCH /actiontec/api/v6/customer/profiles/{profileId}
     * Expected: 409 Conflict
     * 
     * @throws Exception
     */
    @Test
    public void testUpdateEquipmentModelIspConfigWithConflict() throws Exception {
        final String url = BASE_URL + "/{profileId}";

        mockMvc.perform(
                patch(url, CONFIG_ID_2)
                        .header("Origin", "*")
                        .header(TOKEN_HEADER, token)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(EquipmentModelIspConfigRequest.builder()
                                .ispId(ISP_ID)
                                .modelId(MODEL_ID_1)
                                .requiredFirmwareId(FIRMWARE_ID_1)
                                .build())))
                .andExpect(status().isConflict())
                .andReturn();
    }

    /**
     * Test case for Batch PATCH /actiontec/api/v6/customer/profiles
     * Expected: 400 Bad Request
     * 
     * @throws Exception
     */
    @Test
    public void testUpdateCustomerProfileListBadRequest() throws Exception {
        final String url = BASE_URL;

        List<EquipmentModelIspConfigRequest> equipmentModelIspConfigRequests = new ArrayList<>();
        equipmentModelIspConfigRequests.add(EquipmentModelIspConfigRequest.builder()
                .id(CustomStringUtils.EMPTY)
                .ispId(ISP_ID)
                .modelId(MODEL_ID_1)
                .requiredFirmwareId(FIRMWARE_ID_1)
                .build());

        equipmentModelIspConfigRequests.add(EquipmentModelIspConfigRequest.builder()
                .id(CONFIG_ID_2)
                .ispId(ISP_ID)
                .modelId(MODEL_ID_2)
                .requiredFirmwareId(FIRMWARE_ID_2)
                .build());

        mockMvc.perform(
                patch(url)
                        .header("Origin", "*")
                        .header(TOKEN_HEADER, token)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(equipmentModelIspConfigRequests)))
                .andExpect(status().isBadRequest())
                .andReturn();
    }

    /**
     * Test case for Batch PATCH /actiontec/api/v6/customer/profiles
     * Expected: 404 Not Found
     * 
     * @throws Exception
     */

    public void testUpdateCustomerProfileListNotFound() throws Exception {

        List<EquipmentModelIspConfigRequest> equipmentModelIspConfigRequests = new ArrayList<>();
        equipmentModelIspConfigRequests.add(EquipmentModelIspConfigRequest.builder()
                .id(NOT_EXIST_CONFIG_ID)
                .ispId(ISP_ID)
                .modelId(MODEL_ID_1)
                .requiredFirmwareId(FIRMWARE_ID_1)
                .build());

        equipmentModelIspConfigRequests.add(EquipmentModelIspConfigRequest.builder()
                .id(CONFIG_ID_2)
                .ispId(ISP_ID)
                .modelId(MODEL_ID_2)
                .requiredFirmwareId(FIRMWARE_ID_2)
                .build());

        mockMvc.perform(
                patch(BASE_URL)
                        .header("Origin", "*")
                        .header(TOKEN_HEADER, token)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(equipmentModelIspConfigRequests)))
                .andExpect(status().isNotFound())
                .andReturn();

    }

    /**
     * Test case for Batch PATCH /actiontec/api/v6/customer/profiles
     * Expected: 409 Conflict
     * 
     * @throws Exception
     */
    public void testUpdateCustomerProfileWithConflict() throws Exception {

        List<EquipmentModelIspConfigRequest> equipmentModelIspConfigRequests = new ArrayList<>();
        equipmentModelIspConfigRequests.add(EquipmentModelIspConfigRequest.builder()
                .id(CONFIG_ID_2)
                .ispId(ISP_ID)
                .modelId(MODEL_ID_1)
                .requiredFirmwareId(FIRMWARE_ID_1)
                .build());

        mockMvc.perform(
                patch(BASE_URL)
                        .header("Origin", "*")
                        .header(TOKEN_HEADER, token)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(equipmentModelIspConfigRequests)))
                .andExpect(status().isConflict())
                .andReturn();
    }

    /**
     * Test case for DELETE /actiontec/api/v6/customer/profiles/{profileId}
     * Expected: 200 OK
     * 
     * @throws Exception
     */
    // @Test
    public void testDeleteEquipmentModelIspConfig() throws Exception {
        final String url = BASE_URL + "/{profileId}";

        mockMvc.perform(
                delete(url, CONFIG_ID_1)
                        .header("Origin", "*")
                        .header(TOKEN_HEADER, token))
                .andExpect(status().isNoContent())
                .andReturn();
    }

    /**
     * Test case for DELETE /actiontec/api/v6/customer/profiles
     * Expected: 200 OK
     * 
     * @throws Exception
     */
    public void testDeleteEquipmentModelIspConfigList(EquipmentModelIspConfigRequest request) throws Exception {
        final String url = BASE_URL;

        mockMvc.perform(
                delete(url)
                        .header("Origin", "*")
                        .header(TOKEN_HEADER, token)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isNoContent())
                .andReturn();
    }

    /**
     * Test case for DELETE /actiontec/api/v6/customer/profiles
     * Expected: 400 Bad Request
     * 
     * @throws Exception
     */
    @Test
    public void testDeleteEquipmentModelIspConfigListBadRequest() throws Exception {
        final String url = BASE_URL;

        EquipmentModelIspConfigRequest equipmentModelIspConfigRequest = EquipmentModelIspConfigRequest.builder()
                .ids(Arrays.asList("", ""))
                .build();

        mockMvc.perform(
                delete(url)
                        .header("Origin", "*")
                        .header(TOKEN_HEADER, token)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(equipmentModelIspConfigRequest)))
                .andExpect(status().isBadRequest())
                .andReturn();
    }

    private void performRequest(String url, String ispId, String modelId, String firmwareId,
            String httpMethod, ResultMatcher expectedStatus) throws Exception {
        List<EquipmentModelIspConfigRequest> equipmentModelIspConfigRequests = getEquipmentModelIspConfigRequests(ispId,
                modelId,
                firmwareId);

        switch (httpMethod) {
            case "POST":
                mockMvc.perform(
                        post(url)
                                .header("Origin", "*")
                                .header(TOKEN_HEADER, token)
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(equipmentModelIspConfigRequests)))
                        .andExpect(expectedStatus)
                        .andReturn();
                break;
            case "PATCH":
                mockMvc.perform(
                        patch(url, CONFIG_ID_1)
                                .header("Origin", "*")
                                .header(TOKEN_HEADER, token)
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(equipmentModelIspConfigRequests.get(0))))
                        .andExpect(expectedStatus)
                        .andReturn();
                break;
            default:
                break;
        }
    }

    private List<EquipmentModelIspConfigRequest> getEquipmentModelIspConfigRequests(
            String ispId, String modelId, String firmwareId) {
        return Arrays.asList(
                EquipmentModelIspConfigRequest.builder()
                        .ispId(ispId)
                        .modelId(modelId)
                        .requiredFirmwareId(firmwareId)
                        .build());
    }

}
