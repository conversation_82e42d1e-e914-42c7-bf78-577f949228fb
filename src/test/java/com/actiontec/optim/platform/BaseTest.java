package com.actiontec.optim.platform;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

@SpringBootTest(
    classes = com.incs83.app.main.Launcher.class,
    webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT
)
@RunWith(SpringRunner.class)
@ActiveProfiles("dev")
public class BaseTest {

    /**
     * 基础测试方法 - 验证Spring上下文是否正常加载
     */
    @Test
    public void contextLoads() {
        System.out.println("Spring Boot测试上下文加载成功");
    }
    
}
