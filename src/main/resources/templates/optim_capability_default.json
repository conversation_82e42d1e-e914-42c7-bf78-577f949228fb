{"optim": {"networkCapabilities": {"serviceWiFi": {"numWiFiServices": 8}}, "equipmentInfo": {"productClass": "OPTIMHOME", "modelNumber": "950-007", "modelName": "WF-950-AX", "serialNumber": "ACT250987654321XY", "hardwareVersion": "3.1.2", "manufacturer": "Actiontec Electronics, Inc", "manufacturerOUI": "001A2B"}, "countryCode": "US", "equipmentCapabilities": {"memoryInfo": {"dram": 4096, "dramReservedForLCM": 512, "flash": 1024, "flashReservedForLCM": 128}, "logicalInterfaces": {"lan0": {"labelName": "WAN", "linuxDev": "eth0", "type": "Ethernet", "supportedMTU": 9000, "defaultMTU": 1500, "maxBitRateTx": 25000000000, "maxBitRateRx": 25000000000, "macAddress": "00:1A:2B:3C:4D:5E", "physical": {"poePD": false}}, "lan1": {"labelName": "LAN1/POE+", "linuxDev": "eth1", "type": "Ethernet", "supportedMTU": 1600, "defaultMTU": 1500, "maxBitRateTx": 2500000000, "maxBitRateRx": 2500000000, "macAddress": "00:1A:2B:3C:4D:5F", "physical": {"poePD": true}}, "lan2": {"labelName": "LAN2", "linuxDev": "eth2", "type": "Ethernet", "supportedMTU": 1500, "defaultMTU": 1500, "maxBitRateTx": 1000000000, "maxBitRateRx": 1000000000, "macAddress": "00:1A:2B:3C:4D:60", "physical": {"poePD": false}}, "lan3": {"labelName": "LAN3", "linuxDev": "eth3", "type": "Ethernet", "supportedMTU": 1500, "defaultMTU": 1500, "maxBitRateTx": 1000000000, "maxBitRateRx": 1000000000, "macAddress": "00:1A:2B:3C:4D:61", "physical": {"poePD": false}}, "moca0": {"labelName": "COAX", "linuxDev": "moca0", "type": "MoCA", "supportedMTU": 1500, "defaultMTU": 1500, "maxBitRateTx": 3500000000, "maxBitRateRx": 3500000000, "macAddress": "00:1A:2B:3C:4D:62", "physical": {"mocaVersion": "3.1", "band": "E"}}, "cellular0": {"labelName": "5G/LTE", "linuxDev": "wwan0", "type": "Cellular", "supportedMTU": 1500, "defaultMTU": 1500, "maxBitRateTx": 2000000000, "maxBitRateRx": 5000000000, "macAddress": "00:1A:2B:3C:4D:63", "physical": {"supportedBands": "n1,n3,n7,n28,n41,n77,n78,n79"}}}, "radios": {"2g": {"supportedBands": "2.4GHz", "supportedStandard": "be", "possibleChannels": {"2_4GHz": {"20MHz": "1,2,3,4,5,6,7,8,9,10,11,12,13", "40MHz": "1,2,3,4,5,6,7,8,9"}}, "maxSupportedAssociations": 256, "deployment": "indoor", "nssTx": 4, "nssRx": 4, "maxTxPwr": 20, "supportedSecurity": ["WPA2", "WPA2-PSK-WPA3-SAE"], "supportMLO": true}, "5gl": {"supportedBands": "5GHz", "supportedStandard": "be", "possibleChannels": {"5GHz": {"20MHz": "36,40,44,48,52,56,60,64", "40MHz": "36,44,52,60", "80MHz": "36,52", "160MHz": "36"}}, "maxSupportedAssociations": 256, "deployment": "indoor", "nssTx": 4, "nssRx": 4, "maxTxPwr": 23, "supportedSecurity": ["WPA2", "WPA2-PSK-WPA3-SAE"], "supportMLO": true}, "5gh": {"supportedBands": "5GHz", "supportedStandard": "be", "possibleChannels": {"5GHz": {"20MHz": "100,104,108,112,116,120,124,128,132,136,140,144,149,153,157,161,165", "40MHz": "100,108,116,124,132,140,149,157", "80MHz": "100,116,132,149", "160MHz": "100"}}, "maxSupportedAssociations": 256, "deployment": "indoor", "nssTx": 4, "nssRx": 4, "maxTxPwr": 23, "supportedSecurity": ["WPA2", "WPA2-PSK-WPA3-SAE"], "supportMLO": true}, "6g": {"supportedBands": "6GHz", "supportedStandard": "be", "possibleChannels": {"6GHz": {"20MHz": "1,5,9,13,17,21,25,29,33,37,41,45,49,53,57,61,65,69,73,77,81,85,89,93,97,101,105,109,113,117,121,125,129,133,137,141,145,149,153,157,161,165,169,173,177,181,185,189,193,197,201,205,209,213,217,221,225,229,233", "40MHz": "1,9,17,25,33,41,49,57,65,73,81,89,97,105,113,121,129,137,145,153,161,169,177,185,193,201,209,217,225,233", "80MHz": "1,17,33,49,65,81,97,113,129,145,161,177,193,209,225", "160MHz": "1,33,65,97,129,161,193,225", "320MHz": "1,65,129,193"}}, "maxSupportedAssociations": 512, "6GHzPwrClass": "LPI", "deployment": "indoor", "nssTx": 8, "nssRx": 8, "maxTxPwr": 14, "supportedSecurity": ["WPA3-SAE"], "supportMLO": true}}}}}