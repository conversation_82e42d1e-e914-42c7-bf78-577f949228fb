{"strict": false, "optim": {"network": {"system": {"password": "admin1234", "timezone": "EST5EDT,M3.2.0/2,M11.1.0/2"}, "serviceWiFi": {"primary": {"separatedSsid": false, "isolated": false, "mlo": {"enabled": true}, "radios": {"2.4G": {"enabled": true, "name": "MyHomeNetwork", "securityMode": "WPA2-PSK-WPA3-SAE", "passphrase": {"value": "SecurePassword123!", "encryptionType": "None", "nonce": "abc123"}}, "5G": {"enabled": true, "name": "MyHomeNetwork", "securityMode": "WPA2-PSK-WPA3-SAE", "passphrase": {"value": "SecurePassword123!", "encryptionType": "None", "nonce": "def456"}}, "6G": {"enabled": true, "name": "MyHomeNetwork", "securityMode": "WPA3-SAE", "passphrase": {"value": "SecurePassword123!", "encryptionType": "None", "nonce": "ghi789"}}}}, "guest": {"separatedSsid": false, "isolated": true, "mlo": {"enabled": false}, "radios": {"2.4G": {"enabled": true, "name": "GuestNetwork", "securityMode": "WPA2-PSK-WPA3-SAE", "passphrase": {"value": "GuestPassword123!", "encryptionType": "None", "nonce": "jkl012"}}, "5G": {"enabled": true, "name": "GuestNetwork", "securityMode": "WPA2-PSK-WPA3-SAE", "passphrase": {"value": "GuestPassword123!", "encryptionType": "None", "nonce": "mno345"}}, "6G": {"enabled": true, "name": "GuestNetwork", "securityMode": "WPA3-SAE", "passphrase": {"value": "GuestPassword123!", "encryptionType": "None", "nonce": "pqr678"}}}}, "smartHome": {"separatedSsid": false, "isolated": true, "mlo": {"enabled": false}, "radios": {"2.4G": {"enabled": true, "name": "SmartHomeNetwork", "securityMode": "WPA2", "passphrase": {"value": "SmartHomePassword123!", "encryptionType": "None", "nonce": "stu901"}}, "5G": {"enabled": true, "name": "SmartHomeNetwork", "securityMode": "WPA2-PSK-WPA3-SAE", "passphrase": {"value": "SmartHomePassword123!", "encryptionType": "None", "nonce": "vwx234"}}, "6G": {"enabled": true, "name": "SmartHomeNetwork", "securityMode": "WPA3-SAE", "passphrase": {"value": "SmartHomePassword123!", "encryptionType": "None", "nonce": "yzab567"}}}}}, "serviceInet": {"default": {"wan": {"default": {"interfaceSelection": "Auto", "interfaceCandidates": "eth0", "detectionMethod": "DHCP", "metric": 100}}, "mode": "routing", "serviceIPv4": "default"}}, "serviceIPv4": {"default": {"enabled": true, "addressingType": "Dynamic", "routing": "default"}}, "serviceDHCPServer4": {"default": {"enabled": true, "pools": {"lan": {"enabled": true, "minAddress": "*************", "maxAddress": "*************", "subnetMask": "*************", "dnsServers": "*******,*******", "leaseTime": 86400}}}}, "serviceRouting": {"default": {"enabled": true}}}, "equipment": {"SELF": {"hostName": "home-gateway", "interfaces": {"lan0": {"enabled": true, "physical": {"duplexMode": "Auto", "eeeEnabled": true, "flowControl": "auto"}}}, "radios": {"2g": {"enabled": true, "bandwidth": "40MHz", "autoChannelSelectionEnabled": true, "transmitPower": 100, "maximumClients": 32}, "5g": {"enabled": true, "bandwidth": "80MHz", "autoChannelSelectionEnabled": true, "transmitPower": 80, "maximumClients": 64}}}}}, "ucentral": {}, "tr-181": {"ops": {}}}