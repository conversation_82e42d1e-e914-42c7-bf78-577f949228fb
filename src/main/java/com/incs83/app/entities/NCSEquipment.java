package com.incs83.app.entities;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;

@Entity
@Table(name = "ncs_equipment")
public class NCSEquipment {

    @Id
    private String id;

    @NotNull
    @Column(name = "createdAt")
    private Date createdAt;

    @NotNull
    @Column(name = "createdBy")
    private String createdBy;

    @Column(name = "updatedAt")
    private Date updatedAt;

    @Column(name = "updatedBy")
    private String updatedBy;

    @Column(name = "downLinkRate")
    private Double downLinkRate;

    @Column(name = "friendlyName")
    private String friendlyName;

    @NotNull
    @Column(name = "model_name")
    private String modelName;

    @Column(name = "product_id")
    private String productId;

    @Column(name = "groupId")
    private String groupId;

    @Column(name = "mac")
    private String mac;

    @Column(name = "serial")
    private String serial;

    @Column(name = "serviceTelephoneNo")
    private String serviceTelephoneNo;

    @Column(name = "upLinkRate")
    private Double upLinkRate;

    @Column(name = "subscriber_id")
    private String subscriberId;

    @Column(name = "dpp_key")
    private String dppKey;

    @NotNull
    @Column(name = "in_use")
    private boolean inUse = false;

    @NotNull
    @Column(name = "isp_id")
    private String ispId;

    @Column(name = "network_id")
    private String networkId;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public @NotNull Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(@NotNull Date createdAt) {
        this.createdAt = createdAt;
    }

    public @NotNull String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(@NotNull String createdBy) {
        this.createdBy = createdBy;
    }

    public Date getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public Double getDownLinkRate() {
        return downLinkRate;
    }

    public void setDownLinkRate(Double downLinkRate) {
        this.downLinkRate = downLinkRate;
    }

    public String getFriendlyName() {
        return friendlyName;
    }

    public void setFriendlyName(String friendlyName) {
        this.friendlyName = friendlyName;
    }

    public @NotNull String getModelName() {
        return modelName;
    }

    public void setModelName(@NotNull String modelName) {
        this.modelName = modelName;
    }

    public String getProductId() {
        return productId;
    }

    public void setProductId(String productId) {
        this.productId = productId;
    }

    public String getGroupId() {
        return groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }

    public String getMac() {
        return mac;
    }

    public void setMac(String mac) {
        this.mac = mac;
    }

    public String getSerial() {
        return serial;
    }

    public void setSerial(String serial) {
        this.serial = serial;
    }

    public String getServiceTelephoneNo() {
        return serviceTelephoneNo;
    }

    public void setServiceTelephoneNo(String serviceTelephoneNo) {
        this.serviceTelephoneNo = serviceTelephoneNo;
    }

    public Double getUpLinkRate() {
        return upLinkRate;
    }

    public void setUpLinkRate(Double upLinkRate) {
        this.upLinkRate = upLinkRate;
    }

    public String getSubscriberId() {
        return subscriberId;
    }

    public void setSubscriberId(String subscriberId) {
        this.subscriberId = subscriberId;
    }

    public @NotNull boolean isInUse() {
        return inUse;
    }

    public void setInUse(@NotNull boolean inUse) {
        this.inUse = inUse;
    }

    public @NotNull String getIspId() {
        return ispId;
    }

    public void setIspId(@NotNull String ispId) {
        this.ispId = ispId;
    }

    public String getNetworkId() {
        return networkId;
    }

    public void setNetworkId(String networkId) {
        this.networkId = networkId;
    }

    public String getDppKey() {
        return dppKey;
    }

    public void setDppKey(String dppKey) {
        this.dppKey = dppKey;
    }
}
