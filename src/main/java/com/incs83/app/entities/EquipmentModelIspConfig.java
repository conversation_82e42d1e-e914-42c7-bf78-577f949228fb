package com.incs83.app.entities;

import java.sql.Timestamp;
import java.time.LocalDateTime;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "equipment_model_isp_config", schema = "actiontec")
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@Builder
public class EquipmentModelIspConfig {

    @Id
    @Column(name = "id", length = 255)
    private String id;

    @Column(name = "equipment_model_id", length = 255, nullable = false)
    private String equipmentModelId;

    @Column(name = "isp_id", length = 255, nullable = false)
    private String ispId;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "equipment_model_id", insertable = false, updatable = false)
    private EquipmentModel equipmentModel;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "min_version_firmware_id")
    private Firmware minVersionFirmware;

    @Column(name = "created_at", updatable = false)
    private Timestamp createdAt;

    @Column(name = "created_by", length = 255, nullable = false, updatable = false)
    private String createdBy;

    @Column(name = "updated_at")
    private Timestamp updatedAt;

    @Column(name = "updated_by", length = 255, nullable = false, updatable = true)
    private String updatedBy;
}
