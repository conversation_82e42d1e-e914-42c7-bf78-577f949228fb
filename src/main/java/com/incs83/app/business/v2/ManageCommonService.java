package com.incs83.app.business.v2;

import com.actiontec.optim.mongodb.dto.ApDetailDto;
import com.actiontec.optim.mongodb.dto.HostDto;
import com.actiontec.optim.platform.constant.ActiontecSQL;
import com.actiontec.optim.platform.service.NCSIspService;
import com.actiontec.optim.util.MacOuiUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.incs83.abstraction.ApiResponseCode;
import com.incs83.app.constants.misc.ActiontecConstants;
import com.incs83.app.constants.misc.ApplicationConstants;
import com.incs83.app.constants.queries.ClusterInfoSQL;
import com.incs83.app.constants.queries.EquipmentSQL;
import com.incs83.app.constants.queries.GroupISPSQL;
import com.incs83.app.constants.queries.SubscriberSQL;
import com.incs83.app.entities.*;
import com.incs83.app.entities.cassandra.*;
import com.incs83.app.enums.APDetailLookType;
import com.incs83.app.responsedto.v2.Device.DeviceListDTO;
import com.incs83.app.responsedto.v2.isp.ISPDTOList;
import com.incs83.app.responsedto.v2.isp.ISPModel;
import com.incs83.app.service.data.MongoServiceImpl;
import com.incs83.config.ESClientConfig;
import com.incs83.constants.ApplicationCommonConstants;
import com.incs83.context.ExecutionContext;
import com.incs83.exceptions.ApiException;
import com.incs83.exceptions.handler.AuthEntityNotAllowedException;
import com.incs83.exceptions.handler.UrlNotFoundException;
import com.incs83.exceptions.handler.ValidationException;
import com.incs83.mt.DataAccessService;
import com.incs83.mt.MongoTenantTemplate;
import com.incs83.pubsub.KafkaPublisher;
import com.incs83.security.tokenFactory.UserContext;
import com.incs83.service.CommonService;
import com.incs83.service.S3Service;
import com.incs83.services.HazelcastService;
import com.incs83.util.CommonUtils;
import com.mongodb.BasicDBObject;
import com.mongodb.DBCollection;
import com.mongodb.DBCursor;
import com.mongodb.DBObject;
import com.mongodb.util.JSON;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.eclipse.paho.client.mqttv3.MqttClient;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.net.URL;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.incs83.Constants.ESConstants.EXT_MAC;
import static com.incs83.Constants.ESConstants.EXT_SERIAL;
import static com.incs83.app.constants.misc.ActiontecConstants.AP_DETAIL;
import static com.incs83.app.constants.misc.ActiontecConstants.INTERNET_SERVICE_PROVIDER;
import static com.incs83.app.constants.misc.ActiontecConstants.*;
import static com.incs83.app.constants.misc.ApplicationConstants.EMPTY_STRING;
import static com.incs83.app.constants.misc.ApplicationConstants.EXTENDER;
import static com.incs83.app.constants.misc.ApplicationConstants.GATEWAY;
import static com.incs83.app.constants.misc.ApplicationConstants.ISP;
import static com.incs83.app.constants.misc.ApplicationConstants.ISP_KEY;
import static com.incs83.app.constants.misc.ApplicationConstants.TIMESTAMP;
import static com.incs83.app.constants.misc.ApplicationConstants.TWO_DECIMAL_PLACE;
import static com.incs83.app.constants.misc.ApplicationConstants.ZERO;
import static com.incs83.app.constants.misc.ApplicationConstants.*;
import static com.incs83.constants.ApplicationCommonConstants.COMMA;
import static com.incs83.constants.ApplicationCommonConstants.DEFAULT;
import static com.incs83.constants.ApplicationCommonConstants.*;

@Service
public class ManageCommonService {

    private static final Logger LOG = LogManager.getLogger("org");

    @Autowired
    private ESClientConfig esClientConfig;

    @Autowired
    private DataAccessService dataAccessService;

    @Autowired
    private KafkaPublisher kafkaPublisher;

    /*@Autowired
    private ManageSubscriberService manageSubscriberService;*/

    @Autowired
    private MongoTenantTemplate mongoTemplate;
    @Autowired
    private MongoServiceImpl mongoService;

    @Autowired
    private HazelcastService hazelcastService;

    @Autowired
    private CommonService commonService;

    @Autowired
    private HazelcastService cacheService;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private S3Service s3Service;

    @Autowired
    NCSIspService ispService;

    @Value("${mesh.enable:false}")
    private Boolean meshEnable;

    private  enum CapbilityFields {
        dualBandSupported,
        dfsSupported,
        ieee80211kSupported,
        ieee80211vSupported,
        ieee80211rSupported
    }

    public static String encrypt() {
        String strData = "----------";

        return strData;
    }

    public void isDurationValid(Long duration) throws Exception {
        if (duration < 0)
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "duration cannot be negative value " + duration);
    }

    public String getConnectivityStatusForEquipment(DBObject equipment) {
        String status = "GREEN";
        if (equipment != null) {
            HashMap<String, String> commonProps = commonService.read(COMMON_CONFIG);
            long equipmentOfflineTime = 7L;
            try {
                equipmentOfflineTime = Long.valueOf(commonProps.get(COMMON_DEVICE_STATUS_RETENTION_TIME));
            } catch (Exception e) {

            }
            if (isHookEnabled()) {
                HashMap<String, Object> query = new HashMap<>();
                query.put("userId", equipment.get("userId"));
                BasicDBObject projection = new BasicDBObject();
                projection.put("status", 1);
                List<BasicDBObject> vmqMetric = mongoService.findListByTimestamp(query, "vmqMetrics", "timestamp", equipmentOfflineTime, -1, projection);
                if (Objects.nonNull(vmqMetric) && vmqMetric.size() > 0) {
                    if (String.valueOf(vmqMetric.get(0).get("status")).equalsIgnoreCase("connected")) {
                        return "GREEN";
                    } else {
                        return "RED";
                    }
                }
            }

            String connectionStatus = String.valueOf(equipment.get("connectionStatus"));
            if (StringUtils.equalsIgnoreCase("offline", connectionStatus)) {
                return "RED";
            }

            long currentTime = Calendar.getInstance().getTimeInMillis();
            long lastMetricReceived = Long.valueOf(String.valueOf(("v3".equalsIgnoreCase(String.valueOf(equipment.get("etlVersion"))) || Objects.isNull(equipment.get("sendingTime"))) ? equipment.get("timestamp") : equipment.get("sendingTime")));
            long timeDiff = currentTime - lastMetricReceived;


            if (timeDiff > (equipmentOfflineTime * 60 * 1000)) {
                status = "RED";
            } else {
                // According to the following logical, the status is always GREEN
                List<BasicDBObject> wifiRadios = (List<BasicDBObject>) equipment.get("wifiRadios");
                int radioEnableCount = 0;
                for(Object wifiRadio : wifiRadios) {
                    int enabled = Integer.valueOf(((BasicDBObject) wifiRadio).get("enable").toString());
                    if(enabled == 1) {
                        radioEnableCount++;
                    }
                }
                if (wifiRadios.size() == radioEnableCount) {
                    status = "GREEN";
                }
            }
        }
        return status;
    }

    public Boolean isHookEnabled() {
        Boolean isHookEnable = false;
        HashMap<String, String> equipmentConfig = commonService.read(EQUIPMENT_CONFIG);
        try {
            isHookEnable = Boolean.valueOf(equipmentConfig.get(REALTIME_CONNECTIVITY_STATUS));
        } catch (Exception e) {

        }

        return isHookEnable;
    }

    public String getConnectivityStatusForDevice(DBObject device) {
        String optStandB = "802.11 b";
        String optStandBG = "802.11 bg";
        String optStandA = "802.11 a";
        String colour = "GREEN";

        HashMap<String, String> deviceProps = commonService.read(DEVICE_CONFIG);

        Double rssiRange = -75.0;
        try {
            rssiRange = Double.valueOf(deviceProps.get(DEVICE_RSSI_LIMIT));
        } catch (Exception e) {
            LOG.error("failed to rssi range", e);
        }

        if (getCurrentDevStatus(device)) {
            colour = "GREY";
        } else {
            Double rssi = Objects.nonNull(device.get("rssi")) ? Double.valueOf(device.get("rssi").toString()) : 0.0;
            String wifiMode = (String) device.get("wifiMode");
            if (rssi < rssiRange || optStandB.equals(wifiMode) || optStandBG.equals(wifiMode) || optStandA.equals(wifiMode))
                colour = "YELLOW";
        }

        return colour;
    }

    public boolean getCurrentDevStatus(DBObject device) {
        String connectionStatus = String.valueOf(device.get("connectionStatus"));
        if (StringUtils.equalsIgnoreCase("offline", connectionStatus)) {
            return true;
        }

        boolean status = false;
        HashMap<String, Object> queryParam = new HashMap<>();
        queryParam.put("userId", device.get("userId"));
        queryParam.put("serialNumber", device.get("serialNumber"));

        BasicDBObject fieldsToRemove = new BasicDBObject();
        fieldsToRemove.put("timestamp", ONE);
        fieldsToRemove.put("sendingTime", ONE);
        fieldsToRemove.put("etlVersion", ONE);
        fieldsToRemove.put("ntInfoTimestamp", ONE);

        if (Objects.nonNull(device.get("timestamp"))) {
            DBObject apDetailTimestamp = mongoService.findOne(queryParam, new HashMap<>(), AP_DETAIL, TIMESTAMP, DESC, fieldsToRemove);
            if (Objects.nonNull(apDetailTimestamp)) {

                HashMap<String, String> commonProps = commonService.read(COMMON_CONFIG);
                int equipmentOfflineTime = 7;
                try {
                    equipmentOfflineTime = Integer.valueOf(commonProps.get(COMMON_DEVICE_STATUS_RETENTION_TIME));
                } catch (Exception e) {
                    LOG.error("failed to equipment offline time", e);
                }

                long currentMillis = System.currentTimeMillis();
                long onlineBaseMillis = currentMillis - equipmentOfflineTime * 60000L;
                long lastMetricReceived = apDetailTimestamp.get("ntInfoTimestamp") != null ? Long.parseLong(apDetailTimestamp.get("ntInfoTimestamp").toString()) : 0L;

                if (lastMetricReceived < onlineBaseMillis) {
                    DBObject dataToUpdate = new BasicDBObject();
                    BasicDBObject dataToSet = new BasicDBObject();
                    dataToSet.put("connectionStatus", "offline");
                    dataToSet.put("sendingTime", lastMetricReceived - ((equipmentOfflineTime + 10) * 60 * 1000L));
                    dataToUpdate.put("$set", dataToSet);

                    BasicDBObject query = new BasicDBObject();
                    query.put("userId", device.get("userId"));
                    query.put("serialNumber", device.get("serialNumber"));
                    mongoService.update(query, dataToUpdate, false, false, AP_DETAIL);

                    status = true;
                }
            }
        }
        return status;
    }

    public HashMap<String, String> getHostNameForStation(Set<String> macAddress, Equipment userEquipment) {
        HashMap<String, Object> query = new HashMap<>();
        HashMap<String, Object> inParams = new HashMap<>();
        HashMap<String, Object> inClause = new HashMap<>();
        inClause.put("resourceName", "macAddress");
        inClause.put("resource", macAddress);
        inParams.put("in", inClause);

        query.put("userId", userEquipment.getRgwSerial());

        BasicDBObject mongoFieldOptions = new BasicDBObject();
        mongoFieldOptions.put("macAddress", 1);
        mongoFieldOptions.put("hostname", 1);
        mongoFieldOptions.put("userId", 1);

        List<BasicDBObject> deviceDetailList = mongoService.findList(query, inParams, ActiontecConstants.STATION_DETAIL, mongoFieldOptions);

        HashMap<String, String> devicesHostname = new HashMap<>();
        deviceDetailList.forEach(item -> {
            devicesHostname.put(String.valueOf(item.get("macAddress")), getHostNameByPriorityForDevice(item));
        });
        return devicesHostname;
    }

    public List getMacAddressFromUserId(String userId) {
        HashMap<String, Object> query = new HashMap<>();
        HashMap<String, Object> inParams = new HashMap<>();
        List<String> macAddressList = new ArrayList<>();
        query.put("userId", userId);

        BasicDBObject mongoFieldOptions = new BasicDBObject();
        mongoFieldOptions.put("macAddress", 1);

        List<BasicDBObject> macAddress = mongoService.findList(query, inParams, ActiontecConstants.STATION_DETAIL, mongoFieldOptions);
        macAddress.forEach(item -> {
            macAddressList.add(item.get("macAddress").toString());
        });
        return macAddressList;
    }

    public String getDisplayNameForEthMoca(String macAddress, Equipment userEquipment) {
        Set<String> macAddressSet = new HashSet<>();
        macAddressSet.add(macAddress);
        HashMap<String, Object> inParams = new HashMap<>();
        HashMap<String, Object> inClause = new HashMap<>();
        inClause.put("resourceName", "macAddress");
        inClause.put("resource", macAddressSet);
        inParams.put("in", inClause);

        HashMap<String, Object> query = new HashMap<>();
        query.put("userId", userEquipment.getRgwSerial());

        BasicDBObject mongoFieldOptions = new BasicDBObject();
        mongoFieldOptions.put("macAddress", 1);
        mongoFieldOptions.put("friendlyName", 1);
        mongoFieldOptions.put("timestamp", 1);
        mongoFieldOptions.put("hostname", 1);
        mongoFieldOptions.put("userId", 1);

        List<BasicDBObject> deviceDetailList = mongoService.findList(query, inParams, HOSTNAME_DETAIL, mongoFieldOptions);
        deviceDetailList.sort(Comparator.comparing(s -> ((Long) s.get("timestamp"))));
        Collections.reverse(deviceDetailList);
        if (Objects.nonNull(deviceDetailList) && !deviceDetailList.isEmpty()) {
            return getDisplayNameByPriorityForDevice(deviceDetailList.get(0));
        }
        return macAddress;
    }

    public String getDisplayNameForStation(String macAddress, Equipment userEquipment) {
        Set<String> macAddressSet = new HashSet<>();
        macAddressSet.add(macAddress);
        HashMap<String, Object> inParams = new HashMap<>();
        HashMap<String, Object> inClause = new HashMap<>();
        inClause.put("resourceName", "macAddress");
        inClause.put("resource", macAddressSet);
        inParams.put("in", inClause);

        HashMap<String, Object> query = new HashMap<>();
        query.put("userId", userEquipment.getRgwSerial());

        BasicDBObject mongoFieldOptions = new BasicDBObject();
        mongoFieldOptions.put("macAddress", 1);
        mongoFieldOptions.put("friendlyName", 1);
        mongoFieldOptions.put("timestamp", 1);
        mongoFieldOptions.put("vendor", 1);
        mongoFieldOptions.put("hostname", 1);
        mongoFieldOptions.put("userId", 1);

        List<BasicDBObject> deviceDetailList = mongoService.findList(query, inParams, ActiontecConstants.STATION_DETAIL, mongoFieldOptions);
        deviceDetailList.sort(Comparator.comparing(s -> ((Long) s.get("timestamp"))));
        Collections.reverse(deviceDetailList);
        if (Objects.nonNull(deviceDetailList) && !deviceDetailList.isEmpty()) {
            return getDisplayNameByPriorityForDevice(deviceDetailList.get(0));
        }
        return macAddress;
    }

    public String getHostNameByGateway(String macAddress, Equipment userEquipment) {
        Set<String> macAddressSet = new HashSet<>();
        macAddressSet.add(macAddress);
        HashMap<String, Object> inParams = new HashMap<>();
        HashMap<String, Object> inClause = new HashMap<>();
        inClause.put("resourceName", "macAddress");
        inClause.put("resource", macAddressSet);
        inParams.put("in", inClause);

        HashMap<String, Object> query = new HashMap<>();
        query.put("userId", userEquipment.getRgwSerial());
        query.put("serialNumber", getGatewaySerialByUserId(userEquipment.getRgwSerial()).orElseGet(()->userEquipment.getRgwSerial()));

        BasicDBObject mongoFieldOptions = new BasicDBObject();
        mongoFieldOptions.put("macAddress", 1);
        mongoFieldOptions.put("friendlyName", 1);
        mongoFieldOptions.put("timestamp", 1);
        mongoFieldOptions.put("hostname", 1);
        mongoFieldOptions.put("userId", 1);

        List<BasicDBObject> deviceDetailList = mongoService.findList(query, inParams, HOSTNAME_DETAIL, mongoFieldOptions);
        deviceDetailList.sort(Comparator.comparing(s -> ((Long) s.get("timestamp"))));
        Collections.reverse(deviceDetailList);
        if (Objects.nonNull(deviceDetailList) && !deviceDetailList.isEmpty()) {
            return getDisplayNameByPriorityForDevice(deviceDetailList.get(0));
        }
        return macAddress;
    }

    public HashMap<String, String> getFriendlyNameByStation(Set<String> macAddressSet, Equipment userEquipment) {
        HashMap<String, String> stationFriendlyName = new HashMap<>();
        HashMap<String, Object> query = new HashMap<>();
        HashMap<String, Object> inParams = new HashMap<>();
        HashMap<String, Object> inClause = new HashMap<>();
        inClause.put("resourceName", "macAddress");
        inClause.put("resource", macAddressSet);
        inParams.put("in", inClause);

        query.put("userId", userEquipment.getRgwSerial());

        BasicDBObject mongoFieldOptions = new BasicDBObject();
        mongoFieldOptions.put("macAddress", 1);
        mongoFieldOptions.put("friendlyName", 1);
        mongoFieldOptions.put("timestamp", 1);
        mongoFieldOptions.put("vendor", 1);
        mongoFieldOptions.put("hostname", 1);
        mongoFieldOptions.put("userId", 1);

        HashSet<String> uniqueDeviceMac = new HashSet<>();
        List<BasicDBObject> uniqueDeviceList = new ArrayList<>();
        List<BasicDBObject> deviceDetailList = mongoService.findList(query, inParams, ActiontecConstants.STATION_DETAIL, mongoFieldOptions);
        deviceDetailList.sort(Comparator.comparing(s -> ((Long) s.get("timestamp"))));
        Collections.reverse(deviceDetailList);
        deviceDetailList.forEach((device) -> {
            if (!uniqueDeviceMac.contains(String.valueOf(device.get("macAddress")))) {
                uniqueDeviceList.add(device);
                uniqueDeviceMac.add(String.valueOf(device.get("macAddress")));
            }
        });
        uniqueDeviceList.forEach((device) -> {
            String friendlyName = device.get("friendlyName") != null ? device.get("friendlyName").toString().trim() : null;
            if(friendlyName != null) {
                if(!friendlyName.isEmpty()) {
                    stationFriendlyName.put(String.valueOf(device.get("macAddress")), friendlyName);
                }
            }
        });

        return stationFriendlyName;
    }

    public HashMap<String, String> getFriendlyNameForStation(Set<String> macAddressSet, Equipment userEquipment) {
        HashMap<String, String> stationFriendlyName = new HashMap<>();
        HashMap<String, Object> query = new HashMap<>();
        HashMap<String, Object> inParams = new HashMap<>();
        HashMap<String, Object> inClause = new HashMap<>();
        inClause.put("resourceName", "macAddress");
        inClause.put("resource", macAddressSet);
        inParams.put("in", inClause);

        query.put("userId", userEquipment.getRgwSerial());

        BasicDBObject mongoFieldOptions = new BasicDBObject();
        mongoFieldOptions.put("macAddress", 1);
        mongoFieldOptions.put("friendlyName", 1);
        mongoFieldOptions.put("timestamp", 1);
        mongoFieldOptions.put("vendor", 1);
        mongoFieldOptions.put("hostname", 1);
        mongoFieldOptions.put("userId", 1);

        HashSet<String> uniqueDeviceMac = new HashSet<>();
        List<BasicDBObject> uniqueDeviceList = new ArrayList<>();
        List<BasicDBObject> deviceDetailList = mongoService.findList(query, inParams, ActiontecConstants.STATION_DETAIL, mongoFieldOptions);
        deviceDetailList.sort(Comparator.comparing(s -> ((Long) s.get("timestamp"))));
        Collections.reverse(deviceDetailList);
        deviceDetailList.forEach((device) -> {
            if (!uniqueDeviceMac.contains(String.valueOf(device.get("macAddress")))) {
                uniqueDeviceList.add(device);
                uniqueDeviceMac.add(String.valueOf(device.get("macAddress")));
            }
        });
        uniqueDeviceList.forEach((device) -> {
            String friendlyName = getDisplayNameByPriorityForDevice(device);
            stationFriendlyName.put(String.valueOf(device.get("macAddress")), friendlyName);
        });

        return stationFriendlyName;
    }

    public HashMap<String, String> getDisplayNameForEquipment(Set<String> equipmentSerialOrMAC, APDetailLookType lookType, boolean isOutputWithSerialNumber, Equipment userEquipment) {
        HashMap<String, Object> query = new HashMap<>();
        query.put("userId", userEquipment.getRgwSerial());

        HashMap<String, Object> inParams = new HashMap<>();
        HashMap<String, Object> inClause = new HashMap<>();
        inClause.put("resourceName", lookType.name());
        inClause.put("resource", equipmentSerialOrMAC);
        inParams.put("in", inClause);

        BasicDBObject mongoFieldOptions = new BasicDBObject();
        mongoFieldOptions.put("macAddress", 1);
        mongoFieldOptions.put("friendlyName", 1);
        mongoFieldOptions.put("modelName", 1);
        mongoFieldOptions.put("serialNumber", 1);

        List<BasicDBObject> equipmentsList = mongoService.findList(query, inParams, AP_DETAIL, mongoFieldOptions);

        HashMap<String, String> equipmentFriendlyName = new HashMap<>();
        if (lookType.name().equals("serialNumber") && isOutputWithSerialNumber) {
            equipmentsList.forEach(item -> {
                equipmentFriendlyName.put(String.valueOf(item.get("serialNumber")), getDisplayNameByPriorityForEquipment(item));
            });
        } else {
            equipmentsList.forEach(item -> {
                equipmentFriendlyName.put(String.valueOf(item.get("macAddress")), getDisplayNameByPriorityForEquipment(item));
            });
        }

        return equipmentFriendlyName;
    }

    public List<BasicDBObject> getHostnameDetail(String userId) {
        HashMap<String, Object> query = new HashMap<>();
        query.put("userId", userId);
        query.put("phyType", new BasicDBObject("$exists", true));
        List<BasicDBObject> result = mongoService.findListByTimestamp(query, HOSTNAME_DETAIL, TIMESTAMP, (3L * 24L * 60L), DESC, new BasicDBObject());
        Map<String, List<BasicDBObject>> dataMap = new HashMap<>();
        for (BasicDBObject record : result) {
            String macAdd = record.getString("macAddress");
            if (!dataMap.containsKey(macAdd))
                dataMap.put(macAdd, new ArrayList<>());
            dataMap.get(macAdd).add(record);
        }
        result.clear();
        dataMap.values().forEach(v -> {
            if (v.size() > ONE) {
                v.sort((q1, q2) -> Long.compare((Long) q2.get(TIMESTAMP), (Long) q1.get(TIMESTAMP)));
                if (v.get(ZERO).get(TIMESTAMP).equals(v.get(ONE).get(TIMESTAMP))) {
                    if (v.get(ZERO).get("type").equals(EXTENDER)) {
                        result.add(v.get(ZERO));
                    } else {
                        result.add(v.get(ONE));
                    }
                } else {
                    result.add(v.get(ZERO));
                }
            } else {
                result.add(v.get(ZERO));
            }
        });

        return result;
    }

    public List<EvAssociation> convertToAssocEvent(List<DBObject> dbObjectList) {
        List<EvAssociation> evAssociationsList = new ArrayList<>();
        dbObjectList.forEach(q -> {
            EvAssociation evAssociation = new EvAssociation();
            evAssociation.setAssocType(String.valueOf(q.get("assocType")));
            evAssociation.setBand(Objects.nonNull(q.get("band")) ? (Integer.valueOf(q.get("band").toString())) : 0);
            evAssociation.setBssid(String.valueOf(q.get("bssid")));
            evAssociation.setIsp(String.valueOf(q.get("isp")));
            evAssociation.setUserId(String.valueOf(q.get("userId")));
            evAssociation.setTimestamp(Objects.nonNull(q.get("timestamp")) ? Long.valueOf(q.get("timestamp").toString()) : 0);
            evAssociation.setSerialNumber(String.valueOf(q.get("serialNumber")));

            evAssociationsList.add(evAssociation);
        });

        return evAssociationsList;
    }

    public List<EvDisassociation> convertToDisAssocEvent(List<DBObject> dbObjectList) {
        List<EvDisassociation> evDisassociationList = new ArrayList<>();
        dbObjectList.forEach(q -> {
            EvDisassociation evDisAssociation = new EvDisassociation();
            evDisAssociation.setAssocType(String.valueOf(q.get("assocType")));
            evDisAssociation.setBand(Objects.nonNull(q.get("band")) ? (Integer.valueOf(q.get("band").toString())) : 0);
            evDisAssociation.setBssid(String.valueOf(q.get("bssid")));
            evDisAssociation.setIsp(String.valueOf(q.get("isp")));
            evDisAssociation.setUserId(String.valueOf(q.get("userId")));
            evDisAssociation.setTimestamp(Objects.nonNull(q.get("timestamp")) ? Long.valueOf(q.get("timestamp").toString()) : 0);
            evDisAssociation.setSerialNumber(String.valueOf(q.get("serialNumber")));

            evDisassociationList.add(evDisAssociation);
        });

        return evDisassociationList;
    }

    public List<EvSteeringLogs> convertToEVSteeringLog(List<DBObject> dbObjectList) {
        List<EvSteeringLogs> evSteeringLogsList = new ArrayList<>();

        dbObjectList.forEach(q -> {
            EvSteeringLogs evSteeringLogs = new EvSteeringLogs();
            evSteeringLogs.setIsp(String.valueOf(q.get("isp")));
            evSteeringLogs.setUserId(String.valueOf(q.get("userId")));
            evSteeringLogs.setTimestamp(Objects.nonNull(q.get("timestamp")) ? Long.valueOf(q.get("timestamp").toString()) : 0);
            evSteeringLogs.setMacaddress(String.valueOf(q.get("macAddress")));
            evSteeringLogs.setObssbssid(String.valueOf(q.get("obssBSSID")));
            evSteeringLogs.setObssrssi(Objects.nonNull(q.get("obssRSSI")) ? Long.valueOf(q.get("obssRSSI").toString()) : 0);
            evSteeringLogs.setIbssbssid(String.valueOf(q.get("ibssBSSID")));
            evSteeringLogs.setIbssrssi(Objects.nonNull(q.get("ibssRSSI")) ? Long.valueOf(q.get("ibssRSSI").toString()) : 0);
            evSteeringLogs.setTbssbssid(String.valueOf(q.get("tbssBSSID")));
            evSteeringLogs.setTbssrssi(Objects.nonNull(q.get("tbssRSSI")) ? Long.valueOf(q.get("tbssRSSI").toString()) : 0);
            evSteeringLogs.setSteeringtype(String.valueOf(q.get("steeringType")));
            evSteeringLogs.setSteeringend(String.valueOf(q.get("steeringEnd")));
            evSteeringLogs.setOphyrate(Objects.nonNull(q.get("oPhyrate")) ? Double.valueOf(q.get("oPhyrate").toString()) : 0);
            evSteeringLogs.setTphyrate(Objects.nonNull(q.get("tPhyrate")) ? Double.valueOf(q.get("tPhyrate").toString()) : 0);
            evSteeringLogs.setOairusage(Objects.nonNull(q.get("oAirusage")) ? Long.valueOf(q.get("oAirusage").toString()) : 0);
            evSteeringLogs.setOfat(Objects.nonNull(q.get("oFat")) ? Long.valueOf(q.get("oFat").toString()) : 0);
            evSteeringLogs.setIfat(Objects.nonNull(q.get("iFat")) ? Long.valueOf(q.get("iFat").toString()) : 0);
            evSteeringLogs.setTfat(Objects.nonNull(q.get("tFat")) ? Long.valueOf(q.get("tFat").toString()) : 0);

            evSteeringLogsList.add(evSteeringLogs);
        });

        return evSteeringLogsList;
    }

    public List<DeviceCapability> convertToDeviceCapability(List<DBObject> dbObjectList) {
        List<DeviceCapability> deviceCapabilityList = new ArrayList<>();
        dbObjectList.forEach(i -> {
            DeviceCapability deviceCapability = new DeviceCapability();
            deviceCapability.setCapability(String.valueOf(i.get("capability")));

            deviceCapabilityList.add(deviceCapability);
        });

        return deviceCapabilityList;
    }

    public String getCapabilityFromDB(BasicDBObject dbObj) {
        String DUAL_BAND = "DB ";
        String SINGLE_BAND = "SB ";
        String IE11K = "k";
        String IE11V = "v";
        String IE11R = "r";
        String DFS = "d";
        String DEF = "-";
        String strDualBandSupported = (true == dbObj.getBoolean(CapbilityFields.dualBandSupported.name(), false))? DUAL_BAND: SINGLE_BAND;
        String strDfsSupported = (true == dbObj.getBoolean(CapbilityFields.dfsSupported.name(), false))? DFS: DEF;
        String strIeee80211kSupported = (true == dbObj.getBoolean(CapbilityFields.ieee80211kSupported.name(), false))? IE11K: DEF;
        String strIeee80211vSupported = (true == dbObj.getBoolean(CapbilityFields.ieee80211vSupported.name(), false))? IE11V: DEF;
        String strIeee80211rSupported = (true == dbObj.getBoolean(CapbilityFields.ieee80211rSupported.name(), false))? IE11R: DEF;

        return strDualBandSupported.concat(strDfsSupported).concat(strIeee80211kSupported).concat(strIeee80211vSupported).concat(strIeee80211rSupported);
    }

    public List<EvRoaming> convertToRoamEvent(List<DBObject> dbObjectList) {
        List<EvRoaming> evRoamingList = new ArrayList<>();

        dbObjectList.forEach(q -> {
            EvRoaming evRoaming = new EvRoaming();
            evRoaming.setIsp(String.valueOf(q.get("isp")));
            evRoaming.setTimestamp(Objects.nonNull(q.get("timestamp")) ? Long.valueOf(q.get("timestamp").toString()) : 0);
            evRoaming.setMacaddress(String.valueOf(q.get("macAddress")));
            evRoaming.setObssid(String.valueOf(q.get("obssid")));
            evRoaming.setOband(Objects.nonNull(q.get("oband")) ? Integer.valueOf(q.get("oband").toString()) : 0);
            evRoaming.setOrssi(Objects.nonNull(q.get("orssi")) ? Double.valueOf(q.get("orssi").toString()) : 0);
            evRoaming.setNbssid(String.valueOf(q.get("nbssid")));
            evRoaming.setNband(Objects.nonNull(q.get("nband")) ? Integer.valueOf(q.get("nband").toString()) : 0);
            evRoaming.setNrssi(Objects.nonNull(q.get("nrssi")) ? Double.valueOf(q.get("nrssi").toString()) : 0);

            evRoamingList.add(evRoaming);
        });

        return evRoamingList;
    }

    public List<EvLogs> convertToEvLogs(List<DBObject> dbObjectList) {
        List<EvLogs> evLogsList = new ArrayList<>();
        dbObjectList.forEach(q -> {
            EvLogs evLogs = new EvLogs();
            evLogs.setIsp(String.valueOf(q.get("isp")));
            evLogs.setTimestamp(Objects.nonNull(q.get("timestamp")) ? Long.valueOf(q.get("timestamp").toString()) : 0);
            evLogs.setMacaddress(String.valueOf(q.get("macAddress")));
            evLogs.setLog(String.valueOf(q.get("log")));
            evLogsList.add(evLogs);
        });

        return evLogsList;
    }

    public List<EvStaConnectLogs> convertToEvStaConnectLogs(List<DBObject> dbObjectList) {
        List<EvStaConnectLogs> evStaConnectLogsList = new ArrayList<>();
        dbObjectList.forEach(q -> {
            EvStaConnectLogs evStaConnectLogs = new EvStaConnectLogs();
            evStaConnectLogs.setIsp(String.valueOf(q.get("isp")));
            evStaConnectLogs.setTimestamp(Objects.nonNull(q.get("timestamp")) ? Long.valueOf(q.get("timestamp").toString()) : 0);
            evStaConnectLogs.setMacaddress(String.valueOf(q.get("macAddress")));
            evStaConnectLogs.setStatus(String.valueOf(q.get("status")));
            evStaConnectLogs.setBand(Objects.nonNull(q.get("band")) ? Integer.valueOf(q.get("band").toString()) : 0);
            evStaConnectLogsList.add(evStaConnectLogs);
        });

        return evStaConnectLogsList;
    }

    public void addMiscDevicesForEquipment(String serialNumber, List<BasicDBObject> devicesList, ArrayList<DeviceListDTO.DeviceDetailListData> deviceDetailDataList, Equipment equipment) throws Exception {

        DeviceListDTO deviceListDTO = new DeviceListDTO();
        for (BasicDBObject q : devicesList) {
            DeviceListDTO.DeviceDetailListData matchedMACDeviceFromStation = deviceDetailDataList.stream().filter(device -> device.getMac().equals(q.getString("macAddress"))).findAny().orElse(null);
            if (Objects.nonNull(matchedMACDeviceFromStation)) {
                continue;
            }
            DeviceListDTO.DeviceDetailListData deviceDetail = deviceListDTO.new DeviceDetailListData();
            //DeviceDetail deviceDetail = new DeviceDetail();
            deviceDetail.setSsid("N/A");
            deviceDetail.setMac(String.valueOf(q.get("macAddress")));
            deviceDetail.setDeviceType(q.getBoolean("isExtender") ? "EXT" : (Objects.isNull(q.get("deviceType")) ? "Other" : q.getString("deviceType")));
            deviceDetail.setConnectedTo(q.getString("serialNumber"));
            HashSet<String> serialNumbers = new HashSet<>();
            serialNumbers.add(deviceDetail.getConnectedTo());
            HashMap<String, String> equipmentFriendlyName = getDisplayNameForEquipment(serialNumbers, APDetailLookType.serialNumber, true, equipment);
            deviceDetail.setConnectivityStatus("N/A"); //Changed from status to ConnectivityStatus to make sync with device detail api
            deviceDetail.setName((Objects.nonNull(q.get("hostname")) && !q.getString("hostname").isEmpty()) ? q.getString("hostname") : getDisplayNameByPriorityForDevice(q));
            deviceDetail.setBand(q.getString("phyType"));
            deviceDetail.setRssi(0.0);
            deviceDetail.setIp(String.valueOf(q.get("ip")));
            deviceDetail.setInternetOn(isInternetEnabledForDeviceMac(deviceDetail.getMac(), q.getString("userId")));
            deviceDetail.setVendor(getVendorName(q.getString("macAddress")));
            deviceDetail.setInternetOn(isInternetEnabledForDeviceMac(deviceDetail.getMac(), equipment.getRgwSerial()));
            deviceDetail.setLastReportedAt(q.getLong(TIMESTAMP));
            deviceDetailDataList.add(deviceDetail);
        }
    }

    public String getDisplayNameByPriorityForDevice(BasicDBObject item) {
        if (Objects.isNull(item.get("friendlyName")) || item.get("friendlyName").toString().trim().isEmpty()) {
            if (Objects.isNull(item.get("hostname")) || item.get("hostname").toString().trim().isEmpty()) {
                String hostNameForAppleDevice = getHostNameForAppleDevices(item);
                if (Objects.isNull(hostNameForAppleDevice) || EMPTY_STRING.equals(hostNameForAppleDevice)) {
                    String vendorName = getVendorName(String.valueOf(item.get("macAddress")));
                    if (Objects.isNull(vendorName) || EMPTY_STRING.equals(vendorName)) {
                        return Objects.nonNull(item.get("macAddress")) ? item.get("macAddress").toString() : null;
                    } else {
                        return vendorName;
                    }
                } else {
                    return hostNameForAppleDevice;
                }
            } else {
                return item.get("hostname").toString();
            }
        } else {
            return item.get("friendlyName").toString();
        }
    }

    public String getDisplayNameByUserIdMacAddr(String userId, String macAddress) {
        HashMap<String, Object> appendableParams = new HashMap<>();
        HashMap<String, Object> queryParam = new HashMap<>();
        queryParam.put("macAddress", macAddress);
        queryParam.put("userId", userId);

        BasicDBObject fieldsToRemove = new BasicDBObject();
        fieldsToRemove.put("_id", 0);
        DBObject item = mongoService.findOne(queryParam, appendableParams, AP_DETAIL, fieldsToRemove);
        if (Objects.nonNull(item) && StringUtils.equals(String.valueOf(item.get("type")), EXTENDER))
        {
            return getDisplayNameForEquipment((BasicDBObject) item);
        }

        return null;
    }

    public String getDisplayNameByPriorityForEthernetMoCADevices(BasicDBObject item) {
        HashMap<String, Object> appendableParams = new HashMap<>();
        HashMap<String, Object> queryParam = new HashMap<>();
        queryParam.put("macAddress", item.get("macAddress"));
        queryParam.put("userId", item.get("userId"));

        BasicDBObject fieldsToRemove = new BasicDBObject();
        fieldsToRemove.put("_id", 0);
        DBObject dbObject = mongoService.findOne(queryParam, appendableParams, HOSTNAME_DETAIL, fieldsToRemove);
        if (Objects.isNull(dbObject)) {
            String vendorName = getVendorName(String.valueOf(item.get("macAddress")));
            if (Objects.isNull(vendorName) || EMPTY_STRING.equals(vendorName)) {
                return Objects.nonNull(item.get("macAddress")) ? item.get("macAddress").toString() : null;
            } else {
                return vendorName;
            }
        } else if (Objects.isNull(dbObject.get("friendlyName")) || EMPTY_STRING.equals(dbObject.get("friendlyName").toString().trim())) {
            if (Objects.isNull(dbObject.get("hostname")) || dbObject.get("hostname").toString().trim().isEmpty()) {
                String vendorName = getVendorName(String.valueOf(item.get("macAddress")));
                if (Objects.isNull(vendorName) || EMPTY_STRING.equals(vendorName)) {
                    return Objects.nonNull(item.get("macAddress")) ? item.get("macAddress").toString() : null;
                } else {
                    return vendorName;
                }
            } else {
                return dbObject.get("hostname").toString();
            }
        } else
            return dbObject.get("friendlyName").toString();
    }

    private String getHostNameByPriorityForDevice(BasicDBObject item) {
        if (Objects.isNull(item.get("hostname")) || EMPTY_STRING.equals(item.get("hostname").toString().trim())) {
            String hostNameForAppleDevice = getHostNameForAppleDevices(item);
            if (Objects.isNull(hostNameForAppleDevice) || EMPTY_STRING.equals(hostNameForAppleDevice.trim())) {
                //return Objects.nonNull(item.get("macAddress")) ? item.get("macAddress").toString() : null;
                /*if (Objects.isNull(getVendorName(String.valueOf(item.get("macAddress"))))) {
                    return Objects.nonNull(item.get("macAddress")) ? item.get("macAddress").toString() : null;
                } else {
                    return getVendorName(String.valueOf(item.get("macAddress")));
                }*/
                return null;
            } else {
                return hostNameForAppleDevice;
            }
        } else {
            return item.get("hostname").toString();
        }

    }

    public void processBSSIDDetails(HashMap<String, Object> bssidDetails, HashSet<String> bssidSets, BasicDBObject apDetail) {
        List<HashMap<String, String>> bssids = Objects.nonNull(apDetail.get("ssids")) ? (List<HashMap<String, String>>) apDetail.get("ssids") : new ArrayList<>();
        if (!bssids.isEmpty()) {
            for (HashMap<String, String> bssidDetail : bssids) {
                HashMap<String, String> details = new HashMap<>();
                if (Objects.nonNull(bssidDetail.get("BSSID")) && bssidDetail.get("BSSID").trim().equals("00:00:00:00:00:00")) {
                    details.put("ssid", "N/A");
                    details.put("band", "N/A");
                    details.put("friendlyName", "N/A");
                    details.put("serialNumber", apDetail.getString("serialNumber"));
                } else {
                    details.put("ssid", String.valueOf(bssidDetail.get("SSID")));
                    details.put("band", String.valueOf(bssidDetail.get("RadioKey")));
                    details.put("friendlyName", getDisplayNameForEquipment(apDetail));
                    details.put("serialNumber", apDetail.getString("serialNumber"));
                }
                bssidDetails.put(String.valueOf(bssidDetail.get("BSSID")), details);
                bssidSets.add(String.valueOf(bssidDetail.get("BSSID")));
            }
        }
    }

    public String getDisplayNameForEquipment(BasicDBObject item) {
        String name = null;

        if (StringUtils.equals(String.valueOf(item.get("type")), EXTENDER)== true && (Objects.isNull(item.get("friendlyName")) || item.get("friendlyName").toString().trim().isEmpty()))
        {
            name = Objects.isNull(item.get("modelName")) ? item.get("macAddress").toString() : item.get("modelName").toString() + "(EXT)";
        } else {
            name = Objects.isNull(item.get("friendlyName")) ? Objects.isNull(item.get("modelName")) ? item.get("macAddress").toString() : item.get("modelName").toString() : item.get("friendlyName").toString();
        }

        return name;
    }

    public String getDisplayNameByPriorityForEquipment(BasicDBObject item) {
        if (Objects.isNull(item.get("friendlyName")) || item.get("friendlyName").toString().trim().isEmpty()) {
            if (Objects.isNull(item.get("modelName")) || item.get("modelName").toString().trim().isEmpty()) {
                return String.valueOf(item.get("serialNumber"));
            } else {
                return String.valueOf(item.get("modelName"));
            }
        } else {
            return String.valueOf(item.get("friendlyName"));
        }
    }

    public Optional<String> getControllerSerialByUserId(String userId) {
        if (meshEnable) {
            BasicDBObject query = new BasicDBObject();
            query.put(ApDetailDto.ApDetailFields.userId.name(), userId);
            query.put(ApDetailDto.ApDetailFields.controller.name(), true);
            BasicDBObject projection = new BasicDBObject();
            projection.put(ApDetailDto.ApDetailFields.serialNumber.name(), 1);

            DBCollection dbCollection = mongoTemplate.getCollection(ApDetailDto.COLLECTION_apDetail);
            DBCursor dbCursor = dbCollection.find(query, projection).limit(2);

            if (1 == dbCursor.size()) {
                return Optional.ofNullable(dbCursor.next()).map(dbObject -> {
                    return String.valueOf(dbObject.get(HostDto.Fields.serialNumber.name()));
                });
            }
        }

        return Optional.ofNullable(null);
    }

    public Optional<String> getGatewaySerialByUserId(String userId) {
        if (meshEnable) {
            BasicDBObject query = new BasicDBObject();
            query.put(ApDetailDto.ApDetailFields.userId.name(), userId);
            query.put(ApDetailDto.ApDetailFields.type.name(), GATEWAY);
            BasicDBObject projection = new BasicDBObject();
            projection.put(ApDetailDto.ApDetailFields.serialNumber.name(), 1);

            DBCollection dbCollection = mongoTemplate.getCollection(ApDetailDto.COLLECTION_apDetail);
            DBCursor dbCursor = dbCollection.find(query, projection).limit(2);

            if (1 == dbCursor.size()) {
                return Optional.ofNullable(dbCursor.next()).map(dbObject -> {
                    return String.valueOf(dbObject.get(HostDto.Fields.serialNumber.name()));
                });
            }
        }

        return Optional.ofNullable(null);
    }

    public Optional<String> getUIdFromTrackingRecordBySn(String serial) {
        if (meshEnable) {
            HashMap<String, Object> params = new HashMap<>();
            params.put("serial", serial);
            List<TrackingEquipment> trackingEquipmentList = null;
            try {
                trackingEquipmentList = (List<TrackingEquipment>) dataAccessService.read(TrackingEquipment.class, ActiontecSQL.GET_TRACKING_EQUIPMENT_BY_SN, params);
                if (Objects.nonNull(trackingEquipmentList) && !trackingEquipmentList.isEmpty()) {
                    return  Optional.ofNullable(trackingEquipmentList.get(0)).map(p->p.getuId());
                }
            } catch (Exception e) {
                LOG.error("Get Error!");
            }
        }

        return Optional.ofNullable(null);
    }

    // Only For Apple Devices
    private String getHostNameForAppleDevices(BasicDBObject item) {
        HashMap<String, Object> appendableParams = new HashMap<>();
        HashMap<String, Object> queryParam = new HashMap<>();
        queryParam.put("macAddress", item.get("macAddress"));
        queryParam.put("userId", item.get("userId"));

        BasicDBObject fieldsToRemove = new BasicDBObject();
        fieldsToRemove.put("_id", 0);
        DBObject dbObject = mongoService.findOne(queryParam, appendableParams, HOSTNAME_DETAIL, fieldsToRemove);
        return (Objects.isNull(dbObject) || Objects.isNull(dbObject.get("hostname"))) ? null : dbObject.get("hostname").toString();
    }

    public String getGroupIdFromClusterId(String id) throws Exception {
        if (!isClusterExist(id))
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Cluster does not exist with cluster Id :: " + id);

        String groupId = null;
        HashMap<String, Object> param = new HashMap<>();
        param.put("clusterId", id);

        List<String> groupList = dataAccessService.readNative(ClusterInfoSQL.GET_GROUP_ID_BY_CLUSTER_ID, param);

        if (Objects.nonNull(groupList) && !groupList.isEmpty()) {
            for (String compartmentId : groupList) {
                groupId = compartmentId;
                break;
            }
        }
        if (!CommonUtils.isSysAdmin() && Objects.nonNull(groupId) && !CommonUtils.getGroupIdOfLoggedInUser().equals(groupId))
            throw new AuthEntityNotAllowedException(ApiResponseCode.RESOURCE_NOT_ALLOWED);
        return groupId;
    }

    public boolean isDefaultCluster(String id) throws Exception {
        ClusterInfo clusterInfo = (ClusterInfo) dataAccessService.read(ClusterInfo.class, id);
        return clusterInfo.isDefaultCluster();
    }

    public Set<String> getAssociatedApsByClusterId(String id) throws Exception {
        HashMap<String, Object> param = new HashMap<>();
        param.put("clusterId", id);

        List<String> userApList = dataAccessService.readNative(ClusterInfoSQL.GET_ALL_USER_AP_ID_BY_CLUSTER_ID, param);
        Set<String> associatedAps = new HashSet<>();

        if (Objects.nonNull(userApList) && !userApList.isEmpty()) {
            associatedAps.addAll(userApList);
        }

        return associatedAps;
    }

    public boolean isClusterExist(String id) throws Exception {
        ClusterInfo clusterInfo = (ClusterInfo) dataAccessService.read(ClusterInfo.class, id);
        if (Objects.isNull(clusterInfo))
            return false;
        return true;
    }

    public Map<String, Double> convertDBObjectToChartData(DBObject dbObject) {
        Double wifiDl = 0.0d;
        Double wifiUl = 0.0d;
        Double wifiHealth = 0.0d;
        Double avgRadioOccupancy6g = 0.0d;
        Double avgRadioOccupancy5g = 0.0d;
        Double avgRadioOccupancy24g = 0.0d;
        HashMap<String, Double> chartData = new HashMap<>();

        if (Objects.nonNull(dbObject)) {
            try {
//                if (Objects.nonNull(dbObject.get("sumStaCount")) && Long.valueOf(dbObject.get("sumStaCount").toString()) > 0) {
//                    wifiDl = Double.parseDouble(TWO_DECIMAL_PLACE.format(Double.valueOf(Objects.nonNull(dbObject.get("sumDlSpeed")) ? dbObject.get("sumDlSpeed").toString() : "0") / Long.valueOf(Objects.nonNull(dbObject.get("sumStaCount")) ? dbObject.get("sumStaCount").toString() : "1") / 1000F));
//                    wifiUl = Double.parseDouble(TWO_DECIMAL_PLACE.format(Double.valueOf(Objects.nonNull(dbObject.get("sumUlSpeed")) ? dbObject.get("sumUlSpeed").toString() : "0") / Long.valueOf(Objects.nonNull(dbObject.get("sumStaCount")) ? dbObject.get("sumStaCount").toString() : "1") / 1000F));
//                }

//                if (Objects.nonNull(dbObject.get("sumUserCount")) && Long.valueOf(dbObject.get("sumUserCount").toString()) > 0) {
//                    wifiHealth = Double.parseDouble(Objects.nonNull(dbObject.get("sumScore")) ? dbObject.get("sumScore").toString() : "0.0") / Long.valueOf(Objects.nonNull(dbObject.get("sumUserCount")) ? dbObject.get("sumUserCount").toString() : "1");
//                    avgRadioOccupancy5g = Double.parseDouble(Objects.nonNull(dbObject.get("sumRadioOccupancy5G")) ? dbObject.get("sumRadioOccupancy5G").toString() : "0.0") / Long.valueOf(Objects.nonNull(dbObject.get("sumUserCount")) ? dbObject.get("sumUserCount").toString() : "1");
//                    avgRadioOccupancy24g = Double.parseDouble(Objects.nonNull(dbObject.get("sumRadioOccupancy24G")) ? dbObject.get("sumRadioOccupancy24G").toString() : "0.0") / Long.valueOf(Objects.nonNull(dbObject.get("sumUserCount")) ? dbObject.get("sumUserCount").toString() : "1");
//                }

                wifiDl = Double.parseDouble(TWO_DECIMAL_PLACE.format(Double.valueOf(Objects.nonNull(dbObject.get("sumDlSpeed")) ? dbObject.get("sumDlSpeed").toString() : "0")));
                wifiUl = Double.parseDouble(TWO_DECIMAL_PLACE.format(Double.valueOf(Objects.nonNull(dbObject.get("sumUlSpeed")) ? dbObject.get("sumUlSpeed").toString() : "0")));
                wifiHealth = Double.parseDouble(Objects.nonNull(dbObject.get("sumScore")) ? dbObject.get("sumScore").toString() : "0.0");
                avgRadioOccupancy6g = Double.parseDouble(Objects.nonNull(dbObject.get("sumRadioOccupancy6G")) ? dbObject.get("sumRadioOccupancy6G").toString() : "0.0");
                avgRadioOccupancy5g = Double.parseDouble(Objects.nonNull(dbObject.get("sumRadioOccupancy5G")) ? dbObject.get("sumRadioOccupancy5G").toString() : "0.0");
                avgRadioOccupancy24g = Double.parseDouble(Objects.nonNull(dbObject.get("sumRadioOccupancy24G")) ? dbObject.get("sumRadioOccupancy24G").toString() : "0.0");
            } catch (NumberFormatException e) {
            }
        }
        chartData.put("wifiDl", wifiDl);
        chartData.put("wifiUl", wifiUl);
        chartData.put("wifiHealth", wifiHealth);
        chartData.put("avgRadioOccupancy6G", avgRadioOccupancy6g);
        chartData.put("avgRadioOccupancy5G", avgRadioOccupancy5g);
        chartData.put("avgRadioOccupancy24G", avgRadioOccupancy24g);

        return chartData;
    }

    public List<HashMap<String, Object>> prepareAirTimeUtzChartData(String keyToRead, Map<String, Double> dataForToday, Map<String, Double> dataForLast7Days, Map<String, Double> dataForLast30Days, Map<String, Double> dataForLast90Days) {
        List<HashMap<String, Object>> graphDataHistorical = new ArrayList();
        HashMap<String, Object> dataMapForToday = new HashMap();
        HashMap<String, Object> dataMapForLast7Days = new HashMap();
        HashMap<String, Object> dataMapForLast30Days = new HashMap();
        HashMap<String, Object> dataMapForLast90Days = new HashMap();
        dataMapForToday.put("Day", "Today");

        int i;
        for (i = 0; i < keyToRead.split(",").length; ++i) {
            dataMapForToday.put(keyToRead.split(",")[i], dataForToday == null ? 0.0D : Double.parseDouble(TWO_DECIMAL_PLACE.format(Double.parseDouble(dataForToday.get(keyToRead.split(",")[i]) != null && !dataForToday.get(keyToRead.split(",")[i]).toString().equals("NaN") ? dataForToday.get(keyToRead.split(",")[i]).toString() : String.valueOf(0)))));
        }

        dataMapForLast7Days.put("Day", "Last 7 days");

        for (i = 0; i < keyToRead.split(",").length; ++i) {
            dataMapForLast7Days.put(keyToRead.split(",")[i], dataForLast7Days == null ? 0.0D : Double.parseDouble(TWO_DECIMAL_PLACE.format(Double.parseDouble(dataForLast7Days.get(keyToRead.split(",")[i]) != null && !dataForLast7Days.get(keyToRead.split(",")[i]).toString().equals("NaN") ? dataForLast7Days.get(keyToRead.split(",")[i]).toString() : String.valueOf(0)))));
        }

        dataMapForLast30Days.put("Day", "Last 30 days");

        for (i = 0; i < keyToRead.split(",").length; ++i) {
            dataMapForLast30Days.put(keyToRead.split(",")[i], dataForLast30Days == null ? 0.0D : Double.parseDouble(TWO_DECIMAL_PLACE.format(Double.parseDouble(dataForLast30Days.get(keyToRead.split(",")[i]) != null && !dataForLast30Days.get(keyToRead.split(",")[i]).toString().equals("NaN") ? dataForLast30Days.get(keyToRead.split(",")[i]).toString() : String.valueOf(0)))));
        }

        dataMapForLast90Days.put("Day", "Last 90 days");

        for (i = 0; i < keyToRead.split(",").length; ++i) {
            dataMapForLast90Days.put(keyToRead.split(",")[i], dataForLast90Days == null ? 0.0D : Double.parseDouble(TWO_DECIMAL_PLACE.format(Double.parseDouble(dataForLast90Days.get(keyToRead.split(",")[i]) != null && !dataForLast90Days.get(keyToRead.split(",")[i]).toString().equals("NaN") ? dataForLast90Days.get(keyToRead.split(",")[i]).toString() : String.valueOf(0)))));
        }

        graphDataHistorical.add(dataMapForToday);
        graphDataHistorical.add(dataMapForLast7Days);
        graphDataHistorical.add(dataMapForLast30Days);
        graphDataHistorical.add(dataMapForLast90Days);
        return graphDataHistorical;
    }

    public List<HashMap<String, Object>> prepareAirTimeUtzChartDatav2(String keyToRead, Map<String, Double> dataForToday, Map<String, Double> dataForLast7Days, Map<String, Double> dataForLast30Days, Map<String, Double> dataForLast90Days) {
        List<HashMap<String, Object>> graphDataHistorical = new ArrayList();
        HashMap<String, Object> dataMapForToday = new HashMap();
        HashMap<String, Object> dataMapForLast7Days = new HashMap();
        HashMap<String, Object> dataMapForLast30Days = new HashMap();
        HashMap<String, Object> dataMapForLast90Days = new HashMap();
        dataMapForToday.put("Day", "Last 24 Hours");

        int i;
        for (i = 0; i < keyToRead.split(",").length; ++i) {
            dataMapForToday.put(keyToRead.split(",")[i], dataForToday == null ? 0.0D : Double.parseDouble(TWO_DECIMAL_PLACE.format(Double.parseDouble(dataForToday.get(keyToRead.split(",")[i]) != null && !dataForToday.get(keyToRead.split(",")[i]).toString().equals("NaN") ? dataForToday.get(keyToRead.split(",")[i]).toString() : String.valueOf(0)))));
        }

        dataMapForLast7Days.put("Day", "Last 7 days");

        for (i = 0; i < keyToRead.split(",").length; ++i) {
            dataMapForLast7Days.put(keyToRead.split(",")[i], dataForLast7Days == null ? 0.0D : Double.parseDouble(TWO_DECIMAL_PLACE.format(Double.parseDouble(dataForLast7Days.get(keyToRead.split(",")[i]) != null && !dataForLast7Days.get(keyToRead.split(",")[i]).toString().equals("NaN") ? dataForLast7Days.get(keyToRead.split(",")[i]).toString() : String.valueOf(0)))));
        }

        dataMapForLast30Days.put("Day", "Last 30 days");

        for (i = 0; i < keyToRead.split(",").length; ++i) {
            dataMapForLast30Days.put(keyToRead.split(",")[i], dataForLast30Days == null ? 0.0D : Double.parseDouble(TWO_DECIMAL_PLACE.format(Double.parseDouble(dataForLast30Days.get(keyToRead.split(",")[i]) != null && !dataForLast30Days.get(keyToRead.split(",")[i]).toString().equals("NaN") ? dataForLast30Days.get(keyToRead.split(",")[i]).toString() : String.valueOf(0)))));
        }

        dataMapForLast90Days.put("Day", "Last 90 days");

        for (i = 0; i < keyToRead.split(",").length; ++i) {
            dataMapForLast90Days.put(keyToRead.split(",")[i], dataForLast90Days == null ? 0.0D : Double.parseDouble(TWO_DECIMAL_PLACE.format(Double.parseDouble(dataForLast90Days.get(keyToRead.split(",")[i]) != null && !dataForLast90Days.get(keyToRead.split(",")[i]).toString().equals("NaN") ? dataForLast90Days.get(keyToRead.split(",")[i]).toString() : String.valueOf(0)))));
        }

        graphDataHistorical.add(dataMapForToday);
        graphDataHistorical.add(dataMapForLast7Days);
        graphDataHistorical.add(dataMapForLast30Days);
        graphDataHistorical.add(dataMapForLast90Days);
        return graphDataHistorical;
    }

    public String getSsidForDeviceDetail(String userId, String serailNumber, String bssid, String bssidType) {
        String ssid = null;
        HashMap<String, Object> params = new HashMap<>();
        BasicDBObject projection = new BasicDBObject();
        params.put("userId", userId);
        params.put("serialNumber", serailNumber);
        projection.clear();
        projection.put(bssidType, 1);

        List<BasicDBObject> equipmentList = mongoService.findList(params, AP_DETAIL, projection);

        if (!equipmentList.isEmpty()) {
            List<DBObject> bssid24List = (List<DBObject>) equipmentList.get(0).get(bssidType);
            for (DBObject dbObject : bssid24List) {
                String apBssid = Objects.isNull(dbObject.get("bssid")) ? EMPTY_STRING : dbObject.get("bssid").toString();
                if (apBssid.equals(bssid)) {
                    ssid = Objects.isNull(dbObject.get("ssid")) ? "N/A" : dbObject.get("ssid").toString();
                    break;
                }
            }
        }

        return ssid;
    }

    public String getSsidForDeviceDetail(String userId, String serialNumber, String bssid) {
        String ssid = null;
        HashMap<String, Object> params = new HashMap<>();
        BasicDBObject projection = new BasicDBObject();
        params.put("userId", userId);
        params.put("serialNumber", serialNumber);
        projection.clear();
        projection.put("ssids", 1);

        List<BasicDBObject> equipmentList = mongoService.findList(params, AP_DETAIL, projection);

        if (!equipmentList.isEmpty()) {
            List<DBObject> bssidList = (List<DBObject>) equipmentList.get(0).get("ssids");
            for (DBObject dbObject : bssidList) {
                String apBssid = Objects.isNull(dbObject.get("BSSID")) ? EMPTY_STRING : dbObject.get("BSSID").toString();
                if (StringUtils.equals(apBssid, bssid)) {
                    ssid = Objects.isNull(dbObject.get("SSID")) ? "N/A" : dbObject.get("SSID").toString();
                    break;
                }
            }
        }

        return ssid;
    }

    /////////////////////////////// Validation Methods ///////////////////////////////////////

    public String getSsidForDeviceDetail(BasicDBObject equipment, String bssid, String bssidType) {
        String ssid = null;
        if (Objects.nonNull(equipment)) {
            List<DBObject> bssid24List = (List<DBObject>) equipment.get(bssidType);
            for (DBObject dbObject : bssid24List) {
                String apBssid = Objects.isNull(dbObject.get("bssid")) ? "" : dbObject.get("bssid").toString();
                if (apBssid.equals(bssid)) {
                    ssid = Objects.isNull(dbObject.get("ssid")) ? "N/A" : dbObject.get("ssid").toString();
                    break;
                }
            }
        }

        return ssid;
    }

    public void checkUserAccess(String subscriberId) {
        if (CommonUtils.isEndUser()) {
            if (!CommonUtils.getUserIdOfLoggedInUser().equals(subscriberId)) {
                throw new AuthEntityNotAllowedException(ApiResponseCode.RESOURCE_NOT_ALLOWED);
            }
        }
    }

    public void checkUserAccessForEquipment(String rgwSerial) throws Exception {
        if (CommonUtils.isEndUser()) {
            HashMap<String, Object> query = new HashMap<>();
            query.put("subscriberId", CommonUtils.getUserIdOfLoggedInUser());
            query.put("id", rgwSerial);
            List<Equipment> equipmentList = (List<Equipment>) dataAccessService.read(Equipment.class, EquipmentSQL.GET_EQUIPMENT_BY_EQUIPMENT_ID_AND_SUBSCRIBER, query);
            if (Objects.isNull(equipmentList) || equipmentList.isEmpty()) {
                UserContext userContext = ExecutionContext.get().getUsercontext();
                LOG.error("checkUserAccessForEquipment failed. equipmentId:[{}] userId:[{}] name:[{}], userName:[{}]",
                        rgwSerial, userContext.getId(), userContext.getName(), userContext.getUsername());
                throw new AuthEntityNotAllowedException(ApiResponseCode.RESOURCE_NOT_ALLOWED);
            }
        }
    }

    public void checkForGroupAccess(String groupId) {
        if (!CommonUtils.isSysAdmin()) {
            if (!CommonUtils.isSameGroup(groupId)) {
                throw new AuthEntityNotAllowedException(ApiResponseCode.RESOURCE_NOT_ALLOWED);
            }
        }

    }

    public void checkDeviceMACBelongsToUser(String deviceMac, Equipment userEquipments) throws Exception {
        HashMap<String, Object> queryParams = new HashMap<>();
        BasicDBObject fieldsToRemove = new BasicDBObject();
        if (CommonUtils.isEndUser()) {
            queryParams.put("userId", userEquipments.getRgwSerial());
            queryParams.put("macAddress", deviceMac);

            fieldsToRemove.put("_id", 0);
            if (Objects.isNull(mongoService.findOne(queryParams, new HashMap<>(), STATION_DETAIL, fieldsToRemove))) {
                throw new AuthEntityNotAllowedException(ApiResponseCode.RESOURCE_NOT_ALLOWED);
            }
        } else if (!CommonUtils.isSysAdmin()) {
            queryParams.clear();
            queryParams.put("userId", userEquipments.getRgwSerial());
            queryParams.put("macAddress", deviceMac);
            DBObject stationDetail = mongoService.findOne(queryParams, new HashMap<>(), STATION_DETAIL, fieldsToRemove);
            getUserAPFromSubscriberIdOrApId(stationDetail.get("userId").toString());
        }
    }

    public void checkDeviceMACBelongsToSubscriber(String deviceMac, Equipment userEquipment) {
        HashMap<String, Object> queryParams = new HashMap<>();
        queryParams.put("userId", userEquipment.getRgwSerial());
        queryParams.put("macAddress", deviceMac);
        boolean macPresent = true;
        BasicDBObject fieldsToRemove = new BasicDBObject();
        fieldsToRemove.put("_id", 0);
        if (Objects.isNull(mongoService.findOne(queryParams, new HashMap<>(), STATION_DETAIL, fieldsToRemove))) {
            macPresent = false;
        }
        if (CommonUtils.isEndUser()) {
            if (!macPresent) {
                throw new AuthEntityNotAllowedException(ApiResponseCode.RESOURCE_NOT_ALLOWED);
            }
        } else if (!CommonUtils.isSysAdmin() || CommonUtils.isSysAdmin()) {
            if (!macPresent) {
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "MAC Address: " + deviceMac + " does not belong to Equipment with RGW Serial: " + userEquipment.getRgwSerial());
            }
        }
    }

    /*public Equipment getUserAPFromSubscriberId(String id) throws Exception {
        HashMap<String, String> queryParams = new HashMap<>();
        queryParams.put("userId", id);
        List<UserAP> userApList = (List<UserAP>) dataAccessService.read(UserAP.class, UserAPSQL.GET_AP_ID_FROM_PLATFORM_USER_ID, queryParams);
        if (Objects.isNull(userApList) || (userApList.isEmpty())) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "No Subscriber found with Id " + id);
        }
        checkUserAccess(id);
        checkForGroupAccess(userApList.get(0));
        return userApList.get(0);
    }*/

    public String getRGWMACOfSubscriber(String userId) {
        String rgwMac = EMPTY_STRING;
        BasicDBObject mongoFieldOptions = new BasicDBObject();
        mongoFieldOptions.clear();
        mongoFieldOptions.put("_id", 0);

        HashMap<String, String> query = new HashMap<>();
        HashMap<String, String> appendableParams = new HashMap<>();
        query.put("userId", userId);
        query.put("serialNumber", userId);

        DBObject aPDetails = mongoService.findOne(query, appendableParams, AP_DETAIL, TIMESTAMP, DESC, mongoFieldOptions);
        if (Objects.nonNull(aPDetails)) {
            rgwMac = String.valueOf(aPDetails.get("macAddress"));
        }

        return rgwMac;
    }

    public Equipment getUserAPFromSubscriberIdOrApId(String equipmentIdOrSerialOrSTN) throws Exception {
        HashMap<String, String> queryParams = new HashMap<>();
        queryParams.put("id", getUIdFromTrackingRecordBySn(equipmentIdOrSerialOrSTN).orElseGet(()->equipmentIdOrSerialOrSTN));
        List<Equipment> equipmentList = (List<Equipment>) dataAccessService.read(Equipment.class, EquipmentSQL.GET_EQUIPMENT_LIST_BY_EQUIPMENT_ID_OR_RGW_SERIAL_OR_STN, queryParams);

        if (Objects.isNull(equipmentList) || (equipmentList.isEmpty())) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "No Equipment found with EquipmentId or RGW Serial Number or STN " + equipmentIdOrSerialOrSTN);
        }

        if (equipmentList.size() > 1) {
            throw new Exception("more than one equipment with id:" + equipmentIdOrSerialOrSTN);
        }

        checkUserAccessForEquipment(equipmentList.get(0).getId());
        checkForGroupAccess(equipmentList.get(0).getGroupId());

        return equipmentList.get(0);
    }

    public Equipment getUserAPWithoutAccessChecking(String equipmentIdOrSerialOrSTN) throws Exception {
        HashMap<String, String> queryParams = new HashMap<>();
        queryParams.put("id", equipmentIdOrSerialOrSTN);
        List<Equipment> equipmentList = (List<Equipment>) dataAccessService.read(Equipment.class, EquipmentSQL.GET_EQUIPMENT_LIST_BY_EQUIPMENT_ID_OR_RGW_SERIAL_OR_STN, queryParams);

        if (Objects.isNull(equipmentList) || (equipmentList.isEmpty())) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "No Equipment found with EquipmentId or RGW Serial Number or STN " + equipmentIdOrSerialOrSTN);
        }

        if (equipmentList.size() > 1) {
            throw new Exception("more than one equipment with id:" + equipmentIdOrSerialOrSTN);
        }

        return equipmentList.get(0);
    }

    public Equipment getUserApByStn(String stn) throws Exception {
        HashMap<String, String> queryParams = new HashMap<>();
        queryParams.put("serviceTelephoneNo", stn);
        List<Equipment> equipmentList = null;
        try {
            equipmentList = (List<Equipment>) dataAccessService.read(Equipment.class, EquipmentSQL.GET_EQUIPMENT_BY_STN, queryParams);
        } catch (Exception e) {
            throw new RuntimeException("failed to read Equipment", e);
        }
        if (Objects.isNull(equipmentList) || (equipmentList.isEmpty())) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "No Equipment found with EquipmentId or RGW Serial Number or STN " + stn);
        }
        checkUserAccessForEquipment(equipmentList.get(0).getId());
        checkForGroupAccess(equipmentList.get(0).getGroupId());
        return equipmentList.get(0);
    }

    public Subscriber getSubscriberFromSubscriberIdOrGAN(String subscriberIdOrGAN) throws Exception {
        HashMap<String, String> queryParams = new HashMap<>();
        queryParams.put("id", subscriberIdOrGAN);
        List<Subscriber> subscriberList = (List<Subscriber>) dataAccessService.read(Subscriber.class, SubscriberSQL.GET_SUBSCRIBER_LIST_BY_SUBSCRIBER_ID_OR_GAN, queryParams);
        if (Objects.isNull(subscriberList) || (subscriberList.isEmpty())) {
            throw new UrlNotFoundException(HttpStatus.NOT_FOUND.value(), "No Subscriber found with SubscriberId or GAN " + subscriberIdOrGAN);
        }
        return subscriberList.get(0);
    }


    public Equipment getEquipmentByEquipmentId(String equipmentId) throws Exception {
        HashMap<String, String> queryParams = new HashMap<>();
        queryParams.put("id", equipmentId);
        Equipment equipment = (Equipment) dataAccessService.read(Equipment.class, equipmentId);
        if (Objects.isNull(equipment)) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "No Equipment found with EquipmentId " + equipmentId);
        }
        checkUserAccessForEquipment(equipment.getRgwSerial());
        checkForGroupAccess(equipment.getGroupId());

        return equipment;
    }


    /*private UserAP convertESDataToUserAp(SubscriberESDTO subscriberESDTO) {
        UserAP userAP = new UserAP();
        userAP.setUserId(subscriberESDTO.getSubscriberId());
        userAP.setApId(subscriberESDTO.getApId());
        userAP.setGroupId(subscriberESDTO.getGroupId());
        userAP.setUpLinkRate(subscriberESDTO.getUpLinkRate());
        userAP.setDownLinkRate(subscriberESDTO.getDownLinkRate());
        userAP.setGlobalAccountNo(subscriberESDTO.getGlobalAccountNo());
        userAP.setCamsAccountNo(subscriberESDTO.getCamsAccountNo());
        userAP.setRgwMAC(subscriberESDTO.getRgwMAC());
        userAP.setAlexaVoiceSubscribed(false);
        userAP.setGoogleHomeVoiceSubscribed(false);
        userAP.setMobileSubscribed(subscriberESDTO.isMobileSubscribed());
        userAP.setPhoneNo(subscriberESDTO.getPhoneNo());
        userAP.setUserName(subscriberESDTO.getName());
        userAP.setProcessed(true);
        userAP.setCreatedAt(new Date(subscriberESDTO.getCreatedAt()));
        userAP.setCreatedBy(subscriberESDTO.getCreatedBy());

        return userAP;

    }*/

    /*private HashMap<String, Object> mapElasticToSubscriberDTO(ElasticSearchDTO response) {
        HashMap<String, Object> userApData = null;
        SubscriberESDTO subscriberESDTO = objectMapper.convertValue(response.getObjects().get(0), SubscriberESDTO.class);
        if (Objects.nonNull(subscriberESDTO)) {
            userApData = new HashMap<>();
            userApData.put("email", subscriberESDTO.getEmail());
            userApData.put("apId", subscriberESDTO.getApId());
            userApData.put("userId", subscriberESDTO.getSubscriberId());
            userApData.put("upLinkRate", subscriberESDTO.getUpLinkRate());
            userApData.put("downLinkRate", subscriberESDTO.getDownLinkRate());
        }

        return userApData;
    }*/

    public void subscriberDataExistInMongo(Equipment userEquipment) throws Exception {
        BasicDBObject mongoFieldOptions = new BasicDBObject();
        mongoFieldOptions.clear();
        mongoFieldOptions.put("_id", 0);
        HashMap<String, String> queryParams = new HashMap<>();
        queryParams.put("userId", userEquipment.getRgwSerial());
        if (meshEnable)
            queryParams.put("type", GATEWAY);
        else
            queryParams.put("serialNumber", userEquipment.getRgwSerial());
        DBObject aPDetails = mongoService.findOne(queryParams, new HashMap<>(), AP_DETAIL, TIMESTAMP, DESC, mongoFieldOptions);
        if (Objects.isNull(aPDetails) || Objects.isNull(aPDetails.get("timestamp")))
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Equipment Info purged / not found for this equipment");
    }

    public void subscriberGroupMisMatch(Equipment userEquipment) throws Exception {
        String actualISP = getIspOfSubscriber(userEquipment.getRgwSerial());
        if (Objects.isNull(actualISP))
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "ISP of this Equipment is not configured in the system.");

        String ispId = getIspByName(actualISP);
        if (Objects.isNull(ispId))
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "ISP of this Equipment is not configured in the system.");

        String groupIspId = null;
        if (cacheService.isMapExist(COMPARTMENT_LIST_MAP_NAME) && Objects.nonNull(cacheService.read(COMPARTMENT_INFO, COMPARTMENT_LIST_MAP_NAME))) {
            List<HashMap<String, Object>> compartmentDataList = (List<HashMap<String, Object>>) cacheService.read(COMPARTMENT_INFO, COMPARTMENT_LIST_MAP_NAME);
            if (Objects.nonNull(compartmentDataList)) {
                HashMap<String, Object> compartment = compartmentDataList.stream().filter(q -> (String.valueOf(q.get("id")).equals(userEquipment.getGroupId()))).findAny().orElse(null);
                groupIspId = (Objects.nonNull(compartment) && Objects.nonNull(compartment.get("ispId"))) ? compartment.get("ispId").toString() : null;
            }
        }

        if (Objects.isNull(groupIspId)) {
            Compartment compartment = (Compartment) dataAccessService.read(Compartment.class, userEquipment.getGroupId());
            if (Objects.isNull(compartment))
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "No Group-ISP mapping found for this Subscriber");

            groupIspId = compartment.getIspId();
        }
        if (!ispId.equals(groupIspId)) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Equipment ISP and Subscriber ISP mismatch! Migrate subscriber to appropriate group.");
        }
    }

    public String checkSerialNumOfEquipment(String serialNumber, Equipment userEquipment) throws Exception {
        String userId;
        BasicDBObject mongoFieldOptions = new BasicDBObject();
        mongoFieldOptions.clear();
        mongoFieldOptions.put("_id", 0);
        HashMap<String, String> queryParams = new HashMap<>();
        if (Objects.nonNull(userEquipment)) {
            queryParams.put("userId", userEquipment.getRgwSerial());
        }
        queryParams.put("serialNumber", serialNumber);
        DBObject aPDetails = mongoService.findOne(queryParams, new HashMap<>(), AP_DETAIL, TIMESTAMP, DESC, mongoFieldOptions);
        if (Objects.isNull(aPDetails)) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "No Equipment found with Serial Number " + serialNumber);
        }

        userId = String.valueOf(aPDetails.get("userId"));
        return userId;
    }

    public void checkMacAddressOfEthMoca(String macAddress, Equipment userEquipment) {
        BasicDBObject mongoFieldOptions = new BasicDBObject();
        mongoFieldOptions.clear();
        mongoFieldOptions.put("_id", 0);

        HashMap<String, String> queryParams = new HashMap<>();
        HashMap<String, String> appendableParams = new HashMap<>();
        queryParams.put("userId", userEquipment.getRgwSerial());
        queryParams.put("macAddress", macAddress);
        DBObject deviceDetails = mongoService.findOne(queryParams, appendableParams, HOSTNAME_DETAIL, TIMESTAMP, DESC, mongoFieldOptions);
        if (Objects.isNull(deviceDetails)) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "No Device found with MAC Address " + macAddress);
        }
    }

    public String getMacAddressOfL2MacAddress(String macAddress, Equipment userEquipment) {
        HashMap<String, String> queryParams = new HashMap<>();
        queryParams.put("userId", userEquipment.getRgwSerial());
        queryParams.put("l2MacAddress", macAddress);

        return Optional.ofNullable(
                        mongoService.findOne(
                                queryParams,
                                new HashMap<>(),
                                HOSTNAME_DETAIL,
                                TIMESTAMP,
                                DESC,
                                new BasicDBObject("_id", 0)
                        )
                )
                .map(hostnameDetail -> hostnameDetail.get("macAddress"))
                .map(Object::toString)
                .orElse(macAddress);
    }

    public void checkMacAddressOfStation(String macAddress, Equipment userEquipment) {
        BasicDBObject mongoFieldOptions = new BasicDBObject();
        mongoFieldOptions.clear();
        mongoFieldOptions.put("_id", 0);

        HashMap<String, String> queryParams = new HashMap<>();
        HashMap<String, String> appendableParams = new HashMap<>();
        queryParams.put("userId", userEquipment.getRgwSerial());
        queryParams.put("macAddress", macAddress);
        DBObject deviceDetails = mongoService.findOne(queryParams, appendableParams, STATION_DETAIL, TIMESTAMP, DESC, mongoFieldOptions);
        if (Objects.isNull(deviceDetails)) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "No Device found with MAC Address " + macAddress);
        }
        String connectivityStatus = getConnectivityStatusForDevice(deviceDetails);
        boolean isForgetDevice = Objects.isNull(deviceDetails.get("isForgetDevice")) ? false : Boolean.valueOf(deviceDetails.get("isForgetDevice").toString());
        if (isForgetDevice && connectivityStatus.equals("GREY")) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Device with MAC :: " + macAddress + " is forgotten, until Device is again connected");
        }
    }

    /*public void checkUserAp(String id) throws Exception {
        getUserAPFromSubscriberId(id);
    }*/

    public void checkAPSerialNumBelongsToSubscriber(String serialNumber, Equipment userEquipment) throws Exception {
        BasicDBObject mongoFieldOptions = new BasicDBObject();
        mongoFieldOptions.clear();
        mongoFieldOptions.put("_id", 0);
        HashMap<String, String> queryParams = new HashMap<>();
        queryParams.put("userId", userEquipment.getRgwSerial());
        queryParams.put("serialNumber", serialNumber);
        DBObject aPDetails = mongoService.findOne(queryParams, new HashMap<>(), AP_DETAIL, TIMESTAMP, DESC, mongoFieldOptions);
        if (Objects.isNull(aPDetails))
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Equipment with SerialNumber : " + serialNumber + " does not belong to Subscriber with Id / RGW Serial: " + userEquipment.getRgwSerial() + " / " + userEquipment.getRgwSerial());
    }

    public void checkAPSerialNumberBelongsToUser(String serialNumber, Equipment userEquipments) throws Exception {
        HashMap<String, Object> queryParams = new HashMap<>();
        BasicDBObject mongoFieldOptions = new BasicDBObject();
        mongoFieldOptions.clear();
        mongoFieldOptions.put("_id", 0);
        BasicDBObject fieldsToRemove = new BasicDBObject();
        if (CommonUtils.isEndUser()) {
            queryParams.put("userId", userEquipments.getRgwSerial());
            queryParams.put("serialNumber", serialNumber);

            fieldsToRemove.put("_id", 0);
            if (Objects.isNull(mongoService.findOne(queryParams, new HashMap<>(), AP_DETAIL, TIMESTAMP, DESC, mongoFieldOptions))) {
                throw new AuthEntityNotAllowedException(ApiResponseCode.RESOURCE_NOT_ALLOWED);
            }
        } else if (!CommonUtils.isSysAdmin()) {
            queryParams.clear();
            queryParams.put("userId", userEquipments.getRgwSerial());
            queryParams.put("serialNumber", serialNumber);
            DBObject aPDetails = mongoService.findOne(queryParams, new HashMap<>(), AP_DETAIL, TIMESTAMP, DESC, mongoFieldOptions);
            try {
//                manageSubscriberService.findSubscriberByIdOrSerial(aPDetails.get("userId").toString());
            } catch (Exception e) {
                throw new AuthEntityNotAllowedException(ApiResponseCode.RESOURCE_NOT_ALLOWED);
            }
        }
    }

    public Date calculateRandomTimeByDate(Date date) {
        long currentTimeStamp = date.getTime();
        long _24HrsfutureTimeStamp = date.getTime() + 24 * 60 * 60 * 1000;
        return new Date(ThreadLocalRandom.current().nextLong(currentTimeStamp, _24HrsfutureTimeStamp));
    }

    public List<BasicDBObject> filterEquipmentsAndCheckForRgwAndExt(List<BasicDBObject> equipmentList) {
        // ETL Bug - Incorrectly Marking RGW as EXT that is why handling in Platform
        // ETL Bug - Filtering the Older Record with Duplicate Items (Records with MAC Address Null and Serial Number Null)
        // ETL Bug - Filtering Records with Blank Serial Numbers
        equipmentList = equipmentList.stream().filter(p -> (p.getString("macAddress") != null && p.getString("serialNumber") != null && !p.getString("serialNumber").equals(EMPTY_STRING))).collect(Collectors.toList());
        equipmentList = equipmentList.parallelStream().peek(p -> {
            try {
                if (p.getString("type").equals(GATEWAY)) {
                    p.put("type", GATEWAY);
                }
            } catch (Exception e) {
            }
        }).collect(Collectors.toList());
        return equipmentList;
    }

    public boolean isInternetEnabledForDeviceMac(String macAddress, String userId) {
        HashMap<String, Object> query = new HashMap<>();
        query.put("userId", userId);
        query.put("macAddress", macAddress);

        List<DBObject> internetStateInfos = mongoService.findList(INTERNET_STATE_INFO, query, DATE, DESC);

        if (internetStateInfos == null || internetStateInfos.isEmpty() || Objects.isNull(internetStateInfos.get(0).get("internetState"))) {
            return true;
        } else {
            return Boolean.valueOf(internetStateInfos.get(0).get("internetState").toString());
        }
    }

    public boolean isSmartSteeringEnabledForDeviceMac(String userId, String serialNumber) {
        BasicDBObject mongoFieldOptions = new BasicDBObject();
        mongoFieldOptions.clear();
        mongoFieldOptions.put("_id", 0);

        HashMap<String, String> queryParams = new HashMap<>();
        HashMap<String, String> appendableParams = new HashMap<>();
        queryParams.put("userId", userId);
        queryParams.put("serialNumber", serialNumber);

        DBObject aPDetails = mongoService.findOne(queryParams, appendableParams, AP_DETAIL, TIMESTAMP, DESC, mongoFieldOptions);
        if (aPDetails == null) {
            LOG.warn("getSteering apDetail not found, equipmentId:[{}]", serialNumber);
            return false;
        }

        DBObject wifiConf = (DBObject) aPDetails.get("wifiConf");
        if (wifiConf == null) {
            LOG.warn("getSteering smartSteering get from smartSteeringEnable, equipmentId:[{}]", serialNumber);
            return String.valueOf(aPDetails.get("smartSteeringEnable")).equals("1") ? true : false;
        }

        DBObject smartSteering = (DBObject) wifiConf.get("SmartSteering");
        if (smartSteering == null) {
            LOG.warn("getSteering smartSteering get from smartSteeringEnable, equipmentId:[{}]", serialNumber);
            return String.valueOf(aPDetails.get("smartSteeringEnable")).equals("1") ? true : false;
        }

        return Objects.nonNull(smartSteering.get("enable"))? Boolean.valueOf(smartSteering.get("enable").toString()):
                                        Objects.isNull(smartSteering.get("Enable"))? false: "1".equals(smartSteering.get("Enable").toString());
    }

    public void createUpdateRPCRecord(Object obj) {
        DBObject query = null;
        String collection = null;
        InternetStateInfo internetStateInfo;
        BasicDBObject record = new BasicDBObject();
        record.put("date", CommonUtils.getCurrentTimeInMillis());
        if (obj instanceof InternetStateInfo) {
            internetStateInfo = (InternetStateInfo) obj;
            record.put("_class", "com.incs83.app.entities.InternetStateInfo");
            record.put("internetState", internetStateInfo.isInternetState());
            record.put("macAddress", internetStateInfo.getMacAddress());
            record.put("userId", internetStateInfo.getUserId());
            query = new BasicDBObject("userId", internetStateInfo.getUserId()).append("macAddress", internetStateInfo.getMacAddress());
            collection = "internetStateInfo";
        }
        DBObject update = new BasicDBObject("$set", record);
        mongoService.update(query, update, true, true, collection);
    }

    public void enableDisableSubscriberToAlexaFeatures(String userId, boolean alexaSubscribed, String alexaEmail) {
        DBObject query = new BasicDBObject();
        query.put("userId", userId);

        BasicDBObject item = new BasicDBObject();
        item.put("alexaSubscribed", alexaSubscribed);
        item.put("alexaEmail", alexaEmail);

        DBObject update = new BasicDBObject();
        update.put("$set", item);

        mongoService.update(query, update, false, true, ActiontecConstants.ALEX_AND_GOOGLEHOME_SUBSCRIBERS);
    }

    public void enableDisableSubscriberToGoogleHomeFeatures(String userId, boolean googleHomeSubscribed, String googleHomeEmail) {
        DBObject query = new BasicDBObject();
        query.put("userId", userId);

        BasicDBObject item = new BasicDBObject();
        item.put("googleHomeSubscribed", googleHomeSubscribed);
        item.put("googleHomeEmail", googleHomeEmail);

        DBObject update = new BasicDBObject();
        update.put("$set", item);

        mongoService.update(query, update, false, true, ActiontecConstants.ALEX_AND_GOOGLEHOME_SUBSCRIBERS);
    }

    public void disableSubscriberToAlexaAndGoogleHomeFeatures(String userId) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("userId", userId);
        mongoService.deleteOne(params, ActiontecConstants.ALEX_AND_GOOGLEHOME_SUBSCRIBERS);
    }

    public HashSet<String> findAllStationBySerialNumberAndUserId(String userId, String serialNumber) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("userId", userId);
        params.put("serialNumber", serialNumber);
        HashMap<String, Object> inParams = new HashMap<>();

        BasicDBObject mongoFieldOptions = new BasicDBObject();
        mongoFieldOptions.put("macAddress", 1);

        List<BasicDBObject> deviceDetailList = mongoService.findList(params, inParams, ActiontecConstants.STATION_DETAIL, mongoFieldOptions);
        HashSet<String> deviceMacList = deviceDetailList.stream().map((device) -> String.valueOf(device.get("macAddress"))).collect(Collectors.toCollection(HashSet::new));

        return deviceMacList;
    }

    public String getAPIDFromMAC(String rgwMAC) throws Exception {
        RestHighLevelClient client = this.esClientConfig.getRestClient();
        if (Objects.isNull(client)) {
            return getAPIDFromDB(rgwMAC);
        } else {
            QueryBuilder qb = QueryBuilders
                    .matchQuery("rgwMAC", rgwMAC.toLowerCase());

            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
            searchSourceBuilder.query(qb);

            SearchRequest searchRequest = new SearchRequest();
            searchRequest.source(searchSourceBuilder);

            SearchResponse searchResponse = null;

            try {
                searchResponse = client.search(searchRequest, RequestOptions.DEFAULT);
                if (Objects.nonNull(searchResponse) && searchResponse.getHits().getTotalHits() > 0) {
                    Map<String, Object> data = searchResponse.getHits().getAt(0).getSourceAsMap();
                    return String.valueOf(data.get("apId"));
                } else {
                    throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "No Equipment found with MAC " + rgwMAC);
                }
            } catch (ValidationException var10) {
                throw var10;
            } catch (Exception e) {
                return getAPIDFromDB(rgwMAC);
            }
        }
    }

    public String getAPIDFromDB(String rgwMAC) throws Exception {
        String apId;
        HashMap<String, Object> query = new HashMap<>();
        query.put("rgwMAC", rgwMAC);

        List<Equipment> equipmentList = (List<Equipment>) dataAccessService.read(Equipment.class, EquipmentSQL.GET_EQUIPMENT_LIST_BY_RGW_MAC, query);
        if (Objects.nonNull(equipmentList) && !equipmentList.isEmpty())
            apId = equipmentList.get(0).getRgwSerial();
        else {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "No Equipment found with MAC " + rgwMAC);
        }

        return apId;
    }


    /*public void isEquipmentOffline(String serialNumber, UserAP userAP) {
        BasicDBObject mongoFieldOptions = new BasicDBObject();
        mongoFieldOptions.clear();
        mongoFieldOptions.put("timestamp", 1);
        mongoFieldOptions.put("sendingTime", 1);
        mongoFieldOptions.put("etlVersion", 1);


        HashMap<String, String> queryParams = new HashMap<>();
        HashMap<String, String> appendableParams = new HashMap<>();
        queryParams.put("userId", userAP.getApId());
        queryParams.put("serialNumber", serialNumber);
        DBObject aPDetails = mongoService.findOne(queryParams, appendableParams, AP_DETAIL, TIMESTAMP, DESC, mongoFieldOptions);
        if (Objects.isNull(aPDetails)) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Serial Number: " + serialNumber + " does not belong to Subscriber with Id: " + userAP.getUserId());
        }
        long currentTime = Calendar.getInstance().getTimeInMillis();
        long lastMetricReceived = Long.valueOf(String.valueOf(("v3".equalsIgnoreCase(String.valueOf(aPDetails.get("etlVersion"))) || Objects.isNull(aPDetails.get("sendingTime"))) ? aPDetails.get("timestamp") : aPDetails.get("sendingTime")));
        long timeDiff = currentTime - lastMetricReceived;

        HashMap<String, String> commonProps = commonService.read(COMMON_CONFIG);
        int equipmentOfflineTime = 7;
        try {
            equipmentOfflineTime = Integer.valueOf(commonProps.get(COMMON_DEVICE_STATUS_RETENTION_TIME));
        } catch (Exception e) {

        }

        if (timeDiff > (equipmentOfflineTime * 60 * 1000)) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Cannot perform operation, Equipment is offline.");
        }
    }*/

    /*public void updateAPDetailAsProcessed(String rgwSerial) {
        HashMap<String, Object> queryParams = new HashMap<>();
        queryParams.put("processed", new BasicDBObject("$exists", false));
        queryParams.put("userId", rgwSerial);
        queryParams.put("serialNumber", rgwSerial);
        HashMap<String, Object> appendableParams = new HashMap<>();
        BasicDBObject projection = new BasicDBObject();
        DBObject apDetail = mongoService.findOne(queryParams, appendableParams, AP_DETAIL, projection);
        if (Objects.nonNull(apDetail)) {
            BasicDBObject record = new BasicDBObject();
            record.put("processed", true);
            DBObject update = new BasicDBObject("$set", record);
            DBObject query = new BasicDBObject("userId", apDetail.get("userId"));
            mongoService.update(query, update, true, true, AP_DETAIL);
        }
    }*/

    public String getIspOfSubscriber(String apId) {
        String isp = null;
        HashMap<String, Object> queryParams = new HashMap<>();
        queryParams.put("userId", apId);
        if (meshEnable)
            queryParams.put("type", GATEWAY);
        else
            queryParams.put("serialNumber", apId);

        HashMap<String, Object> appendableParams = new HashMap<>();
        BasicDBObject projection = new BasicDBObject();
        projection.put("_id", ZERO);
        DBObject apDetail = mongoService.findOne(queryParams, appendableParams, AP_DETAIL, projection);
        if (Objects.nonNull(apDetail)) {
            isp = String.valueOf(apDetail.get("isp"));
        }

        return isp;
    }

    public String getIspByName(String name) {
        String ispId = null;

        ISPDTOList ispdtoList = (ISPDTOList) cacheService.read(ISP_KEY, ISP);
        if (Objects.nonNull(ispdtoList)) {
            ISPModel ispModel = ispdtoList.getData().stream().filter(q -> q.getName().equals(name)).findAny().orElse(null);
            ispId = Objects.nonNull(ispModel) ? ispModel.getId() : null;
        } else {
            HashMap<String, Object> queryParams = new HashMap<>();
            queryParams.put("name", name);

            HashMap<String, Object> appendableParams = new HashMap<>();
            BasicDBObject fieldsToRemove = new BasicDBObject();
            fieldsToRemove.put("_id", ZERO);

            List<DBObject> dbObjects = mongoService.findList(queryParams, appendableParams, INTERNET_SERVICE_PROVIDER, fieldsToRemove);
            if (Objects.nonNull(dbObjects) && !dbObjects.isEmpty())
                ispId = Objects.nonNull(dbObjects.get(0).get("id")) ? dbObjects.get(0).get("id").toString() : null;
        }

        return ispId;
    }

    public void deleteUserTokenFromHazelcast(String userId) throws Exception {
        HashMap<String, String> params = new HashMap<>();
        params.put("user_id", userId);
        params.put("token", null);
        LoginDetails loginDetails = (LoginDetails) dataAccessService.read(LoginDetails.class, userId);
        if (Objects.nonNull(loginDetails)) {
            loginDetails.setToken(null);
            dataAccessService.update(LoginDetails.class, loginDetails);
            hazelcastService.update(userId, new HashMap<>(), ApplicationConstants.TOKEN);
        }
    }

    public String getVendorNameFromCache(String key) {
        String vendorName = null;
        String maskedKey = MacOuiUtil.maskLocallyAdministeredBit(key.trim());
        String[] macIdOctetArray = maskedKey.split(COLON);
        if (macIdOctetArray.length >= 3) {
            String macOctet = (macIdOctetArray[0] + COLON + macIdOctetArray[1] + COLON + macIdOctetArray[2]).toLowerCase();
            Object vName = hazelcastService.read(macOctet.toLowerCase(), MAC_VENDOR_DETAIL);
            if (Objects.nonNull(vName)) {
                vendorName = String.valueOf(vName);
            }
        }

        return vendorName;
    }

    public String getVendorName(String key) {
        String vendorName = null;
        String maskedKey = MacOuiUtil.maskLocallyAdministeredBit(key.trim());
        String[] macIdOctetArray = maskedKey.split(COLON);
        if (macIdOctetArray.length >= 3) {
            String macOctet = (macIdOctetArray[0] + COLON + macIdOctetArray[1] + COLON + macIdOctetArray[2]).toLowerCase();
            Object vName = hazelcastService.read(macOctet.toLowerCase(), MAC_VENDOR_DETAIL);
            if (Objects.nonNull(vName)) {
                vendorName = String.valueOf(vName);
            } else {
                HashMap<String, Object> filter = new BasicDBObject();
                filter.put("macIdOctet", macOctet);

                BasicDBObject sort = new BasicDBObject();
                sort.put("date", -1);

                BasicDBObject fieldsToRemove = new BasicDBObject();

                DBObject dbObject = mongoService.findOne(filter, MAC_ID_PARENT, sort, fieldsToRemove);
                if (Objects.nonNull(dbObject)) {
                    vendorName = String.valueOf(dbObject.get("vendor"));
                }
            }
        }

        return vendorName;
    }

    public void processRPCResult(String transactionId, int maxTries, long threadToSleep) throws Exception {
        BasicDBObject query = new BasicDBObject();
        query.put("_id", transactionId);
        boolean resultReceived = false;
        DBObject rpcResult = null;
        int initialStart = 0;
        Boolean isTimeout = false;
        while (!resultReceived) {
            try {
                Thread.sleep(threadToSleep);
            } catch (InterruptedException e) {
                LOG.error("Error interruption", e);
            }
            initialStart++;
            if (initialStart == maxTries) {
                isTimeout = true;
                LOG.error("Operation Timed Out...please retry");
                BasicDBObject dataToUpdate = new BasicDBObject();
                dataToUpdate.put("result", TIME_OUT);
                dataToUpdate.put("date", new Date());

                BasicDBObject update = new BasicDBObject();
                update.put("$set", dataToUpdate);
                mongoService.update(query, update, false, false, RPC_RESULT_INFO);
                break;
            }
            rpcResult = mongoService.findOne(RPC_RESULT_INFO, query);
            if (Objects.isNull(rpcResult) || Objects.isNull(rpcResult.get("result"))) {
                continue;
            } else {
                resultReceived = true;
            }
        }
        rpcResult = mongoService.findOne(RPC_RESULT_INFO, query);
        if (Objects.nonNull(rpcResult)) {
            if (!RPC_RESULT.equals(rpcResult.get("result"))) {
                if (isTimeout)
                    throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Request Timed Out, please try again after sometime.");
                else
                    throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Request failed, RPC result :: " + String.valueOf(rpcResult.get("result")));
            }
        }
    }

    public Object processFSecureRPCResult(String transactionId, int maxTries, long threadToSleep) throws Exception {
        BasicDBObject query = new BasicDBObject();
        query.put("_id", transactionId);
        boolean resultReceived = false;
        DBObject rpcResult = null;
        int initialStart = 0;
        Boolean isTimeout = false;
        Object response = new Object();
        while (!resultReceived) {
            try {
                Thread.sleep(threadToSleep);
            } catch (InterruptedException e) {
                LOG.error("Error interruption", e);
            }
            initialStart++;
            if (initialStart == maxTries) {
                isTimeout = true;
                LOG.error("Operation Timed Out...please retry");
                BasicDBObject dataToUpdate = new BasicDBObject();
                dataToUpdate.put("isTimeout", isTimeout);
                dataToUpdate.put("timestamp", CommonUtils.getCurrentTimeInMillis());

                BasicDBObject update = new BasicDBObject();
                update.put("$set", dataToUpdate);
                mongoService.update(query, update, false, false, JSON_RPC_V3_INFO);
                break;
            }
            rpcResult = mongoService.findOne(JSON_RPC_V3_INFO, query);
            if (Objects.isNull(rpcResult) || Objects.isNull(rpcResult.get(RPC_RESPONSE_KEY)) || !Boolean.valueOf("" + rpcResult.get(RPC_RESPONSE_KEY))) {
                continue;
            } else {
                resultReceived = true;
            }
        }
        rpcResult = mongoService.findOne(JSON_RPC_V3_INFO, query);
        if (Objects.nonNull(rpcResult)) {
            Integer code = Integer.valueOf(String.valueOf(Objects.isNull(rpcResult.get("code")) ? "0" : rpcResult.get("code")));
            if (code == 200) {
                HashMap<String, Object> rpcRawResponse = objectMapper.convertValue(JSON.parse(String.valueOf(rpcResult.get("rawResponse"))), HashMap.class);
                response = rpcRawResponse.get("payload");
            } else {
                if (isTimeout || code == 0) {
                    throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Request Timed Out, please try again after sometime.");
                } else {
                    throw new ValidationException(code, FSECURE_RESPONSE_MESSAGE.get(code));
                }
            }
        }
        return response;
    }

    public Object processDeRPCResult(String transactionId, int maxTries, long threadToSleep) throws Exception {
        BasicDBObject query = new BasicDBObject();
        query.put("_id", transactionId);
        boolean resultReceived = false;
        DBObject rpcResult = null;
        int initialStart = 0;
        Boolean isTimeout = false;
        Object response = new Object();
        while (!resultReceived) {
            try {
                Thread.sleep(threadToSleep);
            } catch (InterruptedException e) {
                LOG.error("Error interruption", e);
            }
            initialStart++;
            if (initialStart == maxTries) {
                isTimeout = true;
                LOG.error("Operation Timed Out...please retry");
                BasicDBObject dataToUpdate = new BasicDBObject();
                dataToUpdate.put("isTimeout", isTimeout);
                dataToUpdate.put("timestamp", CommonUtils.getCurrentTimeInMillis());

                BasicDBObject update = new BasicDBObject();
                update.put("$set", dataToUpdate);
                mongoService.update(query, update, false, false, JSON_RPC_V3_INFO);
                break;
            }
            rpcResult = mongoService.findOne(JSON_RPC_V3_INFO, query);
            if (Objects.isNull(rpcResult) || Objects.isNull(rpcResult.get(RPC_RESPONSE_KEY)) || !Boolean.valueOf("" + rpcResult.get(RPC_RESPONSE_KEY))) {
                continue;
            } else {
                resultReceived = true;
            }
        }
        rpcResult = mongoService.findOne(JSON_RPC_V3_INFO, query);
        if (Objects.nonNull(rpcResult)) {
            Integer code = Integer.valueOf(String.valueOf(Objects.isNull(rpcResult.get("code")) ? "0" : rpcResult.get("code")));
            if (code == 200) {
                HashMap<String, Object> rpcRawResponse = objectMapper.convertValue(JSON.parse(String.valueOf(rpcResult.get("rawResponse"))), HashMap.class);
                response = rpcRawResponse.get("response");
            } else {
                if (isTimeout || code == 0) {
                    throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Request Timed Out, please try again after sometime.");
                } else {
                    throw new ValidationException(code, FSECURE_RESPONSE_MESSAGE.get(code));
                }
            }
        }

        return Objects.isNull(response) ? Collections.emptyList() : response;
    }

    public Object processSmmRPCResult(String transactionId, int maxTries, long threadToSleep) throws Exception {
        BasicDBObject query = new BasicDBObject();
        query.put("_id", transactionId);
        boolean resultReceived = false;
        DBObject rpcResult = null;
        int initialStart = 0;
        Boolean isTimeout = false;
        Object response = new Object();
        while (!resultReceived) {
            try {
                Thread.sleep(threadToSleep);
            } catch (InterruptedException e) {
                LOG.error("Error interruption", e);
            }
            initialStart++;
            if (initialStart == maxTries) {
                isTimeout = true;
                LOG.error("Operation Timed Out...please retry");
                BasicDBObject dataToUpdate = new BasicDBObject();
                dataToUpdate.put("isTimeout", isTimeout);
                dataToUpdate.put("timestamp", CommonUtils.getCurrentTimeInMillis());

                BasicDBObject update = new BasicDBObject();
                update.put("$set", dataToUpdate);
                mongoService.update(query, update, false, false, JSON_RPC_V3_INFO);
                break;
            }
            rpcResult = mongoService.findOne(JSON_RPC_V3_INFO, query);
            if (Objects.isNull(rpcResult) || Objects.isNull(rpcResult.get(RPC_RESPONSE_KEY)) || !Boolean.valueOf("" + rpcResult.get(RPC_RESPONSE_KEY))) {
                continue;
            } else {
                resultReceived = true;
            }
        }
        rpcResult = mongoService.findOne(JSON_RPC_V3_INFO, query);
        if (Objects.nonNull(rpcResult)) {
            Integer code = Integer.valueOf(String.valueOf(Objects.isNull(rpcResult.get("code")) ? "0" : rpcResult.get("code")));
            //if (code == 200) {
                HashMap<String, Object> rpcRawResponse = objectMapper.convertValue(JSON.parse(String.valueOf(rpcResult.get("rawResponse"))), HashMap.class);
                response = rpcRawResponse.get("response");
//            } else {
//                if (isTimeout || code == 0) {
//                    throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Request Timed Out, please try again after sometime.");
//                } else {
//                    throw new ValidationException(code, FSECURE_RESPONSE_MESSAGE.get(code));
//                }
//            }
        }

        return Objects.isNull(response) ? Collections.emptyList() : response;
    }

    /*public DBObject getTimeSeriesDataForWifiHealthPerMinute(UserAP userAP, String serialNumber) {
        TimeZone timeZone = TimeZone.getTimeZone("UTC");
        Calendar calendar = Calendar.getInstance(timeZone);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        calendar.add(Calendar.DATE, CALENDER_CRITERIA_YESTERDAY);

        long nowMillSec = calendar.getTimeInMillis();
        long dateHour = nowMillSec / 1000;

        BasicDBObject dateCriteria = new BasicDBObject();
        dateCriteria.put("$gte", dateHour);

        BasicDBObject matchParam = new BasicDBObject();
        matchParam.put("userId", userAP.getApId());
        matchParam.put("serialNumber", serialNumber);
        matchParam.put("dateHour", dateCriteria);

        BasicDBObject match = new BasicDBObject();
        match.put("$match", matchParam);

        BasicDBObject unwind = new BasicDBObject();
        unwind.put("$unwind", "$data");

        BasicDBObject groupParam = new BasicDBObject();
        groupParam.put("_id", null);
        groupParam.put("score", new BasicDBObject("$sum", "$data.score"));
        groupParam.put("wanTxBytes", new BasicDBObject("$sum", "$data.wanDeltaBytesSent"));
        groupParam.put("wanRxBytes", new BasicDBObject("$sum", "$data.wanDeltaBytesReceived"));
        groupParam.put("mocaTxBytes", new BasicDBObject("$sum", "$data.mocaDeltaBytesSent"));
        groupParam.put("mocaRxBytes", new BasicDBObject("$sum", "$data.mocaDeltaBytesReceived"));
        groupParam.put("ethP0TxBytes", new BasicDBObject("$sum", "$data.ethP0DeltaBytesSent"));
        groupParam.put("ethP0RxBytes", new BasicDBObject("$sum", "$data.ethP0DeltaBytesReceived"));
        groupParam.put("ethP1TxBytes", new BasicDBObject("$sum", "$data.ethP1DeltaBytesSent"));
        groupParam.put("ethP1RxBytes", new BasicDBObject("$sum", "$data.ethP1DeltaBytesReceived"));
        groupParam.put("ethP2TxBytes", new BasicDBObject("$sum", "$data.ethP2DeltaBytesSent"));
        groupParam.put("ethP2RxBytes", new BasicDBObject("$sum", "$data.ethP2DeltaBytesReceived"));
        groupParam.put("ethP3TxBytes", new BasicDBObject("$sum", "$data.ethP3DeltaBytesSent"));
        groupParam.put("ethP3RxBytes", new BasicDBObject("$sum", "$data.ethP3DeltaBytesReceived"));
        groupParam.put("5gTxBytes", new BasicDBObject("$sum", "$data.deltaBytesReceived5g"));
        groupParam.put("5gRxBytes", new BasicDBObject("$sum", "$data.deltaBytesSent5g"));
        groupParam.put("24gTxBytes", new BasicDBObject("$sum", "$data.deltaBytesReceived24g"));
        groupParam.put("24gRxBytes", new BasicDBObject("$sum", "$data.deltaBytesSent24g"));

        BasicDBObject group = new BasicDBObject();
        group.put("$group", groupParam);

        BasicDBObject project = new BasicDBObject();
        project.put("_id", 0);

        BasicDBObject projection = new BasicDBObject();
        projection.put("$project", project);

        List<BasicDBObject> pipeline = new ArrayList<>();
        pipeline.add(match);
        pipeline.add(unwind);
        pipeline.add(group);
        pipeline.add(projection);

        List<DBObject> dbObjectList = mongoService.aggregationPipeline(pipeline, AP_WIFI_INSIGHTS_PER_MINUTE);

        return dbObjectList.isEmpty() ? null : dbObjectList.get(0);
    }*/

    public MqttClient getMqttClientConnection(String url, String port) throws Exception {
        MqttClient mqttClientConnection;
        mqttClientConnection = new MqttClient(url + ":" + port, CommonUtils.generateUUID());
        return mqttClientConnection;
    }

    public Map<String, Object> getTimeSeriesDataForWifiHealthPerMinute(Equipment userEquipment, String serialNumber) {
        Map<String, Object> awsInsightData = null;
        List<Map<String, Object>> combinedData = new ArrayList<>();
        TimeZone timeZone = TimeZone.getTimeZone("UTC");
        Calendar calendar = Calendar.getInstance(timeZone);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        calendar.add(Calendar.DATE, CALENDER_CRITERIA_YESTERDAY);

        Calendar todayCalendar = Calendar.getInstance(timeZone);
        todayCalendar.set(Calendar.HOUR_OF_DAY, 0);
        todayCalendar.set(Calendar.MINUTE, 0);
        todayCalendar.set(Calendar.SECOND, 0);
        todayCalendar.set(Calendar.MILLISECOND, 0);

        long dateHour = TimeUnit.MILLISECONDS.toSeconds(calendar.getTimeInMillis());

        BasicDBObject dateCriteria = new BasicDBObject();
        dateCriteria.put("$gte", dateHour);

        BasicDBObject mongoFieldOptions = new BasicDBObject();
        mongoFieldOptions.clear();
        mongoFieldOptions.put("_id", 0);

        BasicDBObject matchParam = new BasicDBObject();
        matchParam.put("userId", userEquipment.getRgwSerial());
        matchParam.put("serialNumber", serialNumber);
        matchParam.put("dateHour", dateCriteria);

        List<DBObject> dbObjectList = mongoService.findList(matchParam, null, AP_WIFI_INSIGHTS_PER_MINUTE, mongoFieldOptions);

        if (!dbObjectList.isEmpty()) {
            for (DBObject dbObject : dbObjectList) {
                if (Objects.nonNull(dbObject.get("hourlyData"))) {
                    Map<String, Object> dataByDay = (Map<String, Object>) dbObject.get("hourlyData");
                    for (String key : dataByDay.keySet()) {  // 0 , 1, 2
                        List<Map<String, Object>> apWifiData = (List<Map<String, Object>>) dataByDay.get(key);  // key = 0
                        if (Objects.nonNull(apWifiData)) {
                            combinedData.addAll(apWifiData);
                        }
                    }
                }
            }

            awsInsightData = new HashMap<>();

            Map<String, Object> element = combinedData.parallelStream().sorted((o1, o2) -> Long.compare(Long.valueOf(o2.get("timestamp").toString()), Long.valueOf(o1.get("timestamp").toString()))).findFirst().orElse(null);

            LOG.info("log for apiWifiInsight   ====>> " + element);

//            awsInsightData.put("score", combinedData.parallelStream().mapToDouble(element -> Objects.isNull(element.get("score")) ? 0.0 : (Double) element.get("score")).sum()); // this should also be of latest
//            awsInsightData.put("score", combinedData.parallelStream().sorted((o1, o2) -> Long.compare(Long.valueOf(o2.get("timestamp").toString()), Long.valueOf(o1.get("timestamp").toString()))).mapToDouble(element -> Objects.isNull(element.get("score")) ? 0.0 : (Double) element.get("score")).findFirst().orElse(0.0)); //
            awsInsightData.put("score", Objects.isNull(element.get("wifiHealthScore")) ? 0.0 : (Double) element.get("wifiHealthScore"));

//            awsInsightData.put("wanTxBytes", combinedData.parallelStream().mapToDouble(element -> Objects.isNull(element.get("wanDeltaBytesSent")) ? 0.0 : (Double) element.get("wanDeltaBytesSent")).sum());
//            awsInsightData.put("wanRxBytes", combinedData.parallelStream().mapToDouble(element -> Objects.isNull(element.get("wanDeltaBytesReceived")) ? 0.0 : (Double) element.get("wanDeltaBytesReceived")).sum());
//            awsInsightData.put("wanTxBytes", combinedData.parallelStream().sorted((o1, o2) -> Long.compare(Long.valueOf(o2.get("timestamp").toString()), Long.valueOf(o1.get("timestamp").toString()))).mapToDouble(element -> Objects.isNull(element.get("wanDeltaBytesSent")) ? 0.0 : (Double) element.get("wanDeltaBytesSent")).findFirst().orElse(0.0));
//            awsInsightData.put("wanRxBytes", combinedData.parallelStream().sorted((o1, o2) -> Long.compare(Long.valueOf(o2.get("timestamp").toString()), Long.valueOf(o1.get("timestamp").toString()))).mapToDouble(element -> Objects.isNull(element.get("wanDeltaBytesReceived")) ? 0.0 : (Double) element.get("wanDeltaBytesReceived")).findFirst().orElse(0.0));
            Map<String, Object> wanElement = (Map<String, Object>) element.get("wan");
            awsInsightData.put("wanTxBytes", Objects.isNull(wanElement.get("bytesSent")) ? 0.0 : (Double) wanElement.get("bytesSent"));
            awsInsightData.put("wanRxBytes", Objects.isNull(wanElement.get("bytesReceived")) ? 0.0 : (Double) wanElement.get("bytesReceived"));

//            awsInsightData.put("mocaTxBytes", combinedData.parallelStream().mapToDouble(element -> Objects.isNull(element.get("mocaDeltaBytesSent")) ? 0.0 : (Double) element.get("mocaDeltaBytesSent")).sum());
//            awsInsightData.put("mocaRxBytes", combinedData.parallelStream().mapToDouble(element -> Objects.isNull(element.get("mocaDeltaBytesReceived")) ? 0.0 : (Double) element.get("mocaDeltaBytesReceived")).sum());
//            awsInsightData.put("mocaTxBytes", combinedData.parallelStream().sorted((o1, o2) -> Long.compare(Long.valueOf(o2.get("timestamp").toString()), Long.valueOf(o1.get("timestamp").toString()))).mapToDouble(element -> Objects.isNull(element.get("mocaDeltaBytesSent")) ? 0.0 : (Double) element.get("mocaDeltaBytesSent")).findFirst().orElse(0.0));
//            awsInsightData.put("mocaRxBytes", combinedData.parallelStream().sorted((o1, o2) -> Long.compare(Long.valueOf(o2.get("timestamp").toString()), Long.valueOf(o1.get("timestamp").toString()))).mapToDouble(element -> Objects.isNull(element.get("mocaDeltaBytesReceived")) ? 0.0 : (Double) element.get("mocaDeltaBytesReceived")).findFirst().orElse(0.0));
            Map<String, Object> mocaElement = (Map<String, Object>) element.get("moca");
            Map<String, Object> moca = (Map<String, Object>) mocaElement.get("1");
            if(moca != null) {
                awsInsightData.put("mocaTxBytes", Objects.isNull(moca.get("bytesSent")) ? 0.0 : (Double) moca.get("bytesSent"));
                awsInsightData.put("mocaRxBytes", Objects.isNull(moca.get("bytesReceived")) ? 0.0 : (Double) moca.get("bytesReceived"));
            } else {
                awsInsightData.put("mocaTxBytes", 0.0);
                awsInsightData.put("mocaRxBytes", 0.0);
            }

//            awsInsightData.put("ethP0TxBytes", combinedData.parallelStream().mapToDouble(element -> Objects.isNull(element.get("ethP0DeltaBytesSent")) ? 0.0 : (Double) element.get("ethP0DeltaBytesSent")).sum());
//            awsInsightData.put("ethP0RxBytes", combinedData.parallelStream().mapToDouble(element -> Objects.isNull(element.get("ethP0DeltaBytesReceived")) ? 0.0 : (Double) element.get("ethP0DeltaBytesReceived")).sum());
//            awsInsightData.put("ethP1TxBytes", combinedData.parallelStream().mapToDouble(element -> Objects.isNull(element.get("ethP1DeltaBytesSent")) ? 0.0 : (Double) element.get("ethP1DeltaBytesSent")).sum());
//            awsInsightData.put("ethP1RxBytes", combinedData.parallelStream().mapToDouble(element -> Objects.isNull(element.get("ethP1DeltaBytesReceived")) ? 0.0 : (Double) element.get("ethP1DeltaBytesReceived")).sum());
//            awsInsightData.put("ethP2TxBytes", combinedData.parallelStream().mapToDouble(element -> Objects.isNull(element.get("ethP2DeltaBytesSent")) ? 0.0 : (Double) element.get("ethP2DeltaBytesSent")).sum());
//            awsInsightData.put("ethP2RxBytes", combinedData.parallelStream().mapToDouble(element -> Objects.isNull(element.get("ethP2DeltaBytesReceived")) ? 0.0 : (Double) element.get("ethP2DeltaBytesReceived")).sum());
//            awsInsightData.put("ethP3TxBytes", combinedData.parallelStream().mapToDouble(element -> Objects.isNull(element.get("ethP3DeltaBytesSent")) ? 0.0 : (Double) element.get("ethP3DeltaBytesSent")).sum());
//            awsInsightData.put("ethP3RxBytes", combinedData.parallelStream().mapToDouble(element -> Objects.isNull(element.get("ethP3DeltaBytesReceived")) ? 0.0 : (Double) element.get("ethP3DeltaBytesReceived")).sum());
//            awsInsightData.put("ethP0TxBytes", combinedData.parallelStream().sorted((o1, o2) -> Long.compare(Long.valueOf(o2.get("timestamp").toString()), Long.valueOf(o1.get("timestamp").toString()))).mapToDouble(element -> Objects.isNull(element.get("ethP0DeltaBytesSent")) ? 0.0 : (Double) element.get("ethP0DeltaBytesSent")).findFirst().orElse(0.0));
//            awsInsightData.put("ethP0RxBytes", combinedData.parallelStream().sorted((o1, o2) -> Long.compare(Long.valueOf(o2.get("timestamp").toString()), Long.valueOf(o1.get("timestamp").toString()))).mapToDouble(element -> Objects.isNull(element.get("ethP0DeltaBytesReceived")) ? 0.0 : (Double) element.get("ethP0DeltaBytesReceived")).findFirst().orElse(0.0));
//            awsInsightData.put("ethP1TxBytes", combinedData.parallelStream().sorted((o1, o2) -> Long.compare(Long.valueOf(o2.get("timestamp").toString()), Long.valueOf(o1.get("timestamp").toString()))).mapToDouble(element -> Objects.isNull(element.get("ethP1DeltaBytesSent")) ? 0.0 : (Double) element.get("ethP1DeltaBytesSent")).findFirst().orElse(0.0));
//            awsInsightData.put("ethP1RxBytes", combinedData.parallelStream().sorted((o1, o2) -> Long.compare(Long.valueOf(o2.get("timestamp").toString()), Long.valueOf(o1.get("timestamp").toString()))).mapToDouble(element -> Objects.isNull(element.get("ethP1DeltaBytesReceived")) ? 0.0 : (Double) element.get("ethP1DeltaBytesReceived")).findFirst().orElse(0.0));
//            awsInsightData.put("ethP2TxBytes", combinedData.parallelStream().sorted((o1, o2) -> Long.compare(Long.valueOf(o2.get("timestamp").toString()), Long.valueOf(o1.get("timestamp").toString()))).mapToDouble(element -> Objects.isNull(element.get("ethP2DeltaBytesSent")) ? 0.0 : (Double) element.get("ethP2DeltaBytesSent")).findFirst().orElse(0.0));
//            awsInsightData.put("ethP2RxBytes", combinedData.parallelStream().sorted((o1, o2) -> Long.compare(Long.valueOf(o2.get("timestamp").toString()), Long.valueOf(o1.get("timestamp").toString()))).mapToDouble(element -> Objects.isNull(element.get("ethP2DeltaBytesReceived")) ? 0.0 : (Double) element.get("ethP2DeltaBytesReceived")).findFirst().orElse(0.0));
//            awsInsightData.put("ethP3TxBytes", combinedData.parallelStream().sorted((o1, o2) -> Long.compare(Long.valueOf(o2.get("timestamp").toString()), Long.valueOf(o1.get("timestamp").toString()))).mapToDouble(element -> Objects.isNull(element.get("ethP3DeltaBytesSent")) ? 0.0 : (Double) element.get("ethP3DeltaBytesSent")).findFirst().orElse(0.0));
//            awsInsightData.put("ethP3RxBytes", combinedData.parallelStream().sorted((o1, o2) -> Long.compare(Long.valueOf(o2.get("timestamp").toString()), Long.valueOf(o1.get("timestamp").toString()))).mapToDouble(element -> Objects.isNull(element.get("ethP3DeltaBytesReceived")) ? 0.0 : (Double) element.get("ethP3DeltaBytesReceived")).findFirst().orElse(0.0));
            Map<String, Object> ethElement = (Map<String, Object>) element.get("ethernet");
            Map<String, Object> eth1 = (Map<String, Object>) ethElement.get("1");
            if(eth1 != null) {
                awsInsightData.put("ethP0TxBytes", Objects.isNull(eth1.get("bytesSent")) ? 0.0 : (Double) eth1.get("bytesSent"));
                awsInsightData.put("ethP0RxBytes", Objects.isNull(eth1.get("bytesReceived")) ? 0.0 : (Double) eth1.get("bytesReceived"));
            } else {
                awsInsightData.put("ethP0TxBytes", 0.0);
                awsInsightData.put("ethP0RxBytes", 0.0);
            }

            Map<String, Object> eth2 = (Map<String, Object>) ethElement.get("2");
            if(eth2 != null) {
                awsInsightData.put("ethP1TxBytes", Objects.isNull(eth2.get("bytesSent")) ? 0.0 : (Double) eth2.get("bytesSent"));
                awsInsightData.put("ethP1RxBytes", Objects.isNull(eth2.get("bytesReceived")) ? 0.0 : (Double) eth2.get("bytesReceived"));
            } else {
                awsInsightData.put("ethP1TxBytes", 0.0);
                awsInsightData.put("ethP1RxBytes", 0.0);
            }

            Map<String, Object> eth3 = (Map<String, Object>) ethElement.get("3");
            if(eth3 != null) {
                awsInsightData.put("ethP2TxBytes", Objects.isNull(eth3.get("bytesSent")) ? 0.0 : (Double) eth3.get("bytesSent"));
                awsInsightData.put("ethP2RxBytes", Objects.isNull(eth3.get("bytesReceived")) ? 0.0 : (Double) eth3.get("bytesReceived"));
            } else {
                awsInsightData.put("ethP2TxBytes", 0.0);
                awsInsightData.put("ethP2RxBytes", 0.0);
            }

            Map<String, Object> eth4 = (Map<String, Object>) ethElement.get("4");
            if(eth4 != null) {
                awsInsightData.put("ethP3TxBytes", Objects.isNull(eth4.get("bytesSent")) ? 0.0 : (Double) eth4.get("bytesSent"));
                awsInsightData.put("ethP3RxBytes", Objects.isNull(eth4.get("bytesReceived")) ? 0.0 : (Double) eth4.get("bytesReceived"));
            } else {
                awsInsightData.put("ethP3TxBytes", 0.0);
                awsInsightData.put("ethP3RxBytes", 0.0);
            }


            //TODO ask from ETL
//            awsInsightData.put("5gTxBytes", combinedData.parallelStream().mapToDouble(element -> Objects.isNull(element.get("deltaBytesReceived5g")) ? 0.0 : (Double) element.get("deltaBytesReceived5g")).sum());
//            awsInsightData.put("5gRxBytes", combinedData.parallelStream().mapToDouble(element -> Objects.isNull(element.get("deltaBytesSent5g")) ? 0.0 : (Double) element.get("deltaBytesSent5g")).sum());
//            awsInsightData.put("24gTxBytes", combinedData.parallelStream().mapToDouble(element -> Objects.isNull(element.get("deltaBytesReceived24g")) ? 0.0 : (Double) element.get("deltaBytesReceived24g")).sum());
//            awsInsightData.put("24gRxBytes", combinedData.parallelStream().mapToDouble(element -> Objects.isNull(element.get("deltaBytesSent24g")) ? 0.0 : (Double) element.get("deltaBytesSent24g")).sum());
//            awsInsightData.put("5gTxBytes", combinedData.parallelStream().sorted((o1, o2) -> Long.compare(Long.valueOf(o2.get("timestamp").toString()), Long.valueOf(o1.get("timestamp").toString()))).mapToDouble(element -> Objects.isNull(element.get("deltaBytesReceived5g")) ? 0.0 : (Double) element.get("deltaBytesReceived5g")).findFirst().orElse(0.0));
//            awsInsightData.put("5gRxBytes", combinedData.parallelStream().sorted((o1, o2) -> Long.compare(Long.valueOf(o2.get("timestamp").toString()), Long.valueOf(o1.get("timestamp").toString()))).mapToDouble(element -> Objects.isNull(element.get("deltaBytesSent5g")) ? 0.0 : (Double) element.get("deltaBytesSent5g")).findFirst().orElse(0.0));
//            awsInsightData.put("24gTxBytes", combinedData.parallelStream().sorted((o1, o2) -> Long.compare(Long.valueOf(o2.get("timestamp").toString()), Long.valueOf(o1.get("timestamp").toString()))).mapToDouble(element -> Objects.isNull(element.get("deltaBytesReceived24g")) ? 0.0 : (Double) element.get("deltaBytesReceived24g")).findFirst().orElse(0.0));
//            awsInsightData.put("24gRxBytes", combinedData.parallelStream().sorted((o1, o2) -> Long.compare(Long.valueOf(o2.get("timestamp").toString()), Long.valueOf(o1.get("timestamp").toString()))).mapToDouble(element -> Objects.isNull(element.get("deltaBytesSent24g")) ? 0.0 : (Double) element.get("deltaBytesSent24g")).findFirst().orElse(0.0));
            Map<String, Object> wifi5gElement = (Map<String, Object>) ((Map<String, Object>) element.get("radio")).get("5g");
            awsInsightData.put("5gTxBytes", Objects.isNull(wifi5gElement.get("avgBytesSent")) ? 0.0 : (Double) wifi5gElement.get("avgBytesSent"));
            awsInsightData.put("5gRxBytes", Objects.isNull(wifi5gElement.get("avgBytesReceived")) ? 0.0 : (Double) wifi5gElement.get("avgBytesReceived"));
            Map<String, Object> wifi2gElement = (Map<String, Object>) ((Map<String, Object>) element.get("radio")).get("2g");
            awsInsightData.put("24gTxBytes", Objects.isNull(wifi2gElement.get("avgBytesSent")) ? 0.0 : (Double) wifi2gElement.get("avgBytesSent"));
            awsInsightData.put("24gRxBytes", Objects.isNull(wifi2gElement.get("avgBytesReceived")) ? 0.0 : (Double) wifi2gElement.get("avgBytesReceived"));
        }
        return awsInsightData;
    }

    public String getIspByGroupId(String groupId) throws Exception {
        Compartment compartment = (Compartment) dataAccessService.read(Compartment.class, groupId);
        return compartment.getIspId();
    }

    public String getGroupIdByIspId(String ispId) throws Exception {
        String groupId = null;
        HashMap<String, String> params = new HashMap<>();
        params.put("ispId", ispId);
        List<Compartment> compartmentList = (List<Compartment>) dataAccessService.read(Compartment.class, GroupISPSQL.GET_COMPARTMENT_BY_ISP_ID, params);

        if (Objects.nonNull(compartmentList) && !compartmentList.isEmpty())
            groupId = compartmentList.get(0).getId();

        return groupId;
    }

    private String sanitizeCSVLine(String input) {
        return input.replaceAll("<", "&lt;")
                .replaceAll(">", "&gt;")
                .replaceAll("&", "&amp;")
                .replaceAll("\"", "&quot;")
                .replaceAll("'", "&#x27;");
    }

    public boolean validateCSVFile(MultipartFile file) throws Exception {
        InputStream is = file.getInputStream();
        BufferedReader br = new BufferedReader(new InputStreamReader(is));
        String line;

        while ((line = br.readLine()) != null) {
            if (line.trim().length() > 1 && !line.trim().contains(ApplicationConstants.COMMA)) {
                return true;
            }

            String sanitizedLine = sanitizeCSVLine(line);
            if (!line.equals(sanitizedLine)) {
                return true;
            }
        }

        return false;
    }

    public long countNoOfRecord(MultipartFile file) throws Exception {
        // first line is title, no count
        long count = -1;
        InputStream is = file.getInputStream();
        BufferedReader br = new BufferedReader(new InputStreamReader(is));
        String line;
        while ((line = br.readLine()) != null) {
            if (line.trim().length() > 1) {
                count++;
            }
        }

        return count;
    }

    public void publishSubscriber(DBObject file) throws Exception {
        long count = 0;
        List<HashMap<String, Object>> invalidData = new ArrayList<>();

        URL url = new URL(String.valueOf(file.get("url")));
        String fileName = "/tmp/" + "subscriber";

        File fileOnDisk = new File(fileName);
        FileUtils.copyInputStreamToFile(url.openStream(), fileOnDisk);

        BufferedReader br = new BufferedReader(new FileReader(fileOnDisk));
        String line;
        while ((line = br.readLine()) != null) {
            if (line.trim().length() > 1) {
                count++;

                String[] details = line.trim().split(COMMA);

                BasicDBObject updateData = new BasicDBObject();
                updateData.put("noOfRecordScanned", count);

                BasicDBObject updateMap = new BasicDBObject();
                updateMap.put("$set", updateData);
                mongoService.update(new BasicDBObject("id", file.get("id")), updateMap, false, false, SUBSCRIBERS_DATA_FILE_DETAILS);

                if (details.length < 7) {
                    DBObject fileDetails = mongoService.findOne(SUBSCRIBERS_DATA_FILE_DETAILS, new BasicDBObject("id", file.get("id")));
                    LinkedHashMap<String, Object> invalidRecord = new LinkedHashMap<>();
                    invalidRecord.put("lineNo", count);
                    invalidRecord.put("record", line);
                    invalidRecord.put("remark", "CSV parsing failed, contains less than 7 CSV");

                    invalidData.add(invalidRecord);

                    BasicDBObject recordToUpdate = new BasicDBObject();
                    recordToUpdate.put("invalidRecord", Long.valueOf(fileDetails.get("invalidRecord").toString()) + 1);

                    BasicDBObject update = new BasicDBObject();
                    update.put("$set", recordToUpdate);
                    mongoService.update(new BasicDBObject("id", file.get("id")), update, false, true, SUBSCRIBERS_DATA_FILE_DETAILS);
                    continue;
                }

                HashMap<String, Object> data = new HashMap<>();
                data.put("message", line);
                data.put("url", file.get("url"));
                data.put("id", file.get("id"));
                data.put("isp", file.get("isp"));
                data.put("count", count);

                HashMap<String, Object> publishData = new HashMap<>();
                publishData.put("data", data);
                kafkaPublisher.publishToKafka(publishData, "SUBSCRIBER_BULK_UPDATE");

                BasicDBObject update = new BasicDBObject();
                update.put("$set", new BasicDBObject("status", _SUBSCRIBER_BULK_STATUS_INPROGRESS).append("processStarted", Calendar.getInstance().getTimeInMillis()));
                mongoService.update(new BasicDBObject("id", file.get("id")), update, false, false, SUBSCRIBERS_DATA_FILE_DETAILS);
            }
        }

        BasicDBObject recordToUpdate = new BasicDBObject();
        BasicDBObject update = new BasicDBObject();

        /*String processingStatus = _SUBSCRIBER_BULK_STATUS_COMPLETED;
        recordToUpdate.put("status", processingStatus);
        recordToUpdate.put("processCompleted", Calendar.getInstance().getTimeInMillis());


        update.put("$set", recordToUpdate);
        mongoService.update(new BasicDBObject("id", file.get("id")), update, false, false, SUBSCRIBERS_DATA_FILE_DETAILS);
*/
        if (!invalidData.isEmpty()) {
            String downloadURL = createEXCELReportForInvalidCSV(invalidData);
            recordToUpdate.put("urlForInvalidRecord", downloadURL);

            update.put("$set", recordToUpdate);
            mongoService.update(new BasicDBObject("id", file.get("id")), update, false, false, SUBSCRIBERS_DATA_FILE_DETAILS);
        }
    }

    public String createEXCELReportForInvalidCSV(List<HashMap<String, Object>> failedRecords) {
        String downloadURL = ApplicationCommonConstants.EMPTY_STRING;
        String[] headerColumns = {"LINE NO", "CSV", "REMARKS"};

        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("invalidCSV");

        /*HeaderText*/
        // Create a Font for styling header cells
        Font headingFont = workbook.createFont();
        headingFont.setBold(true);
        headingFont.setFontHeightInPoints((short) 14);
        headingFont.setColor(IndexedColors.BLACK.getIndex());
        // Create a CellStyle with the font
        CellStyle headingTextCellStyle = workbook.createCellStyle();
        headingTextCellStyle.setFont(headingFont);
        headingTextCellStyle.setIndention((short) 5);

        // Create a Row
        Row headerRow = sheet.createRow(0);
        // Create a Font for styling header cells
        Font headerFont = workbook.createFont();
        headerFont.setBold(true);
        headerFont.setFontHeightInPoints((short) 8);
        headerFont.setColor(IndexedColors.BLACK.getIndex());

        // Create a CellStyle with the font
        CellStyle headerCellStyle = workbook.createCellStyle();
        headerCellStyle.setFont(headerFont);
        // Create cells
        for (int i = 0; i < headerColumns.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headerColumns[i]);
            cell.setCellStyle(headerCellStyle);
        }

        // Create Other rows and cells with Job data
        int noOfRows = 1;
        for (HashMap<String, Object> data : failedRecords) {
            Row row = sheet.createRow(noOfRows++);

            row.createCell(0)
                    .setCellValue(String.valueOf(data.get("lineNo")));

            row.createCell(1)
                    .setCellValue(String.valueOf(data.get("record")));

            row.createCell(2)
                    .setCellValue(String.valueOf(data.get("remark")));
        }

        // Resize all columns to fit the content size
        for (int i = 0; i < headerColumns.length; i++) {
            sheet.autoSizeColumn(i);
        }

        String humanReadableReportName = SUB_INVALID_DATA_REPORT + "_" + Calendar.getInstance().getTimeInMillis() + ".xlsx";
        String tempFileName = "/tmp/" + humanReadableReportName;

        try {
            FileOutputStream fileOut = new FileOutputStream(tempFileName);
            workbook.write(fileOut);
            fileOut.close();
            workbook.close();

            File fileOnDisk = new File(tempFileName);
            Map<?, ?> resp = s3Service.uploadFile(fileOnDisk, SUB_DATA_REPORT + "/" + humanReadableReportName);
            downloadURL = resp.get("secure_url").toString();
            fileOnDisk.delete();
        } catch (Exception e) {
            LOG.info("Error while creating excel file :: " + e.getMessage());
        }

        LOG.info("Report DownLoad URL :: " + downloadURL);
        return downloadURL;
    }

    public String getTimeTakenToProcess(long seconds) {
        String time = HYPHEN_STRING;
        int day = (int) TimeUnit.SECONDS.toDays(seconds);
        long hours = TimeUnit.SECONDS.toHours(seconds) - (day * 24);
        long minute = TimeUnit.SECONDS.toMinutes(seconds) - (TimeUnit.SECONDS.toHours(seconds) * 60);
        long second = TimeUnit.SECONDS.toSeconds(seconds) - (TimeUnit.SECONDS.toMinutes(seconds) * 60);

        time = day + " Day " + hours + " h " + minute + " m " + second + " s";
        return time;
    }

    public long convertMinutesToDateHour(long minutes) {
        Calendar now = Calendar.getInstance();
        now.add(Calendar.MINUTE, -(int) (minutes));
        now.set(Calendar.HOUR_OF_DAY, 0);
        now.set(Calendar.MINUTE, 0);
        now.set(Calendar.SECOND, 0);
        now.set(Calendar.MILLISECOND, 0);
        long dateHour = TimeUnit.MILLISECONDS.toSeconds(now.getTimeInMillis());
        return dateHour;
    }

    public long convertMinutesToDateHourSeconds(long minutes) {
        Calendar now = Calendar.getInstance();
        long dateHour = TimeUnit.MILLISECONDS.toSeconds(now.getTimeInMillis());
        dateHour -= (minutes * 60);
        return dateHour;
    }

    public User convertToUser(Map<String, Object> userData) throws Exception {
        User user = new User();
        user.setId(String.valueOf(userData.get("id")));
        user.setEmail(String.valueOf(userData.get("email")));
        user.setFirstName(String.valueOf(userData.get("firstName")));
        user.setLastName(String.valueOf(userData.get("lastName")));
        user.setInternalUser(Objects.nonNull(userData.get("internalUser")) ? Boolean.valueOf(userData.get("internalUser").toString()) : false);
        return user;
    }

    public void validateSubscriber() throws Exception {
        UserContext userContext = ExecutionContext.get().getUsercontext();
        if (Objects.isNull(userContext))
            throw new AuthEntityNotAllowedException(ApiResponseCode.RESOURCE_NOT_ALLOWED);
    }

    public HashMap<String, Object> prepareDataForES(Equipment equipment) {
        List<String> clusters = new ArrayList<>();
        HashMap<String, Object> userAPMap = null;
        try {
            Compartment compartment = (Compartment) dataAccessService.read(Compartment.class, equipment.getGroupId());
            ClusterInfo clusterInfo = compartment.getCluster().stream().filter(cluster -> cluster.isDefaultCluster() && cluster.getName().equals(DEFAULT + compartment.getName())).findAny().orElse(null);
            if (Objects.nonNull(clusterInfo)) {
                clusters.add(clusterInfo.getId());
            }

            HashMap<String, Object> queryParams = new HashMap<>();
            queryParams.put("userId", equipment.getRgwSerial());
            HashMap<String, Object> projection = new HashMap<>();
            projection.put("type", 1);
            projection.put("serialNumber", 1);
            projection.put("macAddress", 1);
            List<HashMap<String, String>> apDetails = mongoService.findList(queryParams, new HashMap<>(), ApplicationCommonConstants.AP_DETAIL, projection);
            List<Map<String, Object>> extInfo = new ArrayList<>();
            apDetails.stream().filter(ap -> !ApplicationCommonConstants.GATEWAY.equals(ap.get("type"))).forEach(elem -> {
                Map<String, Object> extender = new HashMap<>();
                extender.put(EXT_MAC, elem.get("macAddress"));
                extender.put(EXT_SERIAL, elem.get("serialNumber"));
                extInfo.add(extender);
            });

            userAPMap = new HashMap<>();
            userAPMap.put("id", equipment.getId());
            userAPMap.put("name", equipment.getRgwSerial() + ApplicationCommonConstants.EMPTY_STRING + equipment.getRgwMAC());
            userAPMap.put("createdAt", equipment.getCreatedAt());
            userAPMap.put("createdBy", equipment.getCreatedBy());
            userAPMap.put("email", equipment.getRgwSerial() + "@optim.com");
            userAPMap.put("downLinkRate", equipment.getDownLinkRate());
            userAPMap.put("upLinkRate", equipment.getUpLinkRate());
            userAPMap.put("rgwMAC", equipment.getRgwMAC());
            userAPMap.put("apId", equipment.getRgwSerial());
            userAPMap.put("serialNumber", equipment.getRgwSerial());
            userAPMap.put("groupId", equipment.getGroupId());
            userAPMap.put("globalAccountNo", null);
            userAPMap.put("camsAccountNo", null);
            userAPMap.put("extInfo", extInfo);
            userAPMap.put("clusters", clusters);
            userAPMap.put("serviceTelephoneNo", equipment.getServiceTelephoneNo());
        } catch (Exception e) {
            LOG.error("ERROR in prepare data for ES.");
            throw new ApiException(ApiResponseCode.ERROR_PROCESSING_REQUEST);
        }

        return userAPMap;
    }


    public <T> T requireNonNull(T obj, int code, String msg) {
        if (obj == null)
            throw new ValidationException(code, msg);
        return obj;
    }


    public <T> T requireNonNull(T obj, String msg) {
        if (obj == null)
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), msg);
        return obj;
    }


    public <T> T requireNonNullAndNonEmpty(T obj, int code, String msg) {
        if (obj != null && !(obj instanceof List || obj instanceof String)) {
            throw new ValidationException(code, "This function only work for either List or String Objects");
        }

        if (obj == null || (obj instanceof List ? ((List) obj).isEmpty() : obj.toString().isEmpty()))
            throw new ValidationException(code, msg);
        return obj;
    }


    public <T> T requireNonNullAndNonEmpty(T obj, String msg) {
        if (obj != null && !(obj instanceof List || obj instanceof String)) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "This function only work for either List or String Objects");
        }

        if (obj == null || (obj instanceof List ? ((List) obj).isEmpty() : obj.toString().isEmpty()))
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), msg);
        return obj;
    }


    public void isExtenderOnline(String userId, String serialNumber) {
        BasicDBObject mongoFieldOptions = new BasicDBObject();
        mongoFieldOptions.clear();
        mongoFieldOptions.put("timestamp", 1);
        mongoFieldOptions.put("sendingTime", 1);
        mongoFieldOptions.put("etlVersion", 1);


        HashMap<String, String> queryParams = new HashMap<>();
        HashMap<String, String> appendableParams = new HashMap<>();
        queryParams.put("userId", userId);
        queryParams.put("serialNumber", serialNumber);
        DBObject aPDetails = mongoService.findOne(queryParams, appendableParams, AP_DETAIL, TIMESTAMP, DESC, mongoFieldOptions);
        if (Objects.isNull(aPDetails)) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Serial Number: " + serialNumber + " does not belong to Subscriber with Id: " + userId);
        }
        long currentTime = Calendar.getInstance().getTimeInMillis();
        long lastMetricReceived = Long.valueOf(String.valueOf(("v3".equalsIgnoreCase(String.valueOf(aPDetails.get("etlVersion"))) || Objects.isNull(aPDetails.get("sendingTime"))) ? aPDetails.get("timestamp") : aPDetails.get("sendingTime")));
        long timeDiff = currentTime - lastMetricReceived;

        HashMap<String, String> commonProps = commonService.read(COMMON_CONFIG);
        int equipmentOfflineTime = 7;
        try {
            equipmentOfflineTime = Integer.valueOf(commonProps.get(COMMON_DEVICE_STATUS_RETENTION_TIME));
        } catch (Exception e) {

        }

        if (timeDiff > (equipmentOfflineTime * 60 * 1000)) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Cannot perform operation, Equipment is offline.");
        }

    }
    public void checkEquipmentOffline(String userId, String serial) throws Exception {

        BasicDBObject query = new BasicDBObject();
        query.put("userId", userId);
        query.put("serialNumber", serial);

        DBObject apDetail = mongoService.findOne(AP_DETAIL, query);
        if (true == getCurrentDevStatus(apDetail)) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "The equipment is offline.");
        }
    }

    public void checkIspIdForGroupAdmin(String queryIspId) throws Exception {
        String userGroupId = CommonUtils.getGroupIdOfLoggedInUser();
        String userIspId = getIspByGroupId(userGroupId);
        if (StringUtils.isEmpty(userIspId)) {
            throw new ValidationException(HttpStatus.FORBIDDEN.value(), "Insufficient permissions to access this resource");
        }

        if (StringUtils.isNotEmpty(queryIspId) && !StringUtils.equals(userIspId, queryIspId)) {
            throw new ValidationException(HttpStatus.FORBIDDEN.value(), "Insufficient permissions to access this resource");
        }
    }
}
