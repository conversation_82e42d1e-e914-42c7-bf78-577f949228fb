package com.incs83.app.constants.queries;

public interface NCSEquipmentSQL {
    String GET_NCS_EQUIPMENT_BY_SERIAL_LIST = "from NCSEquipment where serial in :serialList";
    String GET_NCS_EQUIPMENT_BY_ISP_ID = "from NCSEquipment where ispId= :ispId";
    String GET_NCS_EQUIPMENT_BY_NETWORK_ID = "from NCSEquipment where networkId = :networkId";
    String GET_NCS_EQUIPMENT_BY_SERIAL = "from NCSEquipment where serial = :serial";
    String GET_NCS_EQUIPMENT_BY_ID = "from NCSEquipment where id = :id";
    String CLEAR_EQUIPMENT_NETWORK_BY_NETWORK_ID = "UPDATE ncs_equipment SET network_id = NULL, in_use = 0 WHERE network_id = :networkId";
    String CLEAR_EQUIPMENT_SUBSCRIBER_BY_SUBSCRIBER_ID = "UPDATE ncs_equipment SET subscriber_id = NULL WHERE subscriber_id = :subscriberId";
    String GET_ALL_EQUIPMENTS = "SELECT ne.id, ne.serial, ne.mac, ne.model_name modelName, ne.product_id productId, ne.in_use inUse, ne.updatedAt, " +
            "ne.isp_id ispId, ne.network_id networkId, " +
            "er.broker_info_id brokerInfoId " +
            "FROM ncs_equipment ne " +
            "LEFT JOIN equipment_redirect er ON er.equipment_id = ne.id";
    String COUNT_ALL_EQUIPMENTS = "SELECT count(*) FROM ncs_equipment ne";
    String UPDATE_NETWORK_AND_SUBSCRIBER_BY_ID_LIST_AND_ISP_ID = "update NCSEquipment set networkId = :networkId, subscriberId = :subscriberId where ispId = :ispId and id in :equipmentIdList";
}
