package com.incs83.app.constants.misc;

public interface RpcConstants {

    String NETWORK_ACTION_URI = "/cpe-api/network/actions";
    String EQUIPMENT_ACTION_URI = "/cpe-api/equipment/actions";
    String DEVICE_ACTION_URI = "/cpe-api/devices/%s/actions";
    String DEVICE_INTERETACCESS_ACTION_URI = "/cpe-api/devices/%s/internetAccess/action";
    String DEVICE_SPEEDTEST_URI = "/cpe-api/diag/%s/phyRate";
    String RADIO_URI = "/cpe-api/radios/%s";
    String RADIO_ACTION_URI = "/cpe-api/radios/%s/actions";
    String SSID_ACTION_URI = "/cpe-api/radios/%s/ssids/%s";
    String STEERING_URI = "/cpe-api/steering";
    String STEERING_URLES_URI = "/cpe-api/steering/rules";
    String DIAG_ACTION_URI = "/cpe-api/diag/actions";
    String BURST_MODE_URI = "/cpe-api/diag/burstMode";
    String DM_ACCESS_URI = "/cpe-acs/dmaccess";
    String REMOTE_ACCESS_HTTP_URI = "/cpe-api/remoteAccess/http";
    String REMOTE_ACCESS_RTTY_URI = "/cpe-api/remoteAccess/rtty";
    String REMOTE_DEBUG_RTTY_URI = "/cpe-api/remoteDebug/rtty";

    String HOUSEHOLD_MGR_URI = "/cpe-api/equipment/onboarding";
    String ACTION_WAN_SPEED_TEST = "WANSpeedTest";
    String ACTION_KICK_WIFI_STA = "kickWiFiSta";
    String ACTION_NEIGHBOR_SCAN = "NeighborScan";
    String ACTION_WIFI_RESET = "Reset";
    String ACTION_DO_ACS = "DoACS";
    String ACTION_REBOOT = "reboot";

    String CPE_API_METHOD_POST = "POST";
    String CPE_API_METHOD_GET = "GET";
    String CPE_API_METHOD_DELETE = "DELETE";
    String NCS_NETWORK_CPE_URI = "/cpe-api/ncs-cpe-api";

    String FW_URL = "/cpe-acs/firmwares";
    String FW_ACTIONS_URL = "/cpe-acs/firmwares/actions";

    String CONTAINER_URL = "/cpe-container/containers";

    // claim network blink api
    String CPE_API_EQUIPMENT_BLINK = "/cpe-api/equipment/blink";
    // disclaim netowrk api
    String CPE_API_EQUIPMENT_ON_BOARDING_REGISTER = "/cpe-api/equipment/onboardingRegister";
    // extender pairing
    String CPE_API_EXTENDER_PAIRING = "/cpe-api/network/extenderPairing";

    // NCS-CPE-API path
    String NCS_CPE_API_INTERNET_CONNECTION = "/api/v1/data/internetConnection";
    String NCS_CPE_API_DEVICES = "/api/v1/data/devices";
}
