package com.incs83.app.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ApiResponseEnum {
    SUCCESS(200, "Success"),
    
    INTERNAL_SERVER_ERROR(500, "Internal Server Error"),
    
    CUSTOMER_PROFILE_CONFLICT(600, "Duplicated combination of ISP ID and Model ID"),
    // Invalid
    CUSTOMER_PROFILE_INVALID_PROFILE_ID(601, "Profile ID does not empty"),
    CUSTOMER_PROFILE_INVALID_ISP_ID(602, "ISP ID does not exist"),
    CUSTOMER_PROFILE_INVALID_MODEL_ID(603, "Model ID does not exist"),
    CUSTOMER_PROFILE_INVALID_FW_ID(604, "FW ID does not exist"),
    CUSTOMER_PROFILE_NOT_FOUND(605, "No customer profiles found");

    private final int code;
    private final String message;

}