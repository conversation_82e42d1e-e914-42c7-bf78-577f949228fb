package com.incs83.app.enums;

import org.springframework.http.HttpStatus;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ErrorResponseEnum {
    INTERNAL_SERVER_ERROR(500, "Internal Server Error"),

    // Customer Profile
    CUSTOMER_PROFILE_CONFLICT(600, "Duplicated combination of ISP ID and Model ID"),
    CUSTOMER_PROFILE_INVALID_PROFILE_ID(601, "Profile ID does not empty"),
    CUSTOMER_PROFILE_INVALID_ISP_ID(602, "ISP ID does not exist"),
    CUSTOMER_PROFILE_INVALID_MODEL_ID(603, "Model ID does not exist"),
    CUSTOMER_PROFILE_INVALID_FW_ID(604, "FW ID does not exist"),
    CUSTOMER_PROFILE_NOT_FOUND(605, "No customer profiles found"),

    // Network
    NETWORK_NOT_FOUND(HttpStatus.NOT_FOUND.value(), "Network not found for networkId: %s"),
    NETWORK_GATEWAY_SERIAL_NOT_FOUND(HttpStatus.NOT_FOUND.value(), "Current network cannot find gateway serial, networkId: %s"),

    // ncs_equipment api
    NCS_EQUIPMENT_NOT_FOUND(HttpStatus.NOT_FOUND.value(), "Equipment not found for serial: %s"),
    NETWORK_EQUIPMENT_INVALID_ACTION(623, "Invalid action: %s"),

    // extender pairing api
    NCS_EQUIPMENT_DPP_KEY_NOT_FOUND(HttpStatus.NOT_FOUND.value(), "DPP key not found for serial: %s"),

    // equipment_model api,
    NCS_EQUIPMENT_STATUS_DOWN(624, "Equipment status is not up");

    // no use
//    NCS_EQUIPMENT_NOT_UNDER_NETWORK(HttpStatus.CONFLICT.value(), "Equipment is not under the specified network");
//    NCS_EQUIPMENT_MODEL_NOT_FOUND(HttpStatus.NOT_FOUND.value(), "Equipment model not found"),

    private final int code;
    private final String message;

    /**
     * Format the message with dynamic parameters
     * @param params Parameters to insert into the message
     * @return Formatted message string
     */
    public String formatMessage(Object... params) {
        return String.format(this.message, params);
    }

}