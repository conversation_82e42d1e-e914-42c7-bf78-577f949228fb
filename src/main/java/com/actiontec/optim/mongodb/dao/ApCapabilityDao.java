package com.actiontec.optim.mongodb.dao;

import com.actiontec.optim.mongodb.dto.ApCapabilityDto;
import com.actiontec.optim.platform.adapter.At3Adapter;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.mongodb.BasicDBObject;
import com.mongodb.DBCollection;
import com.mongodb.DBObject;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.erdtman.jcs.JsonCanonicalizer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;

// todo The format of the apCapability Collection may change in the future; currently, it uses mockup data.
@Component
public class ApCapabilityDao {
    public static final String TEMPLATES_OPTIM_CAPABILITY_DEFAULT_JSON = "/templates/optim_capability_default.json";
    private final Logger logger = LogManager.getLogger(this.getClass());

    @Autowired
    private At3Adapter at3Adapter;

    @Autowired
    private ObjectMapper objectMapper;

    /**
     * Find capability data with specific projection for security modes
     */
    public DBObject findSecurityModesSupported() {
        logger.debug("findSecurityModesSupported");
        
        BasicDBObject query = new BasicDBObject();
        BasicDBObject projection = new BasicDBObject();
        projection.put("_id", 0);
        projection.put("optim.equipmentCapabilities.radios.2g.supportedSecurity", 1);
        
        DBCollection dbCollection = at3Adapter.getMongoDbCollection(ApCapabilityDto.COLLECTION_apCapability);
        DBObject dbObject = dbCollection.findOne(query, projection);
        
        return dbObject;
    }

    /**
     * Create capability data from template
     */
    public void createCapabilityFromTemplate() {
        logger.debug("createCapabilityFromTemplate");
        
        ObjectNode template = fetchCapabilityTemplate();
        if (Objects.isNull(template)) {
            throw new RuntimeException("No capability template found");
        }
        
        ObjectNode templateCopy = template.deepCopy();
        String canonicalPayload = canonicalizeJson(templateCopy);

        try {
            // Create HashMap for insertion
            HashMap<String, Object> dataMap = objectMapper.readValue(canonicalPayload, HashMap.class);
            
            DBCollection dbCollection = at3Adapter.getMongoDbCollection(ApCapabilityDto.COLLECTION_apCapability);
            BasicDBObject document = new BasicDBObject(dataMap);
            dbCollection.insert(document);
            
            logger.info("Successfully created capability data from template");
        } catch (Exception e) {
            logger.error("Failed to create capability data from template", e);
            throw new RuntimeException("Failed to create capability data", e);
        }
    }

    /**
     * Check if capability data exists
     */
    public boolean exists() {
        logger.debug("checking if capability data exists");
        
        BasicDBObject query = new BasicDBObject();
        
        DBCollection dbCollection = at3Adapter.getMongoDbCollection(ApCapabilityDto.COLLECTION_apCapability);
        long count = dbCollection.count(query);
        
        return count > 0;
    }

    /**
     * Extract security modes supported from MongoDB apCapability result
     */
    public List<String> extractSecurityModesSupported(DBObject apCapability) {
        if (Objects.isNull(apCapability)) {
            return Collections.emptyList();
        }
        
        try {
            DBObject optim = (DBObject) apCapability.get("optim");
            if (Objects.isNull(optim)) {
                return Collections.emptyList();
            }
            
            DBObject equipmentCapabilities = (DBObject) optim.get("equipmentCapabilities");
            if (Objects.isNull(equipmentCapabilities)) {
                return Collections.emptyList();
            }
            
            DBObject radios = (DBObject) equipmentCapabilities.get("radios");
            if (Objects.isNull(radios)) {
                return Collections.emptyList();
            }
            
            DBObject radio2g = (DBObject) radios.get("2g");
            if (Objects.isNull(radio2g)) {
                return Collections.emptyList();
            }

            Object supportedSecurityObj = radio2g.get("supportedSecurity");
            if (supportedSecurityObj instanceof List) {
                return (List<String>) supportedSecurityObj;
            }
            return Collections.emptyList();
        } catch (Exception e) {
            logger.error("Failed to extract security modes supported", e);
            return Collections.emptyList();
        }
    }

    /**
     * Get supported security modes, creating default data if not exists
     *
     * @return List of supported security modes
     * @throws Exception if retrieval fails
     */
    public List<String> getSecurityModesSupported() throws Exception {
        DBObject apCapability = findSecurityModesSupported();

        // If no capability data exists, create from template
        if (Objects.isNull(apCapability)) {
            logger.info("No apCapability data found, creating from template");
            createCapabilityFromTemplate();
            apCapability = findSecurityModesSupported();
        }

        // Extract security modes
        return extractSecurityModesSupported(apCapability);
    }

    // Private helper methods
    private ObjectNode fetchCapabilityTemplate() {
        try (InputStream in = getClass().getResourceAsStream(TEMPLATES_OPTIM_CAPABILITY_DEFAULT_JSON)) {
            if (Objects.isNull(in)) {
                return null;
            }
            return (ObjectNode) objectMapper.readTree(in);
        } catch (Exception e) {
            logger.error("Failed to load capability template", e);
            return null;
        }
    }

    private String canonicalizeJson(ObjectNode objectNode) {
        try {
            String jsonString = objectMapper.writeValueAsString(objectNode);
            JsonCanonicalizer jsonCanonicalizer = new JsonCanonicalizer(jsonString);
            return jsonCanonicalizer.getEncodedString();
        } catch (Exception e) {
            throw new RuntimeException("Failed to canonicalize JSON", e);
        }
    }
}
