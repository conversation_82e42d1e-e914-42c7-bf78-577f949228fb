package com.actiontec.optim.mongodb.dao;

import com.actiontec.optim.mongodb.dto.WanSpeedTestDto;
import com.actiontec.optim.platform.adapter.At3Adapter;
import com.actiontec.optim.platform.api.v6.enums.NcsCpeSpeedTestStatus;
import com.actiontec.optim.platform.constant.ApplicationConstants;
import com.incs83.util.CommonUtils;
import com.mongodb.BasicDBObject;
import com.mongodb.DBCollection;
import com.mongodb.DBCursor;
import com.mongodb.DBObject;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.util.*;

@Component
public class WanSpeedTestDao {
    private final Logger logger = LogManager.getLogger(this.getClass());

    @Autowired
    private At3Adapter at3Adapter;

    /**
     * Create and save a new pending speed test record
     */
    public void createPendingRecordWithRecordIdAndUserIdAndSerialNumber(String recordId, String userId, String serialNumber) {
        logger.debug("createPendingRecordWithRecordIdAndUserIdAndSerialNumber. recordId:[{}], userId:[{}], serialNumber:[{}]",
                recordId, userId, serialNumber);

        String id = CommonUtils.generateUUID();
        BasicDBObject data = new BasicDBObject();
        data.put(WanSpeedTestDto.WanSpeedTestFields._id.name(), id);
        data.put(WanSpeedTestDto.WanSpeedTestFields.userId.name(), userId);
        data.put(WanSpeedTestDto.WanSpeedTestFields.serialNumber.name(), serialNumber);
        data.put(WanSpeedTestDto.WanSpeedTestFields.testServer.name(), StringUtils.EMPTY);
        data.put(WanSpeedTestDto.WanSpeedTestFields.date.name(), new Date());
        data.put(WanSpeedTestDto.WanSpeedTestFields.result.name(), StringUtils.EMPTY);
        data.put(WanSpeedTestDto.WanSpeedTestFields.status.name(), NcsCpeSpeedTestStatus.Pending.name());
        data.put(WanSpeedTestDto.WanSpeedTestFields.vmqStatus.name(), new BasicDBObject());
        data.put(WanSpeedTestDto.WanSpeedTestFields.downloadSpeed.name(), 0.0);
        data.put(WanSpeedTestDto.WanSpeedTestFields.uploadSpeed.name(), 0.0);
        data.put(WanSpeedTestDto.WanSpeedTestFields.latency.name(), 0);
        data.put(WanSpeedTestDto.WanSpeedTestFields.rpcVer.name(), ApplicationConstants.RPC_VERSION_V3);
        data.put(WanSpeedTestDto.WanSpeedTestFields.recordId.name(), recordId);

        DBCollection dbCollection = at3Adapter.getMongoDbCollection(WanSpeedTestDto.COLLECTION_WAN_SPEED_TEST);
        dbCollection.insert(data);

    }

    public Optional<WanSpeedTestDto> findByRecordId(String recordId) {
        logger.debug("findByRecordId. recordId:[{}]", recordId);
        BasicDBObject query = new BasicDBObject();
        query.put(WanSpeedTestDto.WanSpeedTestFields.recordId.name(), recordId);

        DBCollection dbCollection = at3Adapter.getMongoDbCollection(WanSpeedTestDto.COLLECTION_WAN_SPEED_TEST);
        DBObject dbObject = dbCollection.findOne(query);

        if (!ObjectUtils.isEmpty(dbObject)) {
            return Optional.of(WanSpeedTestDto.fromBasicDbObject((BasicDBObject) dbObject));
        }
        return Optional.empty();
    }

    public void updateByUserIdAndRecordIdAndSerial(String userId, String recordId, String serialNumber,
                                                   Map<String, Object> updateFields, boolean isUpsert, boolean isMultipleUpdate) {
        BasicDBObject query = new BasicDBObject()
                .append(WanSpeedTestDto.WanSpeedTestFields.userId.name(), userId)
                .append(WanSpeedTestDto.WanSpeedTestFields.recordId.name(), recordId)
                .append(WanSpeedTestDto.WanSpeedTestFields.serialNumber.name(), serialNumber);

        BasicDBObject update = new BasicDBObject("$set", new BasicDBObject(updateFields));

        DBCollection dbCollection = at3Adapter.getMongoDbCollection(WanSpeedTestDto.COLLECTION_WAN_SPEED_TEST);
        dbCollection.update(query, update, isUpsert, isMultipleUpdate);
    }

    public List<WanSpeedTestDto> findByUserIdAndSerial(String userId, String serialNumber) {
        logger.debug("findByUserIdAndSerial. userId:[{}]", userId);

        BasicDBObject query = new BasicDBObject();
        query.put(WanSpeedTestDto.WanSpeedTestFields.userId.name(), userId);
        query.put(WanSpeedTestDto.WanSpeedTestFields.serialNumber.name(), serialNumber);

        DBCollection dbCollection = at3Adapter.getMongoDbCollection(WanSpeedTestDto.COLLECTION_WAN_SPEED_TEST);

        List<WanSpeedTestDto> results = new ArrayList<>();

        try (DBCursor dbCursor = dbCollection.find(query).sort(new BasicDBObject(WanSpeedTestDto.WanSpeedTestFields.date.name(), -1))) {
            while (dbCursor.hasNext()) {
                BasicDBObject dbObj = (BasicDBObject) dbCursor.next();
                results.add(WanSpeedTestDto.fromBasicDbObject(dbObj));
            }
        }

        return results;
    }

    public List<WanSpeedTestDto> findByUserId(String userId) {
        logger.debug("findByUserIdAndSerial. userId:[{}]", userId);

        BasicDBObject query = new BasicDBObject();
        query.put(WanSpeedTestDto.WanSpeedTestFields.userId.name(), userId);

        DBCollection dbCollection = at3Adapter.getMongoDbCollection(WanSpeedTestDto.COLLECTION_WAN_SPEED_TEST);

        List<WanSpeedTestDto> results = new ArrayList<>();

        try (DBCursor dbCursor = dbCollection.find(query).sort(new BasicDBObject(WanSpeedTestDto.WanSpeedTestFields.date.name(), -1))) {
            while (dbCursor.hasNext()) {
                BasicDBObject dbObj = (BasicDBObject) dbCursor.next();
                results.add(WanSpeedTestDto.fromBasicDbObject(dbObj));
            }
        }

        return results;
    }

}
