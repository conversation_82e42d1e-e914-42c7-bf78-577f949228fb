package com.actiontec.optim.mongodb.dao;

import com.actiontec.optim.mongodb.dto.ApDetailDto;
import com.actiontec.optim.platform.adapter.At3Adapter;
import com.actiontec.optim.platform.constant.ApplicationConstants;
import com.incs83.exceptions.handler.ValidationException;
import com.mongodb.BasicDBList;
import com.mongodb.BasicDBObject;
import com.mongodb.DBCollection;
import com.mongodb.DBCursor;
import com.mongodb.DBObject;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.regex.Pattern;

@Component
public class ApDetailDao {
    private final Logger logger = LogManager.getLogger(this.getClass());

    @Autowired
    private At3Adapter at3Adapter;

    public Optional<ApDetailDto> findBySerial(String serial) {
        logger.debug("findBySerial. serial:[{}]", serial);
        BasicDBObject query = new BasicDBObject();
        query.put(ApDetailDto.ApDetailFields.serialNumber.name(), serial);

        DBCollection dbCollection = at3Adapter.getMongoDbCollection(ApDetailDto.COLLECTION_apDetail);
        DBCursor dbCursor = dbCollection.find(query).limit(2);

        ApDetailDto apDetailDto = null;
        int size = dbCursor.size();
        if (size == 0) {
            // no matched apDetail
        } else if (size == 2) {
            throw new RuntimeException("more than one equipment with serial [" + serial + "]");
        } else {
            DBObject dbObject = dbCursor.next();
            apDetailDto = ApDetailDto.fromBasicDbObject((BasicDBObject) dbObject);
        }

        return Optional.ofNullable(apDetailDto);
    }

    public Optional<ApDetailDto> findByUserIdAndSerial(String userId, String serial) {
        logger.debug("findByUserIdAndSerial. userId:[{}] serial:[{}]", userId, serial);
        BasicDBObject query = new BasicDBObject();
        query.put(ApDetailDto.ApDetailFields.userId.name(), userId);
        query.put(ApDetailDto.ApDetailFields.serialNumber.name(), serial);

        DBCollection dbCollection = at3Adapter.getMongoDbCollection(ApDetailDto.COLLECTION_apDetail);
        DBCursor dbCursor = dbCollection.find(query).limit(2);

        ApDetailDto apDetailDto = null;
        int size = dbCursor.size();
        if (size == 0) {
            // no matched apDetail
        } else if (size == 2) {
            throw new RuntimeException("more than one equipment with userId [" + userId + "] and serial [" + serial + "]");
        } else {
            DBObject dbObject = dbCursor.next();
            apDetailDto = ApDetailDto.fromBasicDbObject((BasicDBObject) dbObject);
        }

        return Optional.ofNullable(apDetailDto);
    }

    public Map<String, String> getSerialToMacMap(String userId) {
        Map<String, String> serialToMacMap = new HashMap<>();

        DBCursor dbCursor = listSerialAndMac(userId);
        while (dbCursor.hasNext()) {
            BasicDBObject dbObj = (BasicDBObject) dbCursor.next();
            String serial = dbObj.getString(ApDetailDto.ApDetailFields.serialNumber.name());
            String macAddress = dbObj.getString(ApDetailDto.ApDetailFields.macAddress.name());

            if (StringUtils.isNotBlank(serial) && StringUtils.isNotBlank(macAddress)) {
                serialToMacMap.put(serial, macAddress);
            }
        }

        return serialToMacMap;
    }

    public Map<String, String> getMacToSerialMap(String userId) {
        Map<String, String> macToSerialMap = new HashMap<>();

        DBCursor dbCursor = listSerialAndMac(userId);
        while (dbCursor.hasNext()) {
            BasicDBObject dbObj = (BasicDBObject) dbCursor.next();
            String serial = dbObj.getString(ApDetailDto.ApDetailFields.serialNumber.name());
            String macAddress = dbObj.getString(ApDetailDto.ApDetailFields.macAddress.name());

            if (StringUtils.isNotBlank(serial) && StringUtils.isNotBlank(macAddress)) {
                macToSerialMap.put(macAddress, serial);
            }
        }

        return macToSerialMap;
    }

    private DBCursor listSerialAndMac(String userId) {
        BasicDBObject query = new BasicDBObject();
        query.put(ApDetailDto.ApDetailFields.userId.name(), userId);

        BasicDBObject projection = new BasicDBObject();
        projection.put(ApDetailDto.ApDetailFields.serialNumber.name(), 1);
        projection.put(ApDetailDto.ApDetailFields.macAddress.name(), 1);

        DBCollection dbCollection = at3Adapter.getMongoDbCollection(ApDetailDto.COLLECTION_apDetail);
        return dbCollection.find(query);
    }

    public List<ApDetailDto.SsidDto> findSsidsByUserIdAndSerialNumber(String userId, String serial) {
        logger.debug("findSsidsBySerialNumber. userId:[{}], serial:[{}]", serial, userId);
        BasicDBObject query = new BasicDBObject();
        query.put(ApDetailDto.ApDetailFields.userId.name(), userId);
        query.put(ApDetailDto.ApDetailFields.serialNumber.name(), serial);

        DBCollection dbCollection = at3Adapter.getMongoDbCollection(ApDetailDto.COLLECTION_apDetail);
        DBCursor dbCursor = dbCollection.find(query).limit(2);

        List<ApDetailDto.SsidDto> ssids = null;
        int size = dbCursor.size();
        if (size == 0) {
            // no matched apDetail
        } else if (size == 2) {
            throw new RuntimeException("more than one equipment with serial [" + serial + "]");
        } else {
            DBObject dbObject = dbCursor.next();
            if (dbObject.get(ApDetailDto.ApDetailFields.ssids.name()) != null) {
                BasicDBList basicDBList = (BasicDBList) dbObject.get(ApDetailDto.ApDetailFields.ssids.name());
                ssids = ApDetailDto.SsidDto.fromBasicDbList(basicDBList);

                for (ApDetailDto.SsidDto ssid: ssids)
                    ssid.setLastReportTime(((BasicDBObject)dbObject).getLong(ApDetailDto.ApDetailFields.ntReportTimestamp.name(), 0));
            }
        }

        if (ssids == null) {
            ssids = new ArrayList<>();
        }

        logger.debug("findSsidsBySerialNumber succeeded. serial:[{}] resultSize:[{}]", serial, ssids.size());
        return ssids;
    }

    public List<ApDetailDto.SsidDto> findSsidsBySerialNumber(String serial) {
        logger.debug("findSsidsBySerialNumber. serial:[{}]", serial);
        BasicDBObject query = new BasicDBObject();
        query.put(ApDetailDto.ApDetailFields.serialNumber.name(), serial);

        DBCollection dbCollection = at3Adapter.getMongoDbCollection(ApDetailDto.COLLECTION_apDetail);
        DBCursor dbCursor = dbCollection.find(query).limit(2);

        List<ApDetailDto.SsidDto> ssids = null;
        int size = dbCursor.size();
        if (size == 0) {
            // no matched apDetail
        } else if (size == 2) {
            throw new RuntimeException("more than one equipment with serial [" + serial + "]");
        } else {
            DBObject dbObject = dbCursor.next();
            if (dbObject.get(ApDetailDto.ApDetailFields.ssids.name()) != null) {
                BasicDBList basicDBList = (BasicDBList) dbObject.get(ApDetailDto.ApDetailFields.ssids.name());
                ssids = ApDetailDto.SsidDto.fromBasicDbList(basicDBList);

                for (ApDetailDto.SsidDto ssid: ssids)
                    ssid.setLastReportTime(((BasicDBObject)dbObject).getLong(ApDetailDto.ApDetailFields.ntReportTimestamp.name(), 0));
            }
        }

        if (ssids == null) {
            ssids = new ArrayList<>();
        }

        logger.debug("findSsidsBySerialNumber succeeded. serial:[{}] resultSize:[{}]", serial, ssids.size());
        return ssids;
    }

    public List<ApDetailDto.SsidDto> findSsidsByUserId(String userId) {
        logger.debug("findSsidsByUserId. userId:[{}]", userId);
        BasicDBObject query = new BasicDBObject();
        query.put(ApDetailDto.ApDetailFields.userId.name(), userId);

        List<ApDetailDto.SsidDto> userSsids = new ArrayList<>();
        DBCollection dbCollection = at3Adapter.getMongoDbCollection(ApDetailDto.COLLECTION_apDetail);
        DBCursor dbCursor = dbCollection.find(query);

        while (dbCursor.hasNext()) {
            DBObject dbObject = dbCursor.next();
            if (dbObject.get(ApDetailDto.ApDetailFields.ssids.name()) != null) {
                BasicDBList basicDBList = (BasicDBList) dbObject.get(ApDetailDto.ApDetailFields.ssids.name());
                List<ApDetailDto.SsidDto> ssids = ApDetailDto.SsidDto.fromBasicDbList(basicDBList);

                for (ApDetailDto.SsidDto ssid: ssids) {
                    ssid.setLastReportTime(((BasicDBObject)dbObject).getLong(ApDetailDto.ApDetailFields.ntReportTimestamp.name(), 0));
                    ssid.setSerialNumber(((BasicDBObject)dbObject).getString(ApDetailDto.ApDetailFields.serialNumber.name(), ""));
                }
                userSsids.addAll(ssids);
            }
        }

        if (userSsids == null) {
            userSsids = new ArrayList<>();
        }

        logger.debug("findSsidsByUserId succeeded. userId:[{}] resultSize:[{}]", userId, userSsids.size());
        return userSsids;
    }

    public ApDetailDto findRadioByUserIdAndSerial(String userId, String serial) {
        logger.debug("findRadioByUserIdAndSerial. userId:[{}] serial:[{}]", userId, serial);
        BasicDBObject query = new BasicDBObject();
        query.put(ApDetailDto.ApDetailFields.userId.name(), userId);
        query.put(ApDetailDto.ApDetailFields.serialNumber.name(), serial);
        BasicDBObject projection = new BasicDBObject();
        projection.put(ApDetailDto.ApDetailFields.serialNumber.name(), 1);
        projection.put(ApDetailDto.ApDetailFields.ntReportTimestamp.name(), 1);
        projection.put(ApDetailDto.ApDetailFields.wifiRadios.name(), 1);

        DBCollection dbCollection = at3Adapter.getMongoDbCollection(ApDetailDto.COLLECTION_apDetail);
        DBCursor dbCursor = dbCollection.find(query, projection).limit(2);

        ApDetailDto apDetailDto = new ApDetailDto();
        int size = dbCursor.size();
        if (size == 0) {
            return null;
        } else if (size == 2) {
            throw new RuntimeException("more than one equipment with serial [" + serial + "]");
        } else {
            BasicDBObject dbObj = (BasicDBObject) dbCursor.next();

            apDetailDto.setSerialNumber(dbObj.getString(ApDetailDto.ApDetailFields.serialNumber.name()));
            apDetailDto.setNtReportTimestamp(dbObj.getLong(ApDetailDto.ApDetailFields.ntReportTimestamp.name(),0));

            Optional<List<ApDetailDto.RadioDto>> wifiRadios = Optional.ofNullable(dbObj.get(ApDetailDto.ApDetailFields.wifiRadios.name()))
                    .map(obj -> {
                        return ApDetailDto.RadioDto.fromBasicDbList((BasicDBList) obj);
                    });
            apDetailDto.setWifiRadios(wifiRadios.orElse(null));

        }
        logger.debug("findRadioByUserIdAndSerial succeeded. userId:[{}] serial:[{}] ", userId, serial);
        return apDetailDto;
    }

    public HashMap<String, Object> findDslBySerialNumber(String serial) {
        logger.debug("findDslBySerialNumber. serial:[{}]", serial);
        HashMap<String, Object> result = new HashMap<>();
        BasicDBObject query = new BasicDBObject();
        query.put(ApDetailDto.ApDetailFields.serialNumber.name(), serial);

        DBCollection dbCollection = at3Adapter.getMongoDbCollection(ApDetailDto.COLLECTION_apDetail);
        DBObject apObject = dbCollection.findOne(query);

        Long timestamp = 0L;
        List<ApDetailDto.DslDto> dsls = null;
        if (apObject != null) {
            timestamp = (Long) apObject.get(ApDetailDto.ApDetailFields.timestamp.name());
            if (apObject.get(ApDetailDto.ApDetailFields.DSL.name()) != null) {
                BasicDBList dslList = (BasicDBList) apObject.get(ApDetailDto.ApDetailFields.DSL.name());
                dsls = ApDetailDto.DslDto.fromBasicDbList(dslList);
            }
        }

        if (dsls == null) {
            dsls = new ArrayList<>();
        }

        //When agent fixed report issue will delete this part.
        if (dsls.size() == 0) {
            ApDetailDto.DslDto dsl1 = new ApDetailDto.DslDto();
            dsl1.setLineNumber(1);
            dsl1.setStatus(ApplicationConstants.DSL_DOWN);

            ApDetailDto.DslDto dsl2 = new ApDetailDto.DslDto();
            dsl2.setLineNumber(2);
            dsl2.setStatus(ApplicationConstants.DSL_DOWN);

            dsls.add(dsl1);
            dsls.add(dsl2);
        } else {
            if (dsls.size() == 1) {
                ApDetailDto.DslDto fakeDsl = new ApDetailDto.DslDto();
                ApDetailDto.DslDto dsl = dsls.get(0);
                if (dsl.getLineNumber() == 1) {
                    fakeDsl.setLineNumber(2);
                } else {
                    fakeDsl.setLineNumber(1);
                }
                fakeDsl.setStatus(ApplicationConstants.DSL_DOWN);
                dsls.add(fakeDsl);
            }
        }

        result.put("timestamp", timestamp);
        result.put("dsls", dsls);

        logger.debug("findDslBySerialNumber succeeded. serial:[{}]", serial);
        return result;
    }

    public ApDetailDto findMocaByUserIdAndSerial(String userId, String serial) {
        logger.debug("findMocaByUserIdAndSerial. userId:[{}] serial:[{}]", userId, serial);
        BasicDBObject query = new BasicDBObject();
        query.put(ApDetailDto.ApDetailFields.userId.name(), userId);
        query.put(ApDetailDto.ApDetailFields.serialNumber.name(), serial);
        BasicDBObject projection = new BasicDBObject();
        projection.put(ApDetailDto.ApDetailFields.serialNumber.name(), 1);
        projection.put(ApDetailDto.ApDetailFields.mocas.name(), 1);
        projection.put(ApDetailDto.ApDetailFields.mocaDevices.name(), 1);
        projection.put(ApDetailDto.ApDetailFields.sendingTime.name(), 1);
        projection.put(ApDetailDto.ApDetailFields.receivedTime.name(), 1);

        DBCollection dbCollection = at3Adapter.getMongoDbCollection(ApDetailDto.COLLECTION_apDetail);
        DBCursor dbCursor = dbCollection.find(query, projection).limit(2);

        ApDetailDto apDetailDto = new ApDetailDto();
        int size = dbCursor.size();
        if (size == 0) {
            return null;
        } else if (size == 2) {
            throw new RuntimeException("more than one equipment with serial [" + serial + "]");
        } else {
            BasicDBObject dbObj = (BasicDBObject) dbCursor.next();

            apDetailDto.setSerialNumber(dbObj.getString(ApDetailDto.ApDetailFields.serialNumber.name()));
            apDetailDto.setSendingTime(dbObj.getLong(ApDetailDto.ApDetailFields.sendingTime.name()));
            apDetailDto.setReceivedTime(dbObj.getLong(ApDetailDto.ApDetailFields.receivedTime.name()));

            Optional<List<ApDetailDto.MocaDto>> mocas = Optional.ofNullable(dbObj.get(ApDetailDto.ApDetailFields.mocas.name()))
                    .map(obj -> {
                        return ApDetailDto.MocaDto.fromBasicDbList((BasicDBList) obj);
                    });
            apDetailDto.setMocas(mocas.orElse(new ArrayList<>()));

            Optional<List<ApDetailDto.MocaDeviceDto>> mocaDevices = Optional.ofNullable(dbObj.get(ApDetailDto.ApDetailFields.mocaDevices.name()))
                    .map(obj -> {
                        return ApDetailDto.MocaDeviceDto.fromBasicDbList((BasicDBList) obj);
                    });
            apDetailDto.setMocaDevices(mocaDevices.orElse(new ArrayList<>()));
        }

        logger.debug("findMocaByUserIdAndSerial succeeded. userId:[{}] serial:[{}]", userId, serial);
        return apDetailDto;
    }

    public ApDetailDto.VoipDto findVoipBySerialNumber(String serial) {
        logger.debug("findVoipBySerialNumber. serial:[{}]", serial);
        BasicDBObject query = new BasicDBObject();
        query.put(ApDetailDto.ApDetailFields.serialNumber.name(), serial);

        DBCollection dbCollection = at3Adapter.getMongoDbCollection(ApDetailDto.COLLECTION_apDetail);
        DBObject apObject = dbCollection.findOne(query);

        ApDetailDto.VoipDto voip = null;
        if (apObject != null) {
            if (apObject.get(ApDetailDto.ApDetailFields.voip.name()) != null) {
                BasicDBObject voipObject = (BasicDBObject) apObject.get(ApDetailDto.ApDetailFields.voip.name());
                voip = ApDetailDto.VoipDto.fromBasicDBOject(voipObject);
            }
        }

        logger.debug("findVoipBySerialNumber succeeded. serial:[{}]", serial);
        return voip;
    }

    public ApDetailDto findNetworkByUserIdAndSerial(String userId, String serial) {
        logger.debug("findNetworkByUserIdAndSerial. userId:[{}] serial:[{}]", userId, serial);
        BasicDBObject query = new BasicDBObject();
        query.put(ApDetailDto.ApDetailFields.userId.name(), userId);
        query.put(ApDetailDto.ApDetailFields.serialNumber.name(), serial);
        BasicDBObject projection = new BasicDBObject();
        projection.put(ApDetailDto.ApDetailFields.userId.name(), 1);
        projection.put(ApDetailDto.ApDetailFields.serialNumber.name(), 1);
        projection.put(ApDetailDto.ApDetailFields.network.name(), 1);
        projection.put(ApDetailDto.ApDetailFields.sendingTime.name(), 1);
        projection.put(ApDetailDto.ApDetailFields.receivedTime.name(), 1);
        projection.put(ApDetailDto.ApDetailFields.ntReportTimestamp.name(), 1);
        projection.put(ApDetailDto.ApDetailFields.wan.name(), 1);
        DBCollection dbCollection = at3Adapter.getMongoDbCollection(ApDetailDto.COLLECTION_apDetail);
        DBCursor dbCursor = dbCollection.find(query, projection).limit(2);

        ApDetailDto apDetailDto = new ApDetailDto();
        int size = dbCursor.size();
        if (size == 0) {
            return null;
        } else if (size == 2) {
            throw new RuntimeException("more than one equipment with userId:[" + userId + "] serial:[" + serial + "]");
        } else {
            BasicDBObject dbObj = (BasicDBObject) dbCursor.next();

            apDetailDto.setSerialNumber(dbObj.getString(ApDetailDto.ApDetailFields.serialNumber.name()));
            apDetailDto.setSendingTime(dbObj.getLong(ApDetailDto.ApDetailFields.sendingTime.name(), 0L));
            apDetailDto.setReceivedTime(dbObj.getLong(ApDetailDto.ApDetailFields.receivedTime.name(), 0L));
            apDetailDto.setNtReportTimestamp(dbObj.getLong(ApDetailDto.ApDetailFields.ntReportTimestamp.name(), 0L));

            Optional.ofNullable(dbObj.get(ApDetailDto.ApDetailFields.network.name()))
                    .map(networkDbObj -> ApDetailDto.NetworkDto.fromBasicDbObject((BasicDBObject) networkDbObj))
                    .ifPresent(networkDto -> apDetailDto.setNetworkDto(networkDto));

            Optional.ofNullable(dbObj.get(ApDetailDto.ApDetailFields.wan.name()))
                    .map(wanDbObj -> ApDetailDto.WanDto.fromBasicDBOject((BasicDBObject) wanDbObj))
                    .ifPresent(wanDto-> apDetailDto.setWanDto(wanDto));
        }

        logger.debug("findNetworkByUserIdAndSerial succeeded. userId:[{}] serial:[{}]", userId, serial);
        return apDetailDto;
    }

    public HashMap<String, Object> findPonBySerialNumber(String serial) {
        logger.debug("findPonBySerialNumber. serial:[{}]", serial);

        HashMap<String, Object> result = new HashMap<>();

        BasicDBObject query = new BasicDBObject();
        query.put(ApDetailDto.ApDetailFields.serialNumber.name(), serial);

        DBCollection dbCollection = at3Adapter.getMongoDbCollection(ApDetailDto.COLLECTION_apDetail);
        DBObject apObject = dbCollection.findOne(query);

        Long timestamp = 0L;
        ApDetailDto.PonDto pon = null;
        if (apObject != null) {
            timestamp = (Long) apObject.get(ApDetailDto.ApDetailFields.timestamp.name());
            if (apObject.get(ApDetailDto.ApDetailFields.pon.name()) != null) {
                BasicDBObject ponObject = (BasicDBObject) apObject.get(ApDetailDto.ApDetailFields.pon.name());
                pon = ApDetailDto.PonDto.fromBasicDBOject(ponObject);
            }
        }

        result.put("timestamp", timestamp);
        result.put("pon", pon);

        logger.debug("findPonBySerialNumber succeeded. serial:[{}]", serial);
        return result;
    }

//    public String findBaseMacAddressBySerial(String rgwSerial, String serial) {
//        logger.debug("findBaseMacAddressBySerial. serial:[{}]", serial);
//
//        BasicDBObject query = new BasicDBObject();
//        query.put(ApDetailDto.ApDetailFields.userId.name(), rgwSerial);
//        query.put(ApDetailDto.ApDetailFields.serialNumber.name(), serial);
//
//        DBCollection dbCollection = at3Adapter.getMongoDbCollection(ApDetailDto.COLLECTION_apDetail);
//        DBObject apObject = dbCollection.findOne(query);
//
//        String baseMacAddress = null;
//        if (apObject != null) {
//            if(apObject.get(ApDetailDto.ApDetailFields.baseMacAddress.name()) != null) {
//                baseMacAddress = (String) apObject.get(ApDetailDto.ApDetailFields.baseMacAddress.name());
//            }
//        }
//
//        logger.debug("findBaseMacAddressBySerial. serial:[{}] baseMac:[{}]", serial, baseMacAddress);
//        return baseMacAddress;
//    }
//
//    public String findSerialByMacAddress(String macAddress) {
//        logger.debug("findSerialByMacAddress. macAddress:[{}]", macAddress);
//
//        BasicDBObject query = new BasicDBObject();
//        query.put(ApDetailDto.ApDetailFields.baseMacAddress.name(), macAddress);
//
//        DBCollection dbCollection = at3Adapter.getMongoDbCollection(ApDetailDto.COLLECTION_apDetail);
//        DBObject apObject = dbCollection.findOne(query);
//
//        String serial = null;
//        if (apObject != null) {
//            if(apObject.get(ApDetailDto.ApDetailFields.serialNumber.name()) != null) {
//                serial = (String) apObject.get(ApDetailDto.ApDetailFields.serialNumber.name());
//            }
//        }
//
//        return serial;
//    }

    public List<ApDetailDto> listByUserId(String userId) {
        logger.debug("listByUserId. userId:[{}]", userId);

        BasicDBObject query = new BasicDBObject();
        query.put(ApDetailDto.ApDetailFields.userId.name(), userId);

        DBCollection dbCollection = at3Adapter.getMongoDbCollection(ApDetailDto.COLLECTION_apDetail);
        DBCursor dbCursor = dbCollection.find(query);

        List<ApDetailDto> result = new ArrayList<>();
        while (dbCursor.hasNext()) {
            ApDetailDto apDetailDto = ApDetailDto.fromBasicDbObject((BasicDBObject) dbCursor.next());
            result.add(apDetailDto);
        }

        return result;
    }

    public Map<String, ApDetailDto> listByNetworkId(String networkId) {
        logger.debug("listByNetworkId. networkId:[{}]", networkId);

        BasicDBObject query = new BasicDBObject();
        query.put(ApDetailDto.ApDetailFields.userId.name(), networkId);

        DBCollection dbCollection = at3Adapter.getMongoDbCollection(ApDetailDto.COLLECTION_apDetail);
        DBCursor dbCursor = dbCollection.find(query);

        Map<String, ApDetailDto> result = new HashMap<>();
        while (dbCursor.hasNext()) {
            ApDetailDto apDetailDto = ApDetailDto.fromBasicDbObject((BasicDBObject) dbCursor.next());
            result.put(apDetailDto.getMacAddress().toLowerCase(), apDetailDto);
            if (!apDetailDto.getMacAddress().equalsIgnoreCase(apDetailDto.getBaseMacAddress())) {
                result.put(apDetailDto.getBaseMacAddress().toLowerCase(), apDetailDto);
            }
        }

        return result;
    }

    public DBObject getWifiConfDbObject(String userId, String serial) throws Exception {
        DBObject queryParam = new BasicDBObject();
        queryParam.put("userId", userId);
        queryParam.put("serialNumber", serial);
        BasicDBObject projection = new BasicDBObject();
        projection.put("wifiConf", 1);
        DBCollection dbCollection = at3Adapter.getMongoDbCollection(ApDetailDto.COLLECTION_apDetail);
        DBObject apDetail = dbCollection.findOne(queryParam, projection);

        if (apDetail == null) {
            logger.error("getSteering apDetail not found, equipmentId:[{}]", userId);
            throw new ValidationException(HttpStatus.NOT_FOUND.value(), "The resource is not found");
        }

        DBObject wifiConf = (DBObject) apDetail.get("wifiConf");
        if (wifiConf == null) {
            logger.error("getSteering wifiConf not found, equipmentId:[{}]", userId);
            throw new ValidationException(HttpStatus.NOT_FOUND.value(), "The resource is not found");
        }

        return wifiConf;
    }

    public String getSerialByMacAddress(String macAddress) {
        String serial = "";

        BasicDBObject macAddressRegex = new BasicDBObject("$regex", "^" + Pattern.quote(macAddress) + "$")
                .append("$options", "i");

        BasicDBObject orQuery = new BasicDBObject("$or", Arrays.asList(
                new BasicDBObject("macAddress", macAddressRegex),
                new BasicDBObject("baseMacAddress", macAddressRegex)
        ));

        BasicDBObject projection = new BasicDBObject("serialNumber", 1);

        DBCollection dbCollection = at3Adapter.getMongoDbCollection(ApDetailDto.COLLECTION_apDetail);
        DBObject apDetail = dbCollection.findOne(orQuery, projection);

        if (apDetail != null) {
            serial = (String) apDetail.get("serialNumber");
        }

        return serial;
    }

    public String findSerialByUserIdAndMacAddress(String userId, String macAddress) {
        String serial = "";

        BasicDBObject macAddressRegex = new BasicDBObject("$regex", "^" + Pattern.quote(macAddress) + "$")
                .append("$options", "i");

        BasicDBObject macQuery = new BasicDBObject("$or", Arrays.asList(
                new BasicDBObject(ApDetailDto.ApDetailFields.macAddress.name(), macAddressRegex),
                new BasicDBObject(ApDetailDto.ApDetailFields.baseMacAddress.name(), macAddressRegex)
        ));

        BasicDBObject query = new BasicDBObject("$and", Arrays.asList(
                new BasicDBObject(ApDetailDto.ApDetailFields.userId.name(), userId),
                macQuery
        ));

        BasicDBObject projection = new BasicDBObject(ApDetailDto.ApDetailFields.serialNumber.name(), 1);

        DBCollection dbCollection = at3Adapter.getMongoDbCollection(ApDetailDto.COLLECTION_apDetail);
        DBObject apDetail = dbCollection.findOne(query, projection);

        if (Objects.nonNull(apDetail)) {
            serial = (String) apDetail.get(ApDetailDto.ApDetailFields.serialNumber.name());
        }

        return serial;
    }
}
