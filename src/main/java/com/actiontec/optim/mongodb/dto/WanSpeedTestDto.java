package com.actiontec.optim.mongodb.dto;

import com.actiontec.optim.platform.api.v6.enums.NcsCpeSpeedTestStatus;
import com.mongodb.BasicDBObject;
import java.util.Date;

/**
 * The data in mongodb wanSpeedTest comes from the following sources and is stored after converting speeds to the same unit:
 * 1.Speed test results executed on CPE agent via POST /cpe-api/network/actions (rpc response speed unit: kbps)
 * 2.Speed test results executed on CPE web server via POST /api/v1/data/speedTest (rpc response speed unit: bps)
 */
public class WanSpeedTestDto {
    public static final String COLLECTION_WAN_SPEED_TEST = "wanSpeedTest";

    public enum WanSpeedTestFields {
        _id,
        date,
        result,
        status, // from ncs-cpe-api: GET /api/v1/data/speedTest/records response.status
        vmqStatus,
        downloadSpeed, // Mbps
        latency,
        uploadSpeed, // Mbps
        userId,
        testServer,
        rpcVer,
        recordId, // from ncs-cpe-api: GET /api/v1/data/speedTest/records response.id
        serialNumber
    }

    private String _id;
    private Date date;
    private String result;
    private NcsCpeSpeedTestStatus status;
    private Object vmqStatus;
    private double downloadSpeed;
    private int latency;
    private double uploadSpeed;
    private String userId;
    private String testServer;
    private String rpcVer;
    private String recordId;
    private String serialNumber;

    public static WanSpeedTestDto fromBasicDbObject(BasicDBObject dbObj) {
        WanSpeedTestDto result = new WanSpeedTestDto();
        result.set_id(dbObj.getString(WanSpeedTestFields._id.name()));
        result.setDate(dbObj.getDate(WanSpeedTestFields.date.name()));
        result.setResult(dbObj.getString(WanSpeedTestFields.result.name()));
        result.setStatus(NcsCpeSpeedTestStatus.fromString(dbObj.getString(WanSpeedTestFields.status.name())));
        result.setVmqStatus(dbObj.get(WanSpeedTestFields.vmqStatus.name()));
        result.setDownloadSpeed(dbObj.getDouble(WanSpeedTestFields.downloadSpeed.name(), 0.0));
        result.setLatency(dbObj.getInt(WanSpeedTestFields.latency.name(), 0));
        result.setUploadSpeed(dbObj.getDouble(WanSpeedTestFields.uploadSpeed.name(), 0.0));
        result.setUserId(dbObj.getString(WanSpeedTestFields.userId.name()));
        result.setTestServer(dbObj.getString(WanSpeedTestFields.testServer.name()));
        result.setRpcVer(dbObj.getString(WanSpeedTestFields.rpcVer.name()));
        result.setRecordId(dbObj.getString(WanSpeedTestFields.recordId.name()));
        result.setSerialNumber(dbObj.getString(WanSpeedTestFields.serialNumber.name()));
        return result;
    }

    // Getters and Setters
    public String get_id() { return _id; }
    public void set_id(String _id) { this._id = _id; }

    public Date getDate() { return date; }
    public void setDate(Date date) { this.date = date; }

    public String getResult() { return result; }
    public void setResult(String result) { this.result = result; }

    public NcsCpeSpeedTestStatus getStatus() { return status; }
    public void setStatus(NcsCpeSpeedTestStatus status) { this.status = status; }

    public Object getVmqStatus() { return vmqStatus; }
    public void setVmqStatus(Object vmqStatus) { this.vmqStatus = vmqStatus; }

    public double getDownloadSpeed() { return downloadSpeed; }
    public void setDownloadSpeed(double downloadSpeed) { this.downloadSpeed = downloadSpeed; }

    public int getLatency() { return latency; }
    public void setLatency(int latency) { this.latency = latency; }

    public double getUploadSpeed() { return uploadSpeed; }
    public void setUploadSpeed(double uploadSpeed) { this.uploadSpeed = uploadSpeed; }

    public String getUserId() { return userId; }
    public void setUserId(String userId) { this.userId = userId; }

    public String getTestServer() { return testServer; }
    public void setTestServer(String testServer) { this.testServer = testServer; }

    public String getRpcVer() { return rpcVer; }
    public void setRpcVer(String rpcVer) { this.rpcVer = rpcVer; }

    public String getRecordId() { return recordId; }
    public void setRecordId(String recordId) { this.recordId = recordId; }

    public String getSerialNumber() { return serialNumber; }
    public void setSerialNumber(String serialNumber) { this.serialNumber = serialNumber; }

}
