package com.actiontec.optim.mongodb.dto;

import com.mongodb.BasicDBList;
import com.mongodb.BasicDBObject;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

public class HostDto {
    public static final String COLLECTION = "hostnameDetail";
    public enum Fields {
        userId,
        serialNumber,
        macAddress,
        hostname,
        isExtender,
        isSetTopBox,
        deviceType,
        customType,
        phyType,
        internetAccessBlocked,
        internetAccessBlockStartTime,
        internetAccessBlockDuration,
        friendlyName,
        sendingTime,
        timestamp,
        ntTimestamp,
        dateCreated,
        isp,
        type,
        eqpModelName,
        addresses,
        ssidkey,
        radiokey,
        portNo,
        ip,
        aei,
        l2MacAddress,
        rpcBlocked,
        rpcTimestamp,
        userDefineDeviceType,
        verified
    }

    private String userId;
    private String serialNumber;
    private String macAddress;
    private String l2MacAddress;
    private String hostName;
    private boolean isExtender;
    private boolean isSetTopBox;
    private String deviceType;

    private String customType;
    private String phyType;
    private boolean internetAccessBlocked;
    private long internetAccessBlockStartTime;
    private long internetAccessBlockDuration;
    private long sendingTime;
    private long timestamp;
    private long ntTimestamp;
    private Date dateCreated;
    private String isp;
    private String type;
    private String eqpModelName;
    private List<AddressDto> addresses;
    private String ssidkey;
    private String radiokey;
    private int portNo;
    private String ip;
    private AeiDto aeiDto;
    private boolean rpcBlocked;
    private long rpcTimestamp;
    // The following fields are only stored in MongoDB and do not exist on the equipment.
    private String friendlyName;
    private String userDefineDeviceType;
    private boolean verified;

    public static HostDto fromBasicDbObject(BasicDBObject dbObj) {
        HostDto hostDto = new HostDto();
        hostDto.setUserId(dbObj.getString(Fields.userId.name()));
        hostDto.setSerialNumber(dbObj.getString(Fields.serialNumber.name()));
        hostDto.setMacAddress(dbObj.getString(Fields.macAddress.name()));
        hostDto.setL2MacAddress(dbObj.getString(Fields.l2MacAddress.name()));
        hostDto.setHostName(dbObj.getString(Fields.hostname.name()));
        hostDto.setIsExtender(dbObj.getBoolean(Fields.isExtender.name(), false));
        hostDto.setIsSetTopBox(dbObj.getBoolean(Fields.isSetTopBox.name(), false));
        hostDto.setDeviceType(dbObj.getString(Fields.deviceType.name()));
        hostDto.setCustomType(dbObj.getString(Fields.customType.name()));
        hostDto.setPhyType(dbObj.getString(Fields.phyType.name()));
        hostDto.setInternetAccessBlocked(dbObj.getBoolean(Fields.internetAccessBlocked.name(), false));
        hostDto.setInternetAccessBlockStartTime(dbObj.getLong(Fields.internetAccessBlockStartTime.name(), 0));
        hostDto.setInternetAccessBlockDuration(dbObj.getLong(Fields.internetAccessBlockDuration.name(), 0));
        hostDto.setFriendlyName(dbObj.getString(Fields.friendlyName.name()));
        hostDto.setUserDefineDeviceType(dbObj.getString(Fields.userDefineDeviceType.name()));
        hostDto.setVerified(dbObj.getBoolean(Fields.verified.name(), false));
        hostDto.setSendingTime(dbObj.getLong(Fields.sendingTime.name(), 0));
        hostDto.setTimestamp(dbObj.getLong(Fields.timestamp.name(), 0));
        hostDto.setNtTimestamp(dbObj.getLong(Fields.ntTimestamp.name(), 0));
        hostDto.setDateCreated(dbObj.getDate(Fields.dateCreated.name()));
        hostDto.setIsp(dbObj.getString(Fields.isp.name()));
        hostDto.setType(dbObj.getString(Fields.type.name()));
        hostDto.setEqpModelName(dbObj.getString(Fields.eqpModelName.name()));
        hostDto.setAddresses(AddressDto.fromBasicDbList((BasicDBList) dbObj.get(Fields.addresses.name())));
        hostDto.setSsidkey(dbObj.getString(Fields.ssidkey.name()));
        hostDto.setRadiokey(dbObj.getString(Fields.radiokey.name()));
        hostDto.setPortNo(dbObj.getInt(Fields.portNo.name(), 0));
        hostDto.setIp(dbObj.getString(Fields.ip.name()));
        Optional.ofNullable((BasicDBObject)dbObj.get(Fields.aei.name())).ifPresent(p->hostDto.setAeiDto(AeiDto.fromBasicDbObject((p))));
        hostDto.setRpcBlocked(dbObj.getBoolean(Fields.rpcBlocked.name(), false));
        hostDto.setRpcTimestamp(dbObj.getLong(Fields.rpcTimestamp.name(), 0));
        return hostDto;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
    }

    public String getMacAddress() {
        return macAddress;
    }

    public void setMacAddress(String macAddress) {
        this.macAddress = macAddress;
    }

    public String getL2MacAddress() {
        return l2MacAddress;
    }

    public void setL2MacAddress(String l2MacAddress) {
        this.l2MacAddress = l2MacAddress;
    }

    public String getHostName() {
        return hostName;
    }

    public void setHostName(String hostName) {
        this.hostName = hostName;
    }

    public boolean getIsExtender() {
        return isExtender;
    }

    public void setIsExtender(boolean isExtender) {
        this.isExtender = isExtender;
    }

    public boolean getIsSetTopBox() {
        return isSetTopBox;
    }

    public void setIsSetTopBox(boolean isSetTopBox) {
        this.isSetTopBox = isSetTopBox;
    }

    public String getDeviceType() {
        return deviceType;
    }

    public void setDeviceType(String deviceType) {
        this.deviceType = deviceType;
    }

    public String getCustomType() {
        return customType;
    }

    public void setCustomType(String customType) {
        this.customType = customType;
    }

    public String getPhyType() {
        return phyType;
    }

    public void setPhyType(String phyType) {
        this.phyType = phyType;
    }

    public boolean isInternetAccessBlocked() {
        return internetAccessBlocked;
    }

    public void setInternetAccessBlocked(boolean internetAccessBlocked) {
        this.internetAccessBlocked = internetAccessBlocked;
    }

    public long getInternetAccessBlockStartTime() {
        return internetAccessBlockStartTime;
    }

    public void setInternetAccessBlockStartTime(long internetAccessBlockStartTime) {
        this.internetAccessBlockStartTime = internetAccessBlockStartTime;
    }

    public long getInternetAccessBlockDuration() {
        return internetAccessBlockDuration;
    }

    public void setInternetAccessBlockDuration(long internetAccessBlockDuration) {
        this.internetAccessBlockDuration = internetAccessBlockDuration;
    }

    public String getFriendlyName() {
        return friendlyName;
    }

    public void setFriendlyName(String friendlyName) {
        this.friendlyName = friendlyName;
    }

    public String getUserDefineDeviceType() {
        return userDefineDeviceType;
    }

    public void setUserDefineDeviceType(String userDefineDeviceType) {
        this.userDefineDeviceType = userDefineDeviceType;
    }

    public boolean isVerified() {
        return verified;
    }

    public void setVerified(boolean verified) {
        this.verified = verified;
    }

    public long getSendingTime() {
        return sendingTime;
    }

    public void setSendingTime(long sendingTime) {
        this.sendingTime = sendingTime;
    }

    public long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }

    public long getNtTimestamp() {
        return ntTimestamp;
    }

    public void setNtTimestamp(long ntTimestamp) {
        this.ntTimestamp = ntTimestamp;
    }

    public Date getDateCreated() {
        return dateCreated;
    }

    public void setDateCreated(Date dateCreated) {
        this.dateCreated = dateCreated;
    }

    public String getIsp() {
        return isp;
    }

    public void setIsp(String isp) {
        this.isp = isp;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getEqpModelName() {
        return eqpModelName;
    }

    public void setEqpModelName(String eqpModelName) {
        this.eqpModelName = eqpModelName;
    }

    public List<AddressDto> getAddresses() {
        return addresses;
    }

    public void setAddresses(List<AddressDto> addresses) {
        this.addresses = addresses;
    }

    public String getSsidkey() {
        return ssidkey;
    }

    public void setSsidkey(String ssidkey) {
        this.ssidkey = ssidkey;
    }

    public String getRadiokey() {
        return radiokey;
    }

    public void setRadiokey(String radiokey) {
        this.radiokey = radiokey;
    }

    public int getPortNo() {
        return portNo;
    }

    public void setPortNo(int portNo) {
        this.portNo = portNo;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public AeiDto getAeiDto() {
        return aeiDto;
    }

    public void setAeiDto(AeiDto aeiDto) {
        this.aeiDto = aeiDto;
    }

    public boolean isRpcBlocked() {
        return rpcBlocked;
    }

    public void setRpcBlocked(boolean rpcBlocked) {
        this.rpcBlocked = rpcBlocked;
    }

    public long getRpcTimestamp() {
        return rpcTimestamp;
    }

    public void setRpcTimestamp(long rpcTimestamp) {
        this.rpcTimestamp = rpcTimestamp;
    }

    public static class AddressDto {
        public enum Fields {
            version,
            type,
            address
        }

        private String version;
        private String type;
        private String address;

        public static AddressDto fromBasicDbObject(BasicDBObject dbObj) {
            AddressDto addressDto = new AddressDto();

            addressDto.setVersion(dbObj.getString(AddressDto.Fields.version.name()));
            addressDto.setType(dbObj.getString(AddressDto.Fields.type.name()));
            addressDto.setAddress(dbObj.getString(AddressDto.Fields.address.name()));

            return addressDto;
        }

        public static List<AddressDto> fromBasicDbList(BasicDBList dbList) {
            List<AddressDto> result = new ArrayList<>();
            if (dbList != null) {
                for (Object dbObj : dbList) {
                    AddressDto addressDto = fromBasicDbObject((BasicDBObject) dbObj);
                    result.add(addressDto);
                }
            }
            return result;
        }

        public String getVersion() {
            return version;
        }

        public void setVersion(String version) {
            this.version = version;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public String getAddress() {
            return address;
        }

        public void setAddress(String address) {
            this.address = address;
        }
    }
    public static class AeiDto{
        private String vendor;
        private String model;
        private String type;
        private String os;
        private String os_detail;

        public enum Fields {
            vendor, model, type, os, os_detail
        }


        public static AeiDto fromBasicDbObject(BasicDBObject dbObj) {
            AeiDto aeiDto = new AeiDto();

            aeiDto.setVendor(dbObj.getString(AeiDto.Fields.vendor.name(), ""));
            aeiDto.setModel(dbObj.getString(AeiDto.Fields.model.name(), ""));
            aeiDto.setType(dbObj.getString(AeiDto.Fields.type.name(), ""));
            aeiDto.setOs(dbObj.getString(AeiDto.Fields.os.name(), ""));
            aeiDto.setOs_detail(dbObj.getString(AeiDto.Fields.os_detail.name(), ""));

            return aeiDto;
        }

        public String getVendor() {
            return vendor;
        }

        public void setVendor(String vendor) {
            this.vendor = vendor;
        }

        public String getModel() {
            return model;
        }

        public void setModel(String model) {
            this.model = model;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public String getOs() {
            return os;
        }

        public void setOs(String os) {
            this.os = os;
        }

        public String getOs_detail() {
            return os_detail;
        }

        public void setOs_detail(String os_detail) {
            this.os_detail = os_detail;
        }
    }
}
