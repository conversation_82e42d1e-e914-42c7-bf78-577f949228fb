package com.actiontec.optim.util;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.HttpStatus;

import com.actiontec.optim.platform.api.v6.enums.ResponseMessage;
import com.incs83.exceptions.handler.ValidationException;
import com.incs83.util.CommonUtils;

public class ValidationUtils {

    private static final Logger logger = LogManager.getLogger(ValidationUtils.class);
    /**
     * Validate if the user is a system administrator.
     * @throws ValidationException
     */
    public static void validateIsSystemAdmin() throws ValidationException {
        if (!CommonUtils.isSysAdmin()) {
            throw new ValidationException(HttpStatus.FORBIDDEN.value(),
                    ResponseMessage.FORBIDDEN.getMessage());
        }
    }

    /**
     * Validate if the user is a group administrator.
     * @throws ValidationException
     */
    public static void validateIsGroupAdmin() throws ValidationException {
        if (!CommonUtils.isGroupAdmin()) {
            throw new ValidationException(HttpStatus.FORBIDDEN.value(),
                    ResponseMessage.FORBIDDEN.getMessage());
        }
    }

    /**
     * Validate if the user is a system administrator or a group administrator.
     * @throws ValidationException
     */
    public static void validateIsSysAdminOrGroupAdmin() throws ValidationException {
        if (!CommonUtils.isSysAdmin() && !CommonUtils.isGroupAdmin()) {
            throw new ValidationException(HttpStatus.FORBIDDEN.value(),
                    ResponseMessage.FORBIDDEN.getMessage());
        }
    }

}
