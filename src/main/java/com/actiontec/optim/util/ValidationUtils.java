package com.actiontec.optim.util;
import java.util.Collection;
import java.util.Objects;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.HttpStatus;

import com.actiontec.optim.platform.api.v5.exception.OptimApiException;
import com.actiontec.optim.platform.api.v6.enums.ResponseMessage;
import com.incs83.app.enums.ErrorResponseEnum;
import com.incs83.exceptions.handler.ValidationException;
import com.incs83.util.CommonUtils;

/**
 * ValidationUtils class for validation and exception handling.
 * 
 * description:
 * 1. 400 Bad Request: throwValidationException
 * 2. 404 Not Found: throwNotFoundException
 * 3. 503 Service Unavailable: throwServiceUnavailableException
 */
public class ValidationUtils {

    private static final Logger logger = LogManager.getLogger(ValidationUtils.class);
    /**
     * Validate if the user is a system administrator.
     * @throws ValidationException
     */
    public static void validateIsSystemAdmin() throws ValidationException {
        if (!CommonUtils.isSysAdmin()) {
            throw new ValidationException(HttpStatus.FORBIDDEN.value(),
                    ResponseMessage.FORBIDDEN.getMessage());
        }
    }

    /**
     * Validate if the user is a group administrator.
     * @throws ValidationException
     */
    public static void validateIsGroupAdmin() throws ValidationException {
        if (!CommonUtils.isGroupAdmin()) {
            throw new ValidationException(HttpStatus.FORBIDDEN.value(),
                    ResponseMessage.FORBIDDEN.getMessage());
        }
    }

    /**
     * Validate if the user is a system administrator or a group administrator.
     * @throws ValidationException
     */
    public static void validateIsSysAdminOrGroupAdmin() throws ValidationException {
        if (!CommonUtils.isSysAdmin() && !CommonUtils.isGroupAdmin()) {
            throw new ValidationException(HttpStatus.FORBIDDEN.value(),
                    ResponseMessage.FORBIDDEN.getMessage());
        }
    }

    /**
     * 400 Bad Request
     * Throw validation exception.
     * @param apiResponse
     */
    public static void throwValidationException(ErrorResponseEnum apiResponse) {
        throwValidationException(apiResponse, null);
    }

    /**
     * 400 Bad Request
     * Throw validation exception with log message.
     * @param apiResponse
     * @param logMessage
     */
    public static void throwValidationException(ErrorResponseEnum apiResponse, String logMessage) {
        if (CustomStringUtils.isNotEmpty(logMessage)) {
            logger.info(logMessage);
        }
        throw new ValidationException(apiResponse.getCode(), apiResponse.getMessage());
    }

    /**
     * 400 Bad Request
     * Throw validation exception with message.
     * @param message
     */
    public static void throwValidationException(String message) {
        throw new ValidationException(HttpStatus.BAD_REQUEST.value(), message);
    }

    public static void throwValidationExceptionIfIsNull(Object obj, String message) throws ValidationException {
        if (Objects.isNull(obj)) {
            throwValidationException(message);
        }
    }

    public static void throwValidationExceptionIfIsNull(Object obj, ErrorResponseEnum apiResponse)
            throws ValidationException {
        throwValidationExceptionIfIsNull(obj, apiResponse, null);
    }

    public static void throwValidationExceptionIfIsNull(Object obj, ErrorResponseEnum apiResponse,
            String logMessage) throws ValidationException {
        if (Objects.isNull(obj)) {
            throwValidationException(apiResponse, logMessage);
        }
    }

    public static <T> void throwValidationExceptionIfIsEmpty(Collection<T> collection,
        ErrorResponseEnum apiResponse) {
        throwValidationExceptionIfIsEmpty(collection, apiResponse, null);
    }

    public static <T> void throwValidationExceptionIfIsEmpty(Collection<T> collection,
                                                             ErrorResponseEnum apiResponse, String logMessage) {
        if (CollectionUtils.isEmpty(collection)) {
            throwValidationException(apiResponse, logMessage);
        }
    }

    /**
     * 404 Not Found
     * Throw not found exception.
     * @param apiResponse
     */
    public static void throwNotFoundException(ErrorResponseEnum apiResponse) {
        throwNotFoundException(apiResponse, null);
    }

    /**
     * 404 Not Found
     * Throw not found exception.
     * @param apiResponse
     * @param logMessage
     */
    public static void throwNotFoundException(ErrorResponseEnum apiResponse, String logMessage) {
        if (CustomStringUtils.isNotEmpty(logMessage)) {
            logger.info(logMessage);
        }
        throw new OptimApiException(HttpStatus.NOT_FOUND,
                apiResponse.getMessage());
    }

    public static <T> void throwNotFoundExceptionIfIsNull(T obj, ErrorResponseEnum apiResponse) {
        throwNotFoundExceptionIfIsNull(obj, apiResponse, null);
    }

    public static <T> void throwNotFoundExceptionIfIsNull(T obj, ErrorResponseEnum apiResponse, String logMessage) {
        if (Objects.isNull(obj)) {
            throwNotFoundException(apiResponse, logMessage);
        }
    }

    public static <T> void throwNotFoundExceptionIfIsEmpty(Collection<T> collection, ErrorResponseEnum apiResponse) {
        throwNotFoundExceptionIfIsEmpty(collection, apiResponse, null);
    }

    public static <T> void throwNotFoundExceptionIfIsEmpty(Collection<T> collection, ErrorResponseEnum apiResponse, String logMessage) {
        if (CollectionUtils.isEmpty(collection)) {
            throwNotFoundException(apiResponse, logMessage);
        }
    }

    /**
     * 503 Service Unavailable
     * Throw service unavailable exception.
     * @param apiResponse
     */
    public static void throwServiceUnavailableException(ErrorResponseEnum apiResponse) {
        throwServiceUnavailableException(apiResponse, null);
    }

    /**
     * 503 Service Unavailable
     * Throw service unavailable exception with log message.
     * @param apiResponse
     * @param logMessage
     */
    public static void throwServiceUnavailableException(ErrorResponseEnum apiResponse, String logMessage) {
        if (CustomStringUtils.isNotEmpty(logMessage)) {
            logger.info(logMessage);
        }
        throw new OptimApiException(HttpStatus.SERVICE_UNAVAILABLE,
                apiResponse.getMessage());
    }

    public static void throwServiceUnavailableException(String message) {
        throw new OptimApiException(HttpStatus.SERVICE_UNAVAILABLE, message);
    }

}
