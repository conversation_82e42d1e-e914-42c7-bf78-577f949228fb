package com.actiontec.optim.util;
import java.util.Collection;
import java.util.Objects;
import java.util.Set;

import javax.validation.ConstraintViolation;
import javax.validation.Validator;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.HttpStatus;
import org.springframework.util.ObjectUtils;

import com.actiontec.optim.platform.api.v5.exception.OptimApiException;
import com.actiontec.optim.platform.api.v6.dto.EquipmentModelIspConfigRequest;
import com.actiontec.optim.platform.api.v6.enums.ResponseMessage;
import com.incs83.app.enums.ErrorResponseEnum;
import com.incs83.exceptions.handler.ValidationException;
import com.incs83.util.CommonUtils;

/**
 * ValidationUtils class for validation and exception handling.
 * 
 * description:
 * 1. 400 Bad Request: throwValidationException
 * 2. 404 Not Found: throwNotFoundException
 * 3. 503 Service Unavailable: throwServiceUnavailableException
 */
public class ValidationUtils {

    private static final Logger logger = LogManager.getLogger(ValidationUtils.class);
    /**
     * Validate if the user is a system administrator.
     * @throws ValidationException
     */
    public static void validateIsSystemAdmin() throws ValidationException {
        if (!CommonUtils.isSysAdmin()) {
            throw new OptimApiException(HttpStatus.FORBIDDEN, ErrorResponseEnum.FORBIDDEN.getMessage());
        }
    }

    /**
     * Validate if the user is a group administrator.
     * @throws ValidationException
     */
    public static void validateIsGroupAdmin() throws ValidationException {
        if (!CommonUtils.isGroupAdmin()) {
            throw new OptimApiException(HttpStatus.FORBIDDEN, ErrorResponseEnum.FORBIDDEN.getMessage());
        }
    }

    /**
     * Validate if the user is a system administrator or a group administrator.
     * @throws ValidationException
     */
    public static void validateIsSysAdminOrGroupAdmin() throws ValidationException {
        if (!CommonUtils.isSysAdmin() && !CommonUtils.isGroupAdmin()) {
            throw new ValidationException(HttpStatus.FORBIDDEN.value(),
                    ResponseMessage.FORBIDDEN.getMessage());
        }
    }

    /**
     * 400 Bad Request
     * Throw validation exception.
     * @param errorResponse
     */
    public static void throwValidationException(ErrorResponseEnum errorResponse) {
        throwValidationException(errorResponse, null);
    }

    /**
     * 400 Bad Request
     * Throw validation exception with log message.
     * @param errorResponse
     * @param logMessage
     */
    public static void throwValidationException(ErrorResponseEnum errorResponse, String logMessage) {
        if (CustomStringUtils.isNotEmpty(logMessage)) {
            logger.info(logMessage);
        }
        throw new ValidationException(errorResponse.getCode(), errorResponse.getMessage());
    }

    /**
     * 400 Bad Request
     * Throw validation exception with message.
     * @param message
     */
    public static void throwValidationException(String message) {
        throw new ValidationException(HttpStatus.BAD_REQUEST.value(), message);
    }

    public static void throwValidationExceptionIfIsNull(Object obj, String message) throws ValidationException {
        if (ObjectUtils.isEmpty(obj)) {
            throwValidationException(message);
        }
    }

    public static void throwValidationExceptionIfIsNull(Object obj, ErrorResponseEnum errorResponse)
            throws ValidationException {
        throwValidationExceptionIfIsNull(obj, errorResponse, null);
    }

    public static void throwValidationExceptionIfIsNull(Object obj, ErrorResponseEnum errorResponse,
            String logMessage) throws ValidationException {
        if (Objects.isNull(obj)) {
            throwValidationException(errorResponse, logMessage);
        }
    }

    public static <T> void throwValidationExceptionIfIsEmpty(Collection<T> collection,
        ErrorResponseEnum errorResponse) {
        throwValidationExceptionIfIsEmpty(collection, errorResponse, null);
    }

    public static <T> void throwValidationExceptionIfIsEmpty(Collection<T> collection,
                                                             ErrorResponseEnum errorResponse, String logMessage) {
        if (CollectionUtils.isEmpty(collection)) {
            throwValidationException(errorResponse, logMessage);
        }
    }

    /**
     * 404 Not Found
     * Throw not found exception.
     * @param errorResponse
     */
    public static void throwNotFoundException(ErrorResponseEnum errorResponse) {
        throwNotFoundException(errorResponse, null);
    }

    /**
     * 404 Not Found
     * Throw not found exception.
     * @param errorResponse
     * @param logMessage
     */
    public static void throwNotFoundException(ErrorResponseEnum errorResponse, String logMessage) {
        if (CustomStringUtils.isNotEmpty(logMessage)) {
            logger.info(logMessage);
        }
        throw new OptimApiException(HttpStatus.NOT_FOUND,
                errorResponse.getMessage());
    }

    public static <T> void throwNotFoundExceptionIfIsNull(T obj, ErrorResponseEnum errorResponse) {
        throwNotFoundExceptionIfIsNull(obj, errorResponse, null);
    }

    public static <T> void throwNotFoundExceptionIfIsNull(T obj, ErrorResponseEnum errorResponse, String logMessage) {
        if (Objects.isNull(obj)) {
            throwNotFoundException(errorResponse, logMessage);
        }
    }

    public static <T> void throwNotFoundExceptionIfIsEmpty(Collection<T> collection, ErrorResponseEnum errorResponse) {
        throwNotFoundExceptionIfIsEmpty(collection, errorResponse, null);
    }

    public static <T> void throwNotFoundExceptionIfIsEmpty(Collection<T> collection, ErrorResponseEnum errorResponse, String logMessage) {
        if (CollectionUtils.isEmpty(collection)) {
            throwNotFoundException(errorResponse, logMessage);
        }
    }

    /**
     * 503 Service Unavailable
     * Throw service unavailable exception.
     * @param errorResponse
     */
    public static void throwServiceUnavailableException(ErrorResponseEnum errorResponse) {
        throwServiceUnavailableException(errorResponse, null);
    }

    /**
     * 503 Service Unavailable
     * Throw service unavailable exception with log message.
     * @param errorResponse
     * @param logMessage
     */
    public static void throwServiceUnavailableException(ErrorResponseEnum errorResponse, String logMessage) {
        if (CustomStringUtils.isNotEmpty(logMessage)) {
            logger.info(logMessage);
        }
        throw new OptimApiException(HttpStatus.SERVICE_UNAVAILABLE,
                errorResponse.getMessage());
    }

    public static void throwServiceUnavailableException(String message) {
        throw new OptimApiException(HttpStatus.SERVICE_UNAVAILABLE, message);
    }

    /**
     * 409 Conflict
     * Throw conflict exception.
     *
     * @param code
     * @param message
     */
    public static void throwConflictException(int code, String message) throws ValidationException {
        throw new OptimApiException(HttpStatus.CONFLICT, message);
    }

    /**
     * 409 Conflict
     * Throw conflict exception.
     *
     * @param errorResponse
     */
    public static void throwConflictException(ErrorResponseEnum errorResponse) {
        throw new OptimApiException(HttpStatus.CONFLICT, errorResponse.getMessage());
    }

    /**
     * 409 Conflict
     * Throw conflict exception.
     *
     * @param errorResponse
     * @param logMessage
     */
    public static void throwConflictException(ErrorResponseEnum errorResponse, String logMessage) {
        if (CustomStringUtils.isNotEmpty(logMessage)) {
            logger.info(logMessage);
        }
        throw new OptimApiException(HttpStatus.CONFLICT, errorResponse.getMessage());
    }
    /**
     * Validate the bean and throw ValidationException if theIre are violations.
     * @param request
     * @param group
     * @throws Exception
     */
    public static void validateRequest(EquipmentModelIspConfigRequest request, Class<?> group, Validator validator) throws Exception {
        Set<ConstraintViolation<EquipmentModelIspConfigRequest>> violations = validator.validate(request, group);
        if (!ObjectUtils.isEmpty(violations)) {
            throw new ValidationException(ErrorResponseEnum.BAD_REQUEST.getCode(), ErrorResponseEnum.BAD_REQUEST.getMessage());
        }
    }

}
