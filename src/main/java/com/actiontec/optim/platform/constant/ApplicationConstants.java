package com.actiontec.optim.platform.constant;

public class ApplicationConstants {
    public static final String ENCRYPT_KEY = "1234567887654321";
    public static final String SSID_KEY_UNDEFINED = "undefined";

    public static final String DSL_DOWN = "Down";
    public static final String DSL_UP = "Up";

    public static final String X_API_VERSION = "X-API-Version";
    public static final String API_VERSION_5 = "5";

    public static final String S3_PUBLIC_FILE_FOLDER = "public_file/";

    public static final String TYPE_FILE = "file";
    public static final String TYPE_URI = "uri";

    public static final String FILE_STATUS_INITIAL = "Initial";
    public static final String FILE_STATUS_UPLOADING = "Uploading";
    public static final String FILE_STATUS_SUCCESS = "Success";
    public static final String FILE_STATUS_ERROR = "Error";

    public static final String STRING_EMPTY = "";

    public static final String TRANSACTION_STATUS_INITIAL = "Initial";
    public static final String TRANSACTION_STATUS_IN_PROCESS = "Pending";
    public static final String TRANSACTION_STATUS_COMPLETED = "Completed";
    public static final String TRANSACTION_STATUS_FAILED = "Failed";

    public static final String CLUSTER = "cluster";
    public static final String GROUP = "group";
    public static final String ISP = "isp";
    public static final String ALL_ISP = "all";

    public static final String EVENT_STEER = "Steer";
    public static final String EVENT_ROAM = "Roam";

    public static final String TARGET = "TARGET";
    public static final String OTHER = "OTHER";
    public static final String NA = "N/A";

    public static final String WIFI_2G = "2.4G";
    public static final String WIFI_5G = "5G";
    public static final String WIFI_6G = "6G";

    public static final int ZERO = 0;

    public static final int LAST_DAY = 1;
    public static final int LAST_7_DAYS = 7;
    public static final int LAST_30_DAYS = 30;
    public static final int LAST_90_DAYS = 90;

    public static final int LAST_DAY_HOUR = 24;
    public static final int LAST_7_DAYS_HOUR = 168;
    public static final int LAST_30_DAYS_HOUR = 720;
    public static final int LAST_90_DAYS_HOUR = 2160;

    public static final Long SECONDS_OF_DAY = 86400L;

    public static final int RPC_DEFAULT_MAX_TRIES = 5;
    public static final int RPC_RTTY_SESSION_API_MAX_TRIES = 20;
    public static final long RPC_DEFUALT_RETRY_INTERVAL_MILLIS = 3000L;

    public static final int REMOTE_ACCESS_HTTP_MAX_TIMEOUT_VALUE = 3600;
    public static final int REMOTE_ACCESS_RTTY_MAX_TIMEOUT_VALUE = 604800;

    public static final String TRANSACTION_RECORDS = "transactionRecords";
    public static final String TRACKING_EQUIPMENT_INDEX = "tracking_equipment";

    public static final String PUBLIC_FILE_PATH = "/actiontec/api/v5/files/";

    public static final String CONNECTION_STATUS_OFFLINE = "offline";
    public static final String CONNECTION_STATUS_ONLINE = "online";

    public enum OptimFileType {
        firmware,
        smm,
        Tracking,
        batchEquipment,
        logo,
        certification,
        rtty
    }

    public static final int FW_UPGRADE_RETRY_TIMES = 3;
    public static final int FW_UPGRADE_RETRY_INTERVAL = 600;

    public static final String UPDATE_AT = "update_at";
    public static final String ISP_ID = "isp_id";

    public static final String LEGAL_DOCUMENT_TYPE = "legal_documet";
}
