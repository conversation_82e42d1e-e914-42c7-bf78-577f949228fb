package com.actiontec.optim.platform.repository;

import com.fasterxml.jackson.databind.node.ObjectNode;
import com.incs83.app.constants.queries.ProvisionDataSQL;
import com.incs83.app.entities.ProvisionData;
import com.incs83.mt.DataAccessService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;

@Repository
public class ProvisionDataRepo {

    private final Logger logger = LogManager.getLogger(this.getClass());

    @Autowired
    private DataAccessService dataAccessService;

    public ProvisionData findByNetworkIdAndSerial(String networkId, String serial) {
        HashMap<String, String> params = new HashMap<>();
        params.put("networkId", networkId);
        params.put("serial", serial);

        List<ProvisionData> provisionDataList = (List<ProvisionData>) dataAccessService.read(
                ProvisionData.class, ProvisionDataSQL.GET_PROVISION_DATA_BY_NETWORK_AND_SERIAL, params);

        if (CollectionUtils.isEmpty(provisionDataList)) {
            logger.info("No provision data found for network id {} and serial {}", networkId, serial);
            return null;
        }

        return provisionDataList.get(0);
    }
}
