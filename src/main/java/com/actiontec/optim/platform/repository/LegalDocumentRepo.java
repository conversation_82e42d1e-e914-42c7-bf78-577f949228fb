package com.actiontec.optim.platform.repository;

import com.actiontec.optim.platform.service.AwsS3Service;
import com.incs83.app.constants.queries.LegalDocumentSQL;
import com.incs83.app.entities.LegalDocument;
import com.incs83.mt.DataAccessService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;

@Service
public class LegalDocumentRepo {

    @Autowired
    private DataAccessService dataAccessService;
    @Autowired
    private AwsS3Service awsS3Service;
    public List<LegalDocument> getAll() throws Exception {
        return (List<LegalDocument>) dataAccessService.read(LegalDocument.class);
    }

    public LegalDocument getById(String id) throws Exception {
        return (LegalDocument) dataAccessService.read(LegalDocument.class, id);
    }

    public void create(LegalDocument legalDocument) throws Exception{
        dataAccessService.create(LegalDocument.class, legalDocument);
    }

    public void update(LegalDocument legalDocument) throws Exception{
        dataAccessService.update(LegalDocument.class, legalDocument);
    }

    public void delete(LegalDocument doc) throws Exception {
        awsS3Service.deleteFile(doc.getOptimFile());
        dataAccessService.delete(LegalDocument.class, doc.getId());
    }

    public boolean existsByLegalDocumentName(String name) throws Exception{
        HashMap<String, Object> params = new HashMap<>();
        params.put("name", name);
        List<Object> result = dataAccessService.readNative(LegalDocumentSQL.EXISTS_BY_LEGAL_DOCUMENT_NAME, params);
        if (CollectionUtils.isNotEmpty(result)) {
            Object count = result.get(0);
            return count != null && ((Number) count).longValue() > 0;
        }
        return false;
    }

}
