package com.actiontec.optim.platform.repository;

import java.util.HashMap;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.actiontec.optim.platform.constant.FirmwareSQL;
import com.incs83.app.entities.Firmware;
import com.incs83.mt.DataAccessService;

@Repository
public class FirmwareRepo {

    @Autowired
    private DataAccessService<Firmware> dataAccessService;

    public List<Firmware> findByModelId(String modelId) {

        HashMap<String, Object> params = new HashMap<>();
        params.put("modelId", modelId);

        return (List<Firmware>) dataAccessService
                .read(Firmware.class, FirmwareSQL.GET_FIRMWARE_BY_MODEL_ID, params);
    }

}
