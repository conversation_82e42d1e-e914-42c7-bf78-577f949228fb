package com.actiontec.optim.platform.repository;

import java.util.HashMap;
import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.actiontec.optim.platform.constant.ApplicationConstants;
import com.hazelcast.config.properties.ValidationException;
import com.incs83.app.constants.queries.EquipmentModelIspConfigSQL;
import com.incs83.app.entities.EquipmentModelIspConfig;
import com.incs83.app.entities.Firmware;
import com.incs83.dao.Page;
import com.incs83.enums.sql.Order;
import com.incs83.mt.DataAccessService;
import com.incs83.request.PaginatedRequest;

import lombok.extern.log4j.Log4j2;

@Repository
@Log4j2
public class EquipmentModelIspConfigRepo {

    @Autowired
    private DataAccessService dataAccessService;

    public boolean existsByEquipmentModelId(String equipmentModelId) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("equipmentModelId", equipmentModelId);
        List<Object> result = dataAccessService.readNative(EquipmentModelIspConfigSQL.EXISTS_BY_EQUIPMENT_MODEL_ID,
                params);

        if (CollectionUtils.isNotEmpty(result)) {
            Object count = result.get(0);
            return count != null && ((Number) count).longValue() > 0;
        }
        return false;
    }

    public boolean existsByFirmwareId(String firmwareId) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("firmwareId", firmwareId);
        List<Object> result = dataAccessService.readNative(EquipmentModelIspConfigSQL.EXISTS_BY_FIRMWARE_ID, params);

        if (CollectionUtils.isNotEmpty(result)) {
            Object count = result.get(0);
            return count != null && ((Number) count).longValue() > 0;
        }
        return false;
    }

    public Page<EquipmentModelIspConfig> findAll(PaginatedRequest paginatedRequest) {
        paginatedRequest.setSortBy(ApplicationConstants.ISP_ID);
        paginatedRequest.setOrder(Order.ASC);

        return dataAccessService.read(EquipmentModelIspConfig.class,
                new HashMap<>(),
                paginatedRequest,
                EquipmentModelIspConfigSQL.FROM_TABLE,
                EquipmentModelIspConfigSQL.GET_COUNT_FROM_EQUIPMENT_MODEL_ISP_CONFIG);

    }

    public Page<EquipmentModelIspConfig> findAll(int offset, int limit) {

        PaginatedRequest paginatedRequest = new PaginatedRequest();
        paginatedRequest.setOffset(offset);
        paginatedRequest.setMax(limit);

        return findAll(paginatedRequest);
    }

    public EquipmentModelIspConfig findById(String id) {
        return (EquipmentModelIspConfig) dataAccessService.read(EquipmentModelIspConfig.class, id);
    }

    public List<EquipmentModelIspConfig> findByIds(List<String> ids) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("ids", ids);
        return (List<EquipmentModelIspConfig>) dataAccessService.read(EquipmentModelIspConfig.class,
                EquipmentModelIspConfigSQL.FIND_BY_IDS, params);
    }

    public List<EquipmentModelIspConfig> findByIspIds(List<String> ispIds) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("ispIds", ispIds);
        return (List<EquipmentModelIspConfig>) dataAccessService.read(EquipmentModelIspConfig.class,
                EquipmentModelIspConfigSQL.FIND_BY_ISP_IDS, params);
    }

    public Page<EquipmentModelIspConfig> findByIspId(String ispId, int offset, int limit) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("ispId", ispId);

        PaginatedRequest paginatedRequest = new PaginatedRequest();
        paginatedRequest.setOffset(offset);
        paginatedRequest.setMax(limit);
        paginatedRequest.setOrder(Order.ASC);

        return dataAccessService.read(EquipmentModelIspConfig.class,
                params,
                paginatedRequest,
                EquipmentModelIspConfigSQL.FIND_BY_ISP_ID,
                EquipmentModelIspConfigSQL.GET_COUNT_FROM_EQUIPMENT_MODEL_ISP_CONFIG_BY_ISP_ID);
    }

    public List<EquipmentModelIspConfig> findByIspIdAndEquipmentModelId(String ispId, String equipmentModelId) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("ispId", ispId);
        params.put("equipmentModelId", equipmentModelId);

        return (List<EquipmentModelIspConfig>) dataAccessService.read(EquipmentModelIspConfig.class,
                EquipmentModelIspConfigSQL.FIND_BY_ISP_ID_AND_EQUIPMENT_MODEL_ID, params);
    }

    public Boolean create(String id, String ispId, String equipmentModelId,
            Firmware minFirmwareVersionId, String createBy) throws Exception {

        EquipmentModelIspConfig equipmentModelIspConfig = EquipmentModelIspConfig.builder()
                .id(id)
                .equipmentModelId(equipmentModelId)
                .ispId(ispId)
                .minVersionFirmware(minFirmwareVersionId)
                .createdBy(createBy)
                .updatedBy(createBy)
                .build();

        return dataAccessService.create(EquipmentModelIspConfig.class, equipmentModelIspConfig);
    }

    public Boolean create(List<EquipmentModelIspConfig> equipmentModelIspConfigs) throws Exception {
        return dataAccessService.create(EquipmentModelIspConfig.class, equipmentModelIspConfigs);
    }

    public EquipmentModelIspConfig update(String profileId, String equipmentModelId, String ispId,
            Firmware minFirmwareVersionId, String updatedBy) {

        EquipmentModelIspConfig equipmentModelIspConfig = EquipmentModelIspConfig.builder()
                .id(profileId)
                .equipmentModelId(equipmentModelId)
                .ispId(ispId)
                .minVersionFirmware(minFirmwareVersionId)
                .updatedBy(updatedBy)
                .build();

        return (EquipmentModelIspConfig) dataAccessService
                .update(EquipmentModelIspConfig.class, equipmentModelIspConfig);
    }

    public List<EquipmentModelIspConfig> update(List<EquipmentModelIspConfig> equipmentModelIspConfigs) {
        return (List<EquipmentModelIspConfig>) dataAccessService
                .update(EquipmentModelIspConfig.class, equipmentModelIspConfigs);
    }

    /**
     * equipmentModelId and ispId as a key for deletion
     * 
     * @param equipmentModelId
     * @param ispId
     * @throws Exception
     */
    public Boolean deleteById(String id) throws ValidationException {

        try {
            dataAccessService.delete(EquipmentModelIspConfig.class, id);
            return true;
        } catch (Exception e) {
            log.info("Error deleting EquipmentModelIspConfig with id {}: {}",
                    id, e.getMessage());
            throw e;
        }
    }

    public Boolean deleteByIds(List<String> ids) throws ValidationException {

        HashMap<String, Object> params = new HashMap<>();
        params.put("ids", ids);
        dataAccessService.delete(EquipmentModelIspConfigSQL.DELETE_BY_IDS, params);

        return true;
    }

}