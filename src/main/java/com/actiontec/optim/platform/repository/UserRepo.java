package com.actiontec.optim.platform.repository;

import java.util.HashMap;
import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.ObjectUtils;

import com.incs83.app.constants.queries.UserSQL;
import com.incs83.mt.DataAccessService;

@Repository
public class UserRepo {

    private final Logger logger = LogManager.getLogger(this.getClass());
    private final String SPACE = " ";

    @Autowired
    private DataAccessService dataAccessService;

    public String getFullNameById(String id) {
        HashMap<String, String> params = new HashMap<>();
        params.put("id", id);

        List<Object> users = dataAccessService.readNative(UserSQL.GET_USER_FULL_NAME_BY_ID, params);

        if (ObjectUtils.isEmpty(users)) {
            logger.error("User not found with id: {}", id);
            return StringUtils.EMPTY;
        }

        return (String) users.get(0);
    }

}
