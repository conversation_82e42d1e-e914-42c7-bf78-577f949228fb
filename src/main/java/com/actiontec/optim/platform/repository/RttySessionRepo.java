package com.actiontec.optim.platform.repository;

import com.actiontec.optim.platform.api.v6.dto.*;
import com.actiontec.optim.util.CustomStringUtils;
import com.incs83.app.constants.queries.RttySessionSQL;
import com.incs83.app.entities.RttySession;
import com.incs83.app.utils.SqlStatementUtils;
import com.incs83.exceptions.handler.ValidationException;
import com.incs83.mt.DataAccessService;
import com.incs83.util.CommonUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.StringJoiner;

import static com.incs83.app.utils.SqlStatementUtils.*;

@Component
public class RttySessionRepo {
    private final Logger logger = LogManager.getLogger(this.getClass());

    @Autowired
    private DataAccessService dataAccessService;

    public boolean createSession(RttySession rttySession) throws Exception {
        return dataAccessService.create(RttySession.class, rttySession);
    }

    /**
     * Get all RTTY sessions
     * @param queryDTO Query parameters
     * @return Paginated list of sessions
     * @throws Exception If query fails
     */
    public PaginationResponse<RttySessionDTO> getAllSessions(RttySessionQueryDTO queryDTO) throws Exception {
        // since we have two different queries, we need to build the query string manually without reflection.
        StringBuilder query;
        HashMap<String, Object> params = new HashMap<>();
        if (CustomStringUtils.isNotEmpty(queryDTO.getCreatorName())) {
            query = new StringBuilder(RttySessionSQL.GET_RTTY_SESSIONS_BY_CREATOR_NAME);
            params.put("creatorName", queryDTO.getCreatorName());
        } else if (CustomStringUtils.isNotEmpty(queryDTO.getSerialNumber())) {
            query = new StringBuilder(RttySessionSQL.GET_RTTY_SESSIONS_BY_SERIAL);
            params.put("serial", queryDTO.getSerialNumber());
        } else if (queryDTO.isCreatedByMe()) {
            query = new StringBuilder(RttySessionSQL.GET_RTTY_SESSIONS_BY_CREATED_BY);
            params.put("createdBy", CommonUtils.getUserIdOfLoggedInUser());
        } else {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Creator name or serial number cannot be empty");
        }

        StringJoiner whereClause;
        if (CustomStringUtils.isNotEmpty(queryDTO.getIspId())) {
            whereClause = new StringJoiner(SqlStatementUtils.Condition.AND, SqlStatementUtils.Condition.AND, CustomStringUtils.EMPTY);
            whereClause.add(buildEqualStatement(Alias.NCSEquipment, "isp_id", "ispId"));
            params.put("ispId", queryDTO.getIspId());
        } else {
            whereClause = new StringJoiner(SqlStatementUtils.Condition.AND, CustomStringUtils.EMPTY, CustomStringUtils.EMPTY);
        }

        String page = buildPageStatement(queryDTO.getOffset(), queryDTO.getLimit());
        String finalQuery = query.append(whereClause).append(page).toString();
        logger.info("getAllSessions finalQuery: " + finalQuery);

        List<Object[]> rawDataList = dataAccessService.readNative(finalQuery, params);
        PaginationResponse<RttySessionDTO> paginationResponse = new PaginationResponse<>();
        PaginationDTO paginationDTO = new PaginationDTO();
        List<RttySessionDTO> sessionDTOList = new ArrayList<>();

        if(CollectionUtils.isNotEmpty(rawDataList)) {
            for(Object[] rawData: rawDataList) {
                RttySessionDTO sessionDTO = new RttySessionDTO(rawData);
                sessionDTOList.add(sessionDTO);
            }

            if (CustomStringUtils.isNotEmpty(queryDTO.getCreatorName())) {
                paginationDTO.setTotal(countSessions(RttySessionSQL.RTTY_SESSIONS_BY_CREATOR_NAME_COUNT, whereClause, params));
            } else if (CustomStringUtils.isNotEmpty(queryDTO.getSerialNumber())) {
                paginationDTO.setTotal(countSessions(RttySessionSQL.RTTY_SESSIONS_BY_SERIAL_COUNT, whereClause, params));
            } else if (queryDTO.isCreatedByMe()) {
                paginationDTO.setTotal(countSessions(RttySessionSQL.RTTY_SESSIONS_BY_CREATED_BY_COUNT, whereClause, params));
            } else {
                paginationDTO.setTotal(0);
            }
            paginationDTO.setListSize(sessionDTOList.size());
        }

        paginationDTO.setOffset(queryDTO.getOffset());
        paginationDTO.setLimit(queryDTO.getLimit());

        paginationResponse.setPagination(paginationDTO);
        paginationResponse.setData(sessionDTOList);

        return paginationResponse;
    }

    /**
     * Count the number of sessions
     * @param countQuery Count query
     * @param whereClause WHERE clause
     * @param params Parameters
     * @return Number of sessions
     * @throws Exception If query fails
     */
    public Integer countSessions(String countQuery, StringJoiner whereClause, HashMap<String, Object> params) throws Exception {
        StringBuilder query = new StringBuilder(countQuery);
        String finalQuery = query.append(whereClause).toString();
        return Integer.valueOf(dataAccessService.readNative(finalQuery, params).iterator().next().toString());
    }

    public Integer countActiveSessions() {
        HashMap<String, String> params = new HashMap<>();
        return Integer.valueOf(dataAccessService.readNative(RttySessionSQL.ACTIVE_RTTY_SESSIONS_COUNT, params).iterator().next().toString());
    }

    /**
     * Get session by ID
     * @param id Session ID
     * @return Session details
     * @throws Exception If query fails
     */
    public RttySession getSessionById(String id) throws Exception {
        return (RttySession) dataAccessService.read(RttySession.class, id);
    }

    public RttySession getSessionByEquipmentId(String equipmentId) throws Exception {
        if (CustomStringUtils.isEmpty(equipmentId)) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Equipment id cannot be empty");
        }

        HashMap<String, String> params = new HashMap<>();
        params.put("equipmentId", equipmentId);
        List<RttySession> rttySessionList = dataAccessService.read(RttySession.class, RttySessionSQL.GET_SESSION_BY_EQUIPMENT_ID, params);
        if (CollectionUtils.isEmpty(rttySessionList)) {
            return null;
        }

        return rttySessionList.get(0);
    }

    /**
     * Update session
     * @param rttySession Session to update
     * @throws Exception If update fails
     */
    public void updateSession(RttySession rttySession) throws Exception {
        rttySession.setUpdatedAt(LocalDateTime.now());
        rttySession.setUpdatedBy(CommonUtils.getUserIdOfLoggedInUser());
        dataAccessService.update(RttySession.class, rttySession);
    }

    /**
     * Delete session
     * @param sessionId Session ID
     * @throws Exception If deletion fails
     */
    public void deleteSession(String sessionId) throws Exception {
        dataAccessService.delete(RttySession.class, sessionId);
    }
}
