package com.actiontec.optim.platform.repository;

import com.actiontec.optim.platform.adapter.At3Adapter;
import com.actiontec.optim.platform.api.v6.dto.*;
import com.actiontec.optim.util.CustomStringUtils;
import com.incs83.app.constants.queries.NCSEquipmentSQL;
import com.incs83.app.entities.NCSEquipment;
import com.incs83.app.utils.SqlStatementUtils;
import com.incs83.app.utils.SqlStatementUtils.Alias;
import com.incs83.exceptions.handler.ValidationException;
import com.incs83.mt.DataAccessService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.common.protocol.types.Field.Str;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.elasticsearch.cluster.ClusterState.Custom;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

import static com.incs83.app.utils.SqlStatementUtils.*;

@Component
public class NCSEquipmentRepo {
    private final Logger logger = LogManager.getLogger(this.getClass());

    @Autowired
    private DataAccessService dataAccessService;

    @Autowired
    At3Adapter at3Adapter;

    public PaginationResponse<NCSEquipmentDTO> getAllEquipment(NCSEquipmentQueryDTO queryDTO) throws Exception {
        StringBuilder query = new StringBuilder(NCSEquipmentSQL.GET_ALL_EQUIPMENTS);
        HashMap<String, Object> params = new HashMap<>();

        StringJoiner whereClause = new StringJoiner(SqlStatementUtils.Condition.AND, SqlStatementUtils.Condition.WHERE, StringUtils.EMPTY);
        // use reflection to generate dynamic query string.
        Field[] fields = queryDTO.getClass().getDeclaredFields();
        for (Field field : fields) {
            // allow access private attribute.
            field.setAccessible(true);
            Object fieldValue = field.get(queryDTO);
            if (field.getDeclaringClass().equals(PaginationRequest.class) || ObjectUtils.isEmpty(fieldValue)) {
                continue;
            }

            String columnNativeName = SqlStatementUtils.getNativeColumnName(field);

            if (StringUtils.equals(field.getName(), "serialNumber") || StringUtils.equals(field.getName(), "modelName")) {
                fieldValue = fieldValue + "%";
                whereClause.add(buildLikeStatement(Alias.NCSEquipment, columnNativeName, field.getName()));
            } else if (StringUtils.equals(field.getName(), "ispId") || StringUtils.equals(field.getName(), "networkId")) {
                whereClause.add(buildEqualStatement(Alias.NCSEquipment, columnNativeName, field.getName()));
            } else {
                whereClause.add(buildEqualStatement(Alias.NCSEquipment, columnNativeName, field.getName()));
            }
            logger.info("field name: {}, field value: {}", field.getName(), fieldValue.toString());

            params.put(field.getName(), fieldValue);
        }

        // query without any parameters
        if (params.isEmpty()) {
            whereClause = new StringJoiner(StringUtils.EMPTY, StringUtils.EMPTY, StringUtils.EMPTY);
        }

        String page = buildPageStatement((Objects.nonNull(queryDTO.getOffset()) ? queryDTO.getOffset() : 0),
                Objects.nonNull(queryDTO.getLimit()) ? queryDTO.getLimit() : 50);

        String finalQuery = query.append(whereClause).append(page).toString();
        logger.info("getAllEquipments finalQuery: " + finalQuery);

        List<Object[]> rawDataList = dataAccessService.readNative(finalQuery, params);
        PaginationResponse<NCSEquipmentDTO> paginationResponseDTO = new PaginationResponse<>();
        PaginationDTO paginationDTO = new PaginationDTO();
        List<NCSEquipmentDTO> equipmentDataList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(rawDataList)) {
            for (Object[] rawData : rawDataList) {
                NCSEquipmentDTO equipmentDTO = new NCSEquipmentDTO(rawData);
                equipmentDataList.add(equipmentDTO);
            }

            equipmentDataList = populateIspName(equipmentDataList);

            paginationDTO.setTotal(countEquipments(queryDTO, whereClause, params));
            paginationDTO.setListSize(equipmentDataList.size());
            paginationDTO.setOffset(queryDTO.getOffset());
            paginationDTO.setLimit(queryDTO.getLimit());

        }
        paginationResponseDTO.setPagination(paginationDTO);
        paginationResponseDTO.setData(equipmentDataList);

        return paginationResponseDTO;
    }

    private Integer countEquipments(NCSEquipmentQueryDTO queryDTO, StringJoiner whereClause, HashMap<String, Object> params) throws Exception {
        StringBuilder query = new StringBuilder(NCSEquipmentSQL.COUNT_ALL_EQUIPMENTS);
        String finalQuery = query.append(whereClause).toString();
        return Integer.valueOf(dataAccessService.readNative(finalQuery, params).iterator().next().toString());
    }

    // fill in ispName
    private List<NCSEquipmentDTO> populateIspName(List<NCSEquipmentDTO> equipmentDataList) {
        Map<String, String> ispMap = new HashMap<>();
        List<NCSEquipmentDTO> populatedList = new ArrayList<>();

        for (NCSEquipmentDTO equipment : equipmentDataList) {
            String ispId = equipment.getIspId();

            if (ispMap.containsKey(ispId)) {
                equipment.setIspName(ispMap.get(ispId));
            } else {
                String ispName = at3Adapter.getIspNameById(ispId);
                ispMap.put(ispId, ispName);
                equipment.setIspName(ispName);
            }
            populatedList.add(equipment);
        }

        return populatedList;
    }

    public List<NCSEquipment> getNCSEquipmentListByIspId(String ispId) throws Exception {
        HashMap<String, Object> params = new HashMap<>();
        params.put("ispId", ispId);
        return (List<NCSEquipment>) dataAccessService.read(NCSEquipment.class, NCSEquipmentSQL.GET_NCS_EQUIPMENT_BY_ISP_ID, params);
    }

    // new equipment table, contains all equipments (rg and extender)
    public List<NCSEquipment> getNCSEquipmentListByNetworkId(String networkId) throws Exception {
        HashMap<String, String> params = new HashMap<>();
        params.put("networkId", networkId);
        return (List<NCSEquipment>) dataAccessService.read(NCSEquipment.class, NCSEquipmentSQL.GET_NCS_EQUIPMENT_BY_NETWORK_ID, params);
    }

    public NCSEquipment getNCSEquipmentById(String id) throws Exception {
        HashMap<String, String> params = new HashMap<>();
        params.put("id", id);
        List<NCSEquipment> ncsEquipmentList = (List<NCSEquipment>) dataAccessService.read(NCSEquipment.class, NCSEquipmentSQL.GET_NCS_EQUIPMENT_BY_ID,params);
        if (CollectionUtils.isEmpty(ncsEquipmentList)) {
            throw new ValidationException(HttpStatus.NOT_FOUND.value(), "No ncs_equipment found with id " + id);
        }
        return ncsEquipmentList.get(0);
    }

    public List<NCSEquipment> getNCSEquipmentListBySerialList(List<String> serialList) throws Exception {
        HashMap<String, Object> params = new HashMap<>();
        params.put("serialList", serialList);
        logger.info("serialList: " + serialList.toString());
        return (List<NCSEquipment>) dataAccessService.read(NCSEquipment.class, NCSEquipmentSQL.GET_NCS_EQUIPMENT_BY_SERIAL_LIST, params);
    }

    public NCSEquipment getNCSEquipmentBySerial(String serial) throws Exception {
        HashMap<String, String> params = new HashMap<>();
        params.put("serial", serial);
        List<NCSEquipment> ncsEquipmentList = (List<NCSEquipment>) dataAccessService.read(NCSEquipment.class, NCSEquipmentSQL.GET_NCS_EQUIPMENT_BY_SERIAL,params);
        if (CollectionUtils.isEmpty(ncsEquipmentList)) {
            return null;
        }
        return ncsEquipmentList.get(0);
    }

    public void update(NCSEquipment ncsEquipment) throws Exception {
        dataAccessService.update(NCSEquipment.class, ncsEquipment);
    }

    public String getFriendlyNameById(String id) {
        HashMap<String, String> params = new HashMap<>();
        params.put("id", id);
        List<String> result = (List<String>) 
            dataAccessService.read(NCSEquipment.class,
                NCSEquipmentSQL.GET_NCS_EQUIPMENT_FRIENDLY_NAME_BY_ID, params);

        if (CollectionUtils.isEmpty(result)) {
            return CustomStringUtils.EMPTY;
        }
        return result.get(0);
    }

    public void updateFriendlyNameById(String id, String friendlyName) throws Exception {
        HashMap<String, Object> params = new HashMap<>();
        params.put("id", id);
        params.put("friendlyName", friendlyName);
        dataAccessService.update(NCSEquipment.class, 
            NCSEquipmentSQL.UPDATE_NCS_EQUIPMENT_FRIENDLY_NAME_BY_ID, params);
    }

    public void updateNetworkAndSubscriberByIdList(String ispId, List<String> equipmentIdList, String networkId, String subscriberId) throws Exception {
        StringBuilder query = new StringBuilder(NCSEquipmentSQL.UPDATE_NETWORK_AND_SUBSCRIBER_BY_ID_LIST_AND_ISP_ID);
        HashMap<String, Object> queryParameters = new HashMap<>();
        queryParameters.put("ispId", ispId);
        queryParameters.put("equipmentIdList", equipmentIdList);
        queryParameters.put("networkId", networkId);
        queryParameters.put("subscriberId", subscriberId);
        dataAccessService.update(NCSEquipment.class, query.toString(), queryParameters);
    }

    public void deleteById(String id) throws Exception {
        dataAccessService.delete(NCSEquipment.class, id);
    }

    public void clearSubscriberMappingBySubscriberId(String subscriberId) throws Exception {
        HashMap<String, Object> params = new HashMap<>();
        params.put("subscriberId", subscriberId);
        dataAccessService.deleteUpdateNative(NCSEquipmentSQL.CLEAR_EQUIPMENT_SUBSCRIBER_BY_SUBSCRIBER_ID, params);
    }

    public void clearNetworkMappingByNetworkId(String networkId) throws Exception {
        HashMap<String, Object> params = new HashMap<>();
        params.put("networkId", networkId);
        dataAccessService.deleteUpdateNative(NCSEquipmentSQL.CLEAR_EQUIPMENT_NETWORK_BY_NETWORK_ID, params);
    }

    public boolean checkEquipmentListByIspId(String ispId, List<String> equipmentIdList) throws Exception {
        Set<String> ispEquipmentIdList = getNCSEquipmentListByIspId(ispId).stream().map(NCSEquipment::getId).collect(Collectors.toSet());
        return ispEquipmentIdList.containsAll(equipmentIdList);
    }
}
