package com.actiontec.optim.platform.api.v6.controller;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.actiontec.optim.platform.api.v6.dto.CustomerProfileDTO;
import com.actiontec.optim.platform.api.v6.dto.CustomerProfileRequest;
import com.actiontec.optim.platform.api.v6.dto.CustomerProfileRequest.Create;
import com.actiontec.optim.platform.api.v6.dto.CustomerProfileRequest.Patch;
import com.actiontec.optim.platform.api.v6.dto.IdDTO;
import com.actiontec.optim.platform.api.v6.dto.PaginationResponse;
import com.actiontec.optim.platform.service.CustomerProfileService;
import com.actiontec.optim.service.AuditService;
import com.actiontec.optim.util.ValidationUtils;
import com.incs83.util.CommonUtils;

@RestController
@RequestMapping(value = "/actiontec/api/v6/customer/profiles")
public class CustomerProfileController {

    private final Logger logger = LogManager.getLogger(this.getClass());
    private final String AUDIT_LOG_CATEGORY = "Customer Profile";

    @Autowired
    private CustomerProfileService customerProfileService;

    @Autowired
    private AuditService auditService;

    @GetMapping
    public ResponseEntity<PaginationResponse<CustomerProfileDTO>> getAllCustomerProfiles(
            @ModelAttribute @Valid CustomerProfileRequest request,
            HttpServletRequest httpServletRequest) throws Exception {

        // only allow system or group administrators
        ValidationUtils.validateIsSysAdminOrGroupAdmin();

        PaginationResponse<CustomerProfileDTO> customerProfileResponse 
            = customerProfileService.getAllCustomerProfiles(request);

        auditService.createAuditLog(CommonUtils.getUserIdOfLoggedInUser(), "",
            AUDIT_LOG_CATEGORY, request, HttpStatus.OK.toString(),
            customerProfileResponse, httpServletRequest);

        return ResponseEntity.ok(customerProfileResponse);
    }

    @GetMapping("/{profileId}")
    public ResponseEntity<CustomerProfileDTO> getCustomerProfile(
            @PathVariable String profileId, HttpServletRequest httpServletRequest) throws Exception {

        
        // only allow system or group administrators
        ValidationUtils.validateIsSysAdminOrGroupAdmin();

        CustomerProfileDTO customerProfile 
        = customerProfileService.getCustomerProfile(profileId);

        auditService.createAuditLog(CommonUtils.getUserIdOfLoggedInUser(), "",
            AUDIT_LOG_CATEGORY, null, HttpStatus.OK.toString(),
            customerProfile, httpServletRequest);

        return ResponseEntity.ok(customerProfile);
    }

    @PostMapping
    public ResponseEntity<IdDTO> createCustomerProfile(
            @RequestBody @Validated(Create.class) CustomerProfileRequest customerProfileRequest,
            HttpServletRequest httpServletRequest) throws Exception {
        // only allow system
        ValidationUtils.validateIsSystemAdmin();

        IdDTO rsp = customerProfileService.createCustomerProfile(customerProfileRequest);

        auditService.createAuditLog(CommonUtils.getUserIdOfLoggedInUser(), "",
            AUDIT_LOG_CATEGORY, customerProfileRequest, HttpStatus.CREATED.toString(),
            rsp, httpServletRequest);

        return ResponseEntity.status(HttpStatus.CREATED)
                .body(rsp);
    }

    @PatchMapping("/{profileId}")
    public ResponseEntity<IdDTO> updateCustomerProfile(
            @PathVariable String profileId,
            @RequestBody @Validated(Patch.class) CustomerProfileRequest customerProfileRequest,
            HttpServletRequest httpServletRequest) throws Exception {

        // only allow system
        ValidationUtils.validateIsSystemAdmin();

        IdDTO rsp 
        = customerProfileService.updateCustomerProfile(profileId, customerProfileRequest);

        auditService.createAuditLog(CommonUtils.getUserIdOfLoggedInUser(), "",
            AUDIT_LOG_CATEGORY, null, HttpStatus.OK.toString(),
            rsp, httpServletRequest);

        return ResponseEntity.status(HttpStatus.CREATED)
                .body(rsp);
    }

    @DeleteMapping("/{profileId}")
    public ResponseEntity<IdDTO> deleteCustomerProfile(@PathVariable String profileId,
            HttpServletRequest httpServletRequest) throws Exception {

        // only allow system
        ValidationUtils.validateIsSystemAdmin();

        IdDTO rsp = customerProfileService.deleteCustomerProfile(profileId);

        auditService.createAuditLog(CommonUtils.getUserIdOfLoggedInUser(), "",
            AUDIT_LOG_CATEGORY, null, HttpStatus.NO_CONTENT.toString(),
            rsp, httpServletRequest);

        return ResponseEntity.status(HttpStatus.NO_CONTENT)
                .body(rsp);
    }
}
