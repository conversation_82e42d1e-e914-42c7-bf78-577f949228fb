package com.actiontec.optim.platform.api.v6.dto;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

public class SystemConfigDTO {
    @NotNull
    @Size(min = 8, max = 63, message = "Password should be between 8-63 characters")
    @Pattern(regexp = "^[\\x21-\\x7e]+$", message = "Password must contain only ASCII printable characters (no spaces)")
    private String password;

    @NotNull
    private String timezone;

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getTimezone() {
        return timezone;
    }

    public void setTimezone(String timezone) {
        this.timezone = timezone;
    }
}
