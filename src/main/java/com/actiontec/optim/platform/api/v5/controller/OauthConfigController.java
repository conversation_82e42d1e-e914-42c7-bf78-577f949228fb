package com.actiontec.optim.platform.api.v5.controller;

import com.actiontec.optim.platform.adapter.At3Adapter;
import com.actiontec.optim.platform.api.v5.mapper.OauthConfigResponseMapper;
import com.actiontec.optim.platform.api.v5.model.OauthConfigRequest;
import com.actiontec.optim.platform.api.v5.model.OauthConfigResponse;
import com.actiontec.optim.platform.model.OauthConfig;
import com.actiontec.optim.platform.service.OauthConfigService;
import com.incs83.annotation.PreHandle;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping(value = "/actiontec/api/v5/iam/oauth/configurations")
public class OauthConfigController {

    @Autowired
    private OauthConfigResponseMapper oauthConfigResponseMapper;

    @Autowired
    private OauthConfigService oauthConfigService;

    @Autowired
    private At3Adapter at3Adapter;

    @PreHandle(requestMethod = RequestMethod.GET, resourceType = OauthConfig.class)
    @RequestMapping(method = RequestMethod.GET, produces = "application/json")
    public List<OauthConfigResponse> getOauthConfigs(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {

        List<OauthConfig> oauthConfigList = oauthConfigService.getOauthConfigs();
        List<OauthConfigResponse> oauthConfigResponseList = oauthConfigResponseMapper.toOauthConfigResponses(oauthConfigList);
        return oauthConfigResponseList;
    }

    @PreHandle(requestMethod = RequestMethod.POST, resourceType = OauthConfig.class)
    @RequestMapping(method = RequestMethod.POST, produces = "application/json")
    @ResponseStatus(HttpStatus.CREATED)
    public Map<String, String> createOauthConfig(
            @ApiParam(value = "App properties", required = true) @Valid @RequestBody OauthConfigRequest oauthConfigRequest,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {

        OauthConfig oauthConfig = oauthConfigResponseMapper.toOauthConfig(oauthConfigRequest);

        // Verify isp id is valid, Not a good solution, needs to be refactored.
        for(int i=0; i<oauthConfig.getIspIds().size(); i++) {
            String ispId = oauthConfig.getIspIds().get(i);
            at3Adapter.getIspNameById(ispId);
        }

        // Verify role id is valid
        String roleName =  at3Adapter.getRoleNameById(oauthConfig.getRoleId());
        if (roleName.equals("")) {
            throw new RuntimeException("Role Not Found");
        }

        Map<String, String> result = oauthConfigService.addOauthConfig(oauthConfig);
        return result;
    }

    @PreHandle(requestMethod = RequestMethod.GET, resourceType = OauthConfig.class)
    @RequestMapping(value = "/{configId}", method = RequestMethod.GET, produces = "application/json")
    public List<OauthConfigResponse> getOauthConfig(
            @ApiParam(value = "configId", required = true) @PathVariable(name = "configId") String configId,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {

        List<OauthConfigResponse> oauthConfigResponseList = new ArrayList<>();
        OauthConfig oauthConfig = oauthConfigService.getOauthConfigById(configId);
        OauthConfigResponse oauthConfigResponse = oauthConfigResponseMapper.toOauthConfigResponse(oauthConfig);
        oauthConfigResponseList.add(oauthConfigResponse);
        return oauthConfigResponseList;
    }

    @PreHandle(requestMethod = RequestMethod.PUT, resourceType = OauthConfig.class)
    @RequestMapping(value = "/{configId}", method = RequestMethod.PUT, produces = "application/json")
    public void updateOauthConfig(
            @ApiParam(value = "configId", required = true) @PathVariable(name = "configId") String configId,
            @ApiParam(value = "App properties", required = true) @RequestBody OauthConfigRequest oauthConfigRequest,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {

        OauthConfig oauthConfig = oauthConfigResponseMapper.toOauthConfig(oauthConfigRequest);
        oauthConfigService.updateOauthConfig(configId, oauthConfig);
    }

    @PreHandle(requestMethod = RequestMethod.DELETE, resourceType = OauthConfig.class)
    @RequestMapping(value = "/{configId}", method = RequestMethod.DELETE, produces = "application/json")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public void deleteOauthConfig(
            @ApiParam(value = "configId", required = true) @PathVariable(name = "configId") String configId,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {

        oauthConfigService.deleteOauthConfig(configId);
    }
}
