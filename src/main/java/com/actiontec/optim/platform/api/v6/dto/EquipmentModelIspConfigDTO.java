package com.actiontec.optim.platform.api.v6.dto;

import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.util.ObjectUtils;

import com.incs83.app.entities.EquipmentModelIspConfig;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class EquipmentModelIspConfigDTO {

    private String id;

    private String ispId;

    private String modelId;

    private String requiredFirmwareId;

    private String lastChangedBy;

    @DateTimeFormat(pattern = "yyyy-MM-dd' 'HH:mm:ss")
    private Long lastChangedTime;

    public static EquipmentModelIspConfigDTO buildEquipmentModelIspConfigDTO(EquipmentModelIspConfig config,
            String updatedByUserName) {

        String requiredFirmwareId = !ObjectUtils.isEmpty(config.getMinVersionFirmware())
                ? config.getMinVersionFirmware().getId()
                : null;

        return EquipmentModelIspConfigDTO.builder()
                .id(config.getId())
                .ispId(config.getIspId())
                .modelId(config.getEquipmentModelId())
                .requiredFirmwareId(requiredFirmwareId)
                .lastChangedTime(config.getUpdatedAt().getTime())
                .lastChangedBy(updatedByUserName)
                .build();
    }

}
