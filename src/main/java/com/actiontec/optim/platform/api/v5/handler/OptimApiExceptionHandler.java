package com.actiontec.optim.platform.api.v5.handler;

import com.actiontec.optim.platform.api.v5.exception.OptimApiException;
import com.actiontec.optim.platform.api.v6.dto.ErrorResponseDTO;
import com.actiontec.optim.platform.exception.*;
import com.incs83.dto.ResponseDTO;
import com.incs83.exceptions.ApiException;
import com.incs83.exceptions.handler.AuthEntityNotAllowedException;
import com.incs83.exceptions.handler.AuthMethodNotSupportedException;
import com.incs83.exceptions.handler.ConflictException;
import com.incs83.exceptions.handler.OAUTH2AuthenticationException;
import com.incs83.exceptions.handler.OAUTH2ConfigException;
import com.incs83.exceptions.handler.OAUTH2InvalidCodeException;
import com.incs83.exceptions.handler.ValidationException;
import com.incs83.util.ResponseUtil;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.validation.BindException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.multipart.MultipartException;

import java.util.HashMap;
import java.util.LinkedHashMap;

@ControllerAdvice
@Order(1)
public class OptimApiExceptionHandler {
    private final Logger logger = LogManager.getLogger(this.getClass());

    @Autowired
    private ResponseUtil responseUtil;

    @Autowired
    private MessageSource messageSource;

    @ExceptionHandler({OptimApiException.class})
    public ResponseEntity<ErrorResponseDTO> handleGenericException(OptimApiException optimApiException) {
        return ResponseEntity.status(optimApiException.getStatus()).body(optimApiException.toErrorResponseDTO());
    }

    @ExceptionHandler({UnAuthException.class})
    public ResponseEntity<HashMap<String, Object>> handleUnAuthException(UnAuthException e) {
        HashMap<String, Object> resultMap = new HashMap<>();
        resultMap.put("message", "Mismatched ISP");
        resultMap.put("unsubscribed", true);

        return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(resultMap);
    }

    @ExceptionHandler({NoSuchEntityException.class})
    public ResponseEntity<HashMap<String, Object>> handleGenericException(NoSuchEntityException e) {
        HashMap<String, Object> resultMap = new HashMap<>();
        resultMap.put("message", "no such resource");

        return ResponseEntity.status(HttpStatus.NOT_FOUND).body(resultMap);
    }

    @ExceptionHandler({NoSuchModuleException.class})
    public ResponseEntity<HashMap<String, Object>> handleGenericException(NoSuchModuleException e) {
        HashMap<String, Object> resultMap = new HashMap<>();
        resultMap.put("message", "no such resource");

        return ResponseEntity.status(HttpStatus.NOT_FOUND).body(resultMap);
    }

    @ExceptionHandler({CpeFirmwareNotSupportException.class})
    public ResponseEntity<HashMap<String, Object>> handleGenericException(CpeFirmwareNotSupportException e) {
        HashMap<String, Object> resultMap = new HashMap<>();
        resultMap.put("errorCode", 10001);
        resultMap.put("message", "CPE firmware not support.");

        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(resultMap);
    }

    @ExceptionHandler({CpeRpcResultException.class})
    public ResponseEntity<HashMap<String, Object>> handleGenericException(CpeRpcResultException e) {
        HashMap<String, Object> resultMap = new HashMap<>();
        resultMap.put("errorCode", 10002);
        resultMap.put("message", "CPE RPC invoking failed.");

        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(resultMap);
    }

    @ResponseStatus(value = HttpStatus.BAD_REQUEST)
    @ExceptionHandler(value = {MultipartException.class})
    public ResponseEntity<HashMap<String, Object>> handleMultipartException(MultipartException e) {
        logger.error(String.format("IotBackendApi: Got [[%s]] exception with message: %s", e.getClass().getName(), e));
        HashMap<String, Object> resultMap = new HashMap<>();
        resultMap.put("errorCode", 9000);
        resultMap.put("message", "Maximum upload size exceeded");
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(resultMap);
    }

//    @ResponseStatus(value = HttpStatus.NOT_FOUND)
//    @ExceptionHandler(value = UrlNotFoundException.class)
//    public ResponseDTO exception(UrlNotFoundException e) {
//        logger.error(String.format("IotBackendApi: Got [[%s]] exception with message: %s", e.getClass().getName(), e.getMessage()));
//        return responseUtil.validationFailed(HttpStatus.NOT_FOUND.value(), e.getMessage());
//    }

//    @ResponseStatus(value = HttpStatus.METHOD_NOT_ALLOWED)
//    @ExceptionHandler(value = HttpRequestMethodNotSupportedException.class)
//    public ResponseDTO exception(MethodNotSupportedException e) {
//        LOG.error(String.format("IotBackendApi: Got [[%s]] exception with message: %s", e.getClass().getName(), e.getMessage()));
//        return responseUtil.validationFailed(HttpStatus.METHOD_NOT_ALLOWED.value(), e.getMessage());
//    }

    @ResponseStatus(value = HttpStatus.INTERNAL_SERVER_ERROR)
    @ExceptionHandler(value = {ApiException.class})
    public ResponseEntity<HashMap<String, Object>> handleApiException(ApiException e) {
        logger.error(String.format("IotBackendApi: Got [[%s]] exception with message: %s", e.getClass().getName(), e));
        ResponseDTO responseDTO = responseUtil.exception(e.getCode());

        HashMap<String, Object> resultMap = new HashMap<>();
        resultMap.put("code", responseDTO.getCode());
        resultMap.put("message", responseDTO.getMessage());
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(resultMap);
    }

    @ResponseStatus(value = HttpStatus.BAD_REQUEST)
    @ExceptionHandler(value = {ValidationException.class})
    public ResponseEntity<HashMap<String, Object>> handleValidationException(ValidationException e) {
        logger.error(String.format("IotBackendApi: Got [[%s]] exception with message: %s", e.getClass().getName(), e.getMessage()));
        HashMap<String, Object> resultMap = new HashMap<>();
        resultMap.put("code", e.getCode());
        resultMap.put("message", e.getMessage());
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(resultMap);
    }

    @ResponseStatus(value = HttpStatus.BAD_REQUEST)
    @ExceptionHandler(value = {OAUTH2AuthenticationException.class})
    public ResponseEntity<HashMap<String, Object>> handleOauthException(OAUTH2AuthenticationException e) {
        logger.error(String.format("IotBackendApi: Got [[%s]] exception with message: %s", e.getClass().getName(), e.getMessage()));
        HashMap<String, Object> resultMap = new HashMap<>();
        resultMap.put("code", e.getCode());
        resultMap.put("message", e.getMessage());
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(resultMap);
    }

    @ResponseStatus(value = HttpStatus.BAD_REQUEST)
    @ExceptionHandler(value = {OAUTH2ConfigException.class})
    public ResponseEntity<HashMap<String, Object>> handleOauthException(OAUTH2ConfigException e) {
        logger.error(String.format("IotBackendApi: Got [[%s]] exception with message: %s", e.getClass().getName(), e.getMessage()));
        HashMap<String, Object> resultMap = new HashMap<>();
        resultMap.put("code", e.getCode());
        resultMap.put("message", e.getMessage());
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(resultMap);
    }

    @ResponseStatus(value = HttpStatus.BAD_REQUEST)
    @ExceptionHandler(value = {OAUTH2InvalidCodeException.class})
    public ResponseEntity<HashMap<String, Object>> handleOauthException(OAUTH2InvalidCodeException e) {
        logger.error(String.format("IotBackendApi: Got [[%s]] exception with message: %s", e.getClass().getName(), e.getMessage()));
        HashMap<String, Object> resultMap = new HashMap<>();
        resultMap.put("code", e.getCode());
        resultMap.put("message", e.getMessage());
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(resultMap);
    }

//    @ResponseStatus(value = HttpStatus.BAD_REQUEST)
//    @ExceptionHandler(value = {TypeMismatchException.class})
//    public ResponseDTO exception(TypeMismatchException e) {
//        ValidationErrorDTO errorResponse = new ValidationErrorDTO();
//        errorResponse.addError(new ValidationErrorDTO.FieldErrorDTO(e.getErrorCode(), messageSource.getMessage(e.getMessage(), null, null)));
//        logger.error("Invalid / Missing Parameters " + errorResponse);
//        logger.error(String.format("IotBackendApi: Got [[%s]] exception with message: %s", e.getClass().getName(), e.getMessage()), e.getCause());
//        return responseUtil.validationFailed(HttpStatus.BAD_REQUEST.value(), errorResponse);
//    }

    @ResponseStatus(value = HttpStatus.FORBIDDEN)
    @ExceptionHandler(value = {AuthMethodNotSupportedException.class})
    public ResponseEntity<HashMap<String, Object>> handleAuthMethodNotSupportedException(AuthMethodNotSupportedException e) {
        logger.error(String.format("IotBackendApi: Got [[%s]] exception with message: %s", e.getClass().getName(), e.getMessage()));
        HashMap<String, Object> resultMap = new HashMap<>();
        resultMap.put("code", e.getCode());
        resultMap.put("message", e.getMessage());
        return ResponseEntity.status(HttpStatus.FORBIDDEN).body(resultMap);
    }

    @ResponseStatus(value = HttpStatus.FORBIDDEN)
    @ExceptionHandler(value = {AuthEntityNotAllowedException.class})
    public ResponseEntity<HashMap<String, Object>> handleAuthEntityNotAllowedException(AuthEntityNotAllowedException e) {
        logger.error(String.format("IotBackendApi: Got [[%s]] exception with message: %s", e.getClass().getName(), e.getMessage()));
        HashMap<String, Object> resultMap = new HashMap<>();
        resultMap.put("code", e.getCode());
        resultMap.put("message", e.getMessage());
        return ResponseEntity.status(HttpStatus.FORBIDDEN).body(resultMap);
    }

//    @ResponseStatus(value = HttpStatus.BAD_REQUEST)
//    @ExceptionHandler(value = {HttpMessageConversionException.class})
//    public ResponseDTO exception(HttpMessageConversionException e) {
//        logger.error(String.format("IotBackendApi: Got [[%s]] exception with message: %s", e.getClass().getName(), e.getMessage()));
//        return responseUtil.exception(ApiResponseCode.BAD_REQUEST);
//    }

    @ExceptionHandler(value = {BindException.class})
    public ResponseEntity<HashMap<String, Object>> exception(BindException e) {
        StringBuilder sb = new StringBuilder();
        e.getBindingResult().getFieldErrors().forEach(fieldError -> {
            logger.info(String.format("IotBackendApi: Got [[%s]] exception with message: %s",
                fieldError.getClass().getName(), fieldError.getDefaultMessage()));
            sb.append(fieldError.getField()).append(" ").append(fieldError.getDefaultMessage()).append("; ");
        });

        HashMap<String, Object> resultMap = new HashMap<>();
        resultMap.put("message", sb.toString());
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(resultMap);
    }

//    @ResponseStatus(value = HttpStatus.INTERNAL_SERVER_ERROR)
//    @ExceptionHandler(value = {ServletException.class})
//    public ResponseDTO exception(ServletException e) {
//        LOG.error(String.format("IotBackendApi: Got [[%s]] exception with message: %s", e.getClass().getName(), e.getMessage()));
//        return responseUtil.exception(ApiResponseCode.ERROR_PROCESSING_REQUEST);
//    }

    @ResponseStatus(value = HttpStatus.BAD_REQUEST)
    @ExceptionHandler(value = MethodArgumentNotValidException.class)
    public ResponseEntity<HashMap<String, Object>> exception(MethodArgumentNotValidException ex) {
        HashMap<String, Object> responseBody = new LinkedHashMap<>();
        responseBody.put("status", HttpStatus.BAD_REQUEST.value());
        responseBody.put("error", "Bad request parameter");

//        List<Map<String, Object>> fieldErrors = ex.getBindingResult().getFieldErrors()
//                .stream()
//                .map(fieldError -> {
//                    Map<String, Object> error = new LinkedHashMap<>();
//                    error.put("field", fieldError.getField());
//                    error.put("message", fieldError.getDefaultMessage());
//                    error.put("rejectedValue", fieldError.getRejectedValue());
//                    return error;
//                })
//                .collect(Collectors.toList());
//
//        responseBody.put("fieldErrors", fieldErrors);

        return ResponseEntity
                .badRequest()
                .body(responseBody);
    }

//    @ResponseStatus(value = HttpStatus.BAD_REQUEST)
//    @ExceptionHandler(value = MissingServletRequestParameterException.class)
//    public ResponseDTO exception(MissingServletRequestParameterException e) {
//        ValidationErrorDTO errorResponse = new ValidationErrorDTO();
//        errorResponse.addError(new ValidationErrorDTO.FieldErrorDTO(e.getParameterName(), messageSource.getMessage(e.getMessage(), null, null)));
//        LOG.error("Invalid / Missing Parameters " + errorResponse);
//        LOG.error(String.format("IotBackendApi: Got [[%s]] exception with message: %s", e.getClass().getName(), e.getMessage()), e.getCause());
//        return responseUtil.validationFailed(HttpStatus.BAD_REQUEST.value(), errorResponse);
//    }

//    @ResponseStatus(value = HttpStatus.BAD_REQUEST)
//    @ExceptionHandler(value = MethodArgumentTypeMismatchException.class)
//    public ResponseDTO exception(MethodArgumentTypeMismatchException e) {
//        ValidationErrorDTO errorResponse = new ValidationErrorDTO();
//        errorResponse.addError(new ValidationErrorDTO.FieldErrorDTO(e.getMessage(), messageSource.getMessage(e.getMessage(), null, null)));
//        LOG.error("Data Type Mismatch for Parameter(s) " + errorResponse);
//        LOG.error(String.format("IotBackendApi: Got [[%s]] exception with message: %s", e.getClass().getName(), e.getMessage()), e.getCause());
//        return responseUtil.validationFailed(HttpStatus.BAD_REQUEST.value(), errorResponse);
//    }

//    @ResponseStatus(value = HttpStatus.BAD_REQUEST)
//    @ExceptionHandler(value = ServletRequestBindingException.class)
//    public ResponseDTO exception(ServletRequestBindingException e) {
//        ValidationErrorDTO errorResponse = new ValidationErrorDTO();
//        errorResponse.addError(new ValidationErrorDTO.FieldErrorDTO(e.getMessage(), messageSource.getMessage(e.getMessage(), null, null)));
//        LOG.error("Invalid / Missing Parameters " + errorResponse);
//        LOG.error(String.format("IotBackendApi: Got [[%s]] exception with message: %s", e.getClass().getName(), e.getMessage()), e.getCause());
//        return responseUtil.validationFailed(HttpStatus.BAD_REQUEST.value(), e.getMessage());
//    }

    @ResponseStatus(value = HttpStatus.UNAUTHORIZED)
    @ExceptionHandler(value = BadCredentialsException.class)
    public ResponseEntity<HashMap<String, Object>> handleBadCredentialsException(BadCredentialsException e) {
        logger.error(String.format("IotBackendApi: Got [[%s]] exception with message: %s", e.getClass().getName(), e.getMessage()));
        HashMap<String, Object> resultMap = new HashMap<>();
        resultMap.put("code", HttpStatus.UNAUTHORIZED.value());
        resultMap.put("message", e.getMessage());
        return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(resultMap);
    }

    @ResponseStatus(value = HttpStatus.CONFLICT)
    @ExceptionHandler(value = {ConflictException.class})
    public ResponseEntity<HashMap<String, Object>> handleConflictException(ConflictException e) {
        logger.error(String.format("IotBackendApi: Got [[%s]] exception with message: %s", e.getClass().getName(), e.getMessage()));
        HashMap<String, Object> resultMap = new HashMap<>();
        resultMap.put("code", e.getCode());
        resultMap.put("message", e.getMessage());
        return ResponseEntity.status(HttpStatus.CONFLICT).body(resultMap);
    }

    @ExceptionHandler({EntityOverlapException.class})
    public ResponseEntity<HashMap<String, Object>> handleGenericException(EntityOverlapException e) {
        HashMap<String, Object> resultMap = new HashMap<>();
        resultMap.put("message", "resource conflict error.");

        return ResponseEntity.status(HttpStatus.CONFLICT).body(resultMap);
    }

    @ExceptionHandler({PermissionNotSupportException.class})
    public ResponseEntity<HashMap<String, Object>> handleGenericException(PermissionNotSupportException e) {
        HashMap<String, Object> resultMap = new HashMap<>();
        resultMap.put("message", "no permission to handle.");

        return ResponseEntity.status(HttpStatus.FORBIDDEN).body(resultMap);
    }

    @ExceptionHandler(value = Exception.class)
    public ResponseEntity<HashMap<String, Object>> handleException(Exception e) {
        logger.error("api unexpected exception.", e);
        HashMap<String, Object> resultMap = new HashMap<>();
        resultMap.put("errorCode", 10000);
        //resultMap.put("message", "Internal server error.");
        resultMap.put("message", e.getMessage());

        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(resultMap);
    }

    @ExceptionHandler(value = {RpcException.class})
    public ResponseEntity<HashMap<String, Object>> handleRpcTimeoutException(RpcException e) {
        logger.error(String.format("RPC Exception: Got [[%s]] exception with message: %s", e.getClass().getName(), e.getMessage()));
        HashMap<String, Object> resultMap = new HashMap<>();
        resultMap.put("code", e.getCode());
        resultMap.put("message", e.getMessage());
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(resultMap);
    }

    @ResponseStatus(value = HttpStatus.BAD_REQUEST)
    @ExceptionHandler(value = {HttpMessageNotReadableException.class})
    public ResponseEntity<HashMap<String, Object>> handleHttpMessageNotReadableException(HttpMessageNotReadableException e) {
        logger.error(String.format("IotBackendApi: Got [[%s]] exception with message: %s", e.getClass().getName(), e.getMessage()));
        HashMap<String, Object> resultMap = new HashMap<>();
        resultMap.put("code", HttpStatus.BAD_REQUEST.value());
        resultMap.put("message", "Invalid request body");
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(resultMap);
    }
}