package com.actiontec.optim.platform.api.v6.dto;

import javax.validation.constraints.NotBlank;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@Data
@EqualsAndHashCode(callSuper = true)
public class CustomerProfileRequest extends PaginationRequest {

    public interface Create {}
    public interface Patch {}

    @NotBlank(groups = {Create.class, Patch.class}, message = "ISP ID must not be blank")
    private String ispId;

    @NotBlank(groups = {Create.class, Patch.class}, message = "Model ID must not be blank")
    private String modelId;

    @NotBlank(groups = {Create.class, Patch.class},  message = "Firmware Version must not be blank")
    private String requiredFirmwareId;

}
