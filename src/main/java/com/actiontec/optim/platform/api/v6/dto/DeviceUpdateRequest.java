package com.actiontec.optim.platform.api.v6.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * DTO for device update request
 */
public class DeviceUpdateRequest {
    private String name;
    @JsonProperty("type")
    private String userDefineDeviceType;
    private Boolean verified;

    public DeviceUpdateRequest() {}

    public DeviceUpdateRequest(String name, String userDefineDeviceType, Boolean verified) {
        this.name = name;
        this.userDefineDeviceType = userDefineDeviceType;
        this.verified = verified;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getUserDefineDeviceType() {
        return userDefineDeviceType;
    }

    public void setUserDefineDeviceType(String userDefineDeviceType) {
        this.userDefineDeviceType = userDefineDeviceType;
    }

    public Boolean getVerified() {
        return verified;
    }

    public void setVerified(Boolean verified) {
        this.verified = verified;
    }
}
