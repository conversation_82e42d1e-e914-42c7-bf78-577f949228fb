package com.actiontec.optim.platform.api.v6.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.incs83.app.entities.NCSNetwork;

import java.util.List;

public class NetworkOperationsForAppCloudDTO extends NetworkOperationsDTO {
    private IspInfo isp;

    @Override
    @JsonIgnore
    public SubscriberDTO getSubscriber() {
        return super.getSubscriber();
    }

    @Override
    @JsonIgnore
    public void setSubscriber(SubscriberDTO subscriber) {
        super.setSubscriber(subscriber);
    }

    public static class IspInfo {
        private String id;
        private String name;

        public IspInfo() {
        }

        public IspInfo(String id, String name) {
            this.id = id;
            this.name = name;
        }

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }
    }

    public IspInfo getIsp() {
        return isp;
    }

    public void setIsp(IspInfo isp) {
        this.isp = isp;
    }

    public static NetworkOperationsForAppCloudDTO of(NCSNetwork network, List<String> equipmentSerialList, String ispName) {
        NetworkOperationsForAppCloudDTO dto = new NetworkOperationsForAppCloudDTO();
        dto.setId(network.getId());
        dto.setName(network.getName());
        dto.setEquipmentSerialList(equipmentSerialList);
        dto.setIsp(new IspInfo(network.getIspId(), ispName));
        return dto;
    }
}
