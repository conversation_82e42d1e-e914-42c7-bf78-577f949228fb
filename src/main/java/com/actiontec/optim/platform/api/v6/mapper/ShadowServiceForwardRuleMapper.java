package com.actiontec.optim.platform.api.v6.mapper;

import com.actiontec.optim.platform.api.v6.dto.PortForwardRule;
import com.actiontec.optim.platform.api.v6.dto.PortForwardRuleRequest;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.springframework.stereotype.Component;

import java.util.*;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
@Component
public interface ShadowServiceForwardRuleMapper {

    default PortForwardRule convertJsonNodeToPortForwardRule(String id, JsonNode ruleNode) {
        PortForwardRule rule = new PortForwardRule();
        rule.setId(id);
        rule.setName(ruleNode.path("name").asText());
        rule.setEnabled(ruleNode.path("enabled").asBoolean());
        rule.setProtocol(PortForwardRule.Protocol.fromString(ruleNode.path("protocol").asText()));

        // Map source
        PortForwardRuleRequest.Source source = new PortForwardRuleRequest.Source();
        source.setPortStart(ruleNode.path("externalPort").asInt());
        source.setPortEnd(ruleNode.path("externalPortEndRange").asInt());
        rule.setSource(source);

        // Map destination
        PortForwardRuleRequest.Destination destination = new PortForwardRuleRequest.Destination();
        destination.setIpAddress(ruleNode.path("internalClient").asText());
        destination.setPortStart(ruleNode.path("internalPort").asInt());
        destination.setPortEnd(source.getPortEnd() == 0 ? 0 : ruleNode.path("internalPort").asInt() + (source.getPortEnd() - source.getPortStart()));
        rule.setDestination(destination);

        return rule;
    }

    default List<PortForwardRule> convertToPortForwardRuleListFromRuleNodes(JsonNode servicePortForwardingNode) {
        List<PortForwardRule> rules = new ArrayList<>();
        Iterator<Map.Entry<String, JsonNode>> fields = servicePortForwardingNode.fields();
        while (fields.hasNext()) {
            Map.Entry<String, JsonNode> entry = fields.next();
            String id = entry.getKey();
            JsonNode ruleNode = entry.getValue();

            rules.add(convertJsonNodeToPortForwardRule(id, ruleNode));

        }
        return rules;
    }

    default void updateForRuleInForwardingNode(ObjectNode ruleNode, PortForwardRuleRequest request) {
        if (ruleNode.isObject()) {
            Optional.ofNullable(request.getName()).ifPresent(name->ruleNode.put("name",name));
            ruleNode.put("enabled", request.getEnabled());
            Optional.ofNullable(request.getProtocol().name()).ifPresent(p->ruleNode.put("protocol", p));
            Optional.ofNullable(request.getSource()).ifPresent(s->ruleNode.put("externalPort", s.getPortStart()));
            Optional.ofNullable(request.getSource()).ifPresent(s->ruleNode.put("externalPortEndRange", s.getPortEnd()));
            Optional.ofNullable(request.getDestination()).ifPresent(d->ruleNode.put("internalClient", d.getIpAddress()));
            Optional.ofNullable(request.getDestination()).ifPresent(d->ruleNode.put("internalPort", d.getPortStart()));
        }
    }

    default ObjectNode convertPortForwardRuleRequestToRuleNode(PortForwardRuleRequest request) {
        ObjectNode ruleNode = com.fasterxml.jackson.databind.node.JsonNodeFactory.instance.objectNode();

        // Set basic properties
        ruleNode.put("name", request.getName());
        ruleNode.put("enabled", request.getEnabled());
        ruleNode.put("protocol", request.getProtocol().toString());

        // Set source port information
        ruleNode.put("externalPort", request.getSource().getPortStart());
        ruleNode.put("externalPortEndRange", request.getSource().getPortEnd());

        // Set destination information
        ruleNode.put("internalClient", request.getDestination().getIpAddress());
        ruleNode.put("internalPort", request.getDestination().getPortStart());

        return ruleNode;
    }
}
