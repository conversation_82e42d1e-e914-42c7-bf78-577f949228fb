package com.actiontec.optim.platform.api.v6.dto;

import javax.validation.constraints.NotBlank;

public class LegalDocumentUpdateRequest {
    @NotBlank
    private String fileName;

    @NotBlank
    private String md5Checksum;

    public LegalDocumentUpdateRequest(String fileName, String md5Checksum) {
        this.fileName = fileName;
        this.md5Checksum = md5Checksum;
    }

    // Getters and Setters
    public String getFileName() { return fileName; }
    public void setFileName(String fileName) { this.fileName = fileName; }

    public String getMd5Checksum() { return md5Checksum; }
    public void setMd5Checksum(String md5Checksum) { this.md5Checksum = md5Checksum; }
}
