package com.actiontec.optim.platform.api.v6.dto;

import com.actiontec.optim.util.CustomStringUtils;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static com.incs83.app.constants.misc.ApplicationConstants.COMMA;

public class NetworkOperationsDTO {
    private String id;
    private String name;
    private SubscriberDTO subscriber;
    @JsonProperty("equipment")
    private List<String> equipmentSerialList;


    public NetworkOperationsDTO() {
    }

    public NetworkOperationsDTO(Object[] object) {
        this.id = CustomStringUtils.toStringOrNull(object[0]);
        this.name = CustomStringUtils.toStringOrNull(object[1]);
        String subscriberId = CustomStringUtils.toStringOrNull(object[2]);
        String subscriberFirstName = CustomStringUtils.toStringOrNull(object[3]);
        String subscriberLastName = CustomStringUtils.toStringOrNull(object[4]);
        if (CustomStringUtils.isNotEmpty(subscriberId)) {
            this.subscriber = new SubscriberDTO(subscriberId, subscriberFirstName, subscriberLastName);
        } else {
            this.subscriber = new SubscriberDTO();
        }

        String equipmentConcat = CustomStringUtils.toStringOrNull(object[6]);
        if (CustomStringUtils.isNotEmpty(equipmentConcat)) {
            this.equipmentSerialList = Arrays.asList(equipmentConcat.split(COMMA));
        } else {
            this.equipmentSerialList = new ArrayList<>();
        }
    }

    public static class SubscriberDTO {
        String id;
        String firstName;
        String lastName;

        public SubscriberDTO() {
        }

        public SubscriberDTO(String id, String firstName, String lastName) {
            this.id = id;
            this.firstName = firstName;
            this.lastName = lastName;
        }

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getFirstName() {
            return firstName;
        }

        public void setFirstName(String firstName) {
            this.firstName = firstName;
        }

        public String getLastName() {
            return lastName;
        }

        public void setLastName(String lastName) {
            this.lastName = lastName;
        }
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public SubscriberDTO getSubscriber() {
        return subscriber;
    }

    public void setSubscriber(SubscriberDTO subscriber) {
        this.subscriber = subscriber;
    }

    public List<String> getEquipmentSerialList() {
        return equipmentSerialList;
    }

    public void setEquipmentSerialList(List<String> equipmentSerialList) {
        this.equipmentSerialList = equipmentSerialList;
    }
}
