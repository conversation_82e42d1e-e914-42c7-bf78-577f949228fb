package com.actiontec.optim.platform.api.v6.dto;

import org.springframework.util.ObjectUtils;

import java.util.HashMap;
import java.util.List;

public class CpeApiRequest {
    private List<RequestEndpoint> endpoints;

    public List<RequestEndpoint> getEndpoints() {
        return endpoints;
    }

    public void setEndpoints(List<RequestEndpoint> endpoints) {
        this.endpoints = endpoints;
    }

    public static class RequestEndpoint extends CpeApiEndpoint {
        public RequestEndpoint(String path, String method, Object payload) {
            this.setPath(path);
            this.setMethod(method);
            this.setRequestBody(!ObjectUtils.isEmpty(payload)? payload: new HashMap<>());
        }

        private Object requestBody;

        public Object getRequestBody() {
            return requestBody;
        }

        public void setRequestBody(Object payload) {
            this.requestBody = payload;
        }
    }
}
