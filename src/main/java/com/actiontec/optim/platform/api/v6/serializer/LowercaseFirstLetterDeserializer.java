package com.actiontec.optim.platform.api.v6.serializer;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;

/**
 * Custom deserializer that converts the first letter of a string to lowercase
 * for internal processing while accepting capitalized input from JSON
 */
public class LowercaseFirstLetterDeserializer extends JsonDeserializer<String> {
    
    @Override
    public String deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
        String value = p.getValueAsString();
        if (StringUtils.isEmpty(value)) {
            return value;
        } else {
            // Convert first letter to lowercase for internal processing
            return StringUtils.uncapitalize(value);
        }
    }
}
