package com.actiontec.optim.platform.api.v6.dto;

import com.incs83.app.entities.LegalDocument;

import javax.validation.constraints.NotNull;

public class LegalDocumentCreateRequest extends LegalDocumentUpdateRequest{
    @NotNull
    private String name;

    public LegalDocumentCreateRequest(String name, String fileName, String md5Checksum) {
        super(fileName, md5Checksum);
        this.name = name;
    }


    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
