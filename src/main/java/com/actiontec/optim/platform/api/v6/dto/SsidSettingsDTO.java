package com.actiontec.optim.platform.api.v6.dto;

import com.actiontec.optim.platform.api.v6.serializer.CapitalizeFirstLetterSerializer;
import com.actiontec.optim.platform.api.v6.serializer.LowercaseFirstLetterDeserializer;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import java.util.List;

public class SsidSettingsDTO {
    @JsonSerialize(using = CapitalizeFirstLetterSerializer.class)
    @JsonDeserialize(using = LowercaseFirstLetterDeserializer.class)
    private String id;
    
    @JsonSerialize(using = CapitalizeFirstLetterSerializer.class)
    @JsonDeserialize(using = LowercaseFirstLetterDeserializer.class)
    private String type;
    private Boolean separatedSsid;
    private Boolean isolated;
    private Boolean mloEnabled;
    private List<SsidDTO> ssids;

    public SsidSettingsDTO() {}

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Boolean getSeparatedSsid() {
        return separatedSsid;
    }

    public void setSeparatedSsid(Boolean separatedSsid) {
        this.separatedSsid = separatedSsid;
    }

    public Boolean getIsolated() {
        return isolated;
    }

    public void setIsolated(Boolean isolated) {
        this.isolated = isolated;
    }

    public Boolean getMloEnabled() {
        return mloEnabled;
    }

    public void setMloEnabled(Boolean mloEnabled) {
        this.mloEnabled = mloEnabled;
    }

    public List<SsidDTO> getSsids() {
        return ssids;
    }

    public void setSsids(List<SsidDTO> ssids) {
        this.ssids = ssids;
    }
}
