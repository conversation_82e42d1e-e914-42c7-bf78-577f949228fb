package com.actiontec.optim.platform.api.v6.security.entryPoint;

import com.actiontec.optim.platform.api.v6.exception.AppCloudAuthenticationException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.http.MediaType;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.stereotype.Component;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

@Component
public class AppCloudAuthenticationEntryPoint implements AuthenticationEntryPoint {
    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public void commence(HttpServletRequest request,
                         HttpServletResponse response,
                         AuthenticationException authException) throws IOException, ServletException {

        // Only handle AppCloudAuthenticationException
        if (authException instanceof AppCloudAuthenticationException) {
            AppCloudAuthenticationException appCloudAuthenticationException = (AppCloudAuthenticationException) authException;

            Map<String, Object> responseBody = new HashMap<>();
            responseBody.put("errorCode", appCloudAuthenticationException.getErrorCode());
            responseBody.put("message", appCloudAuthenticationException.getMessage());

            response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
            response.setContentType(MediaType.APPLICATION_JSON_VALUE);
            response.setCharacterEncoding("UTF-8");

            objectMapper.writeValue(response.getWriter(), responseBody);
        } else {
            // For non-AppCloudAuthenticationException, just return 401 without body
            response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        }

    }
}

