package com.actiontec.optim.platform.api.v6.enums;

import com.actiontec.optim.util.CustomStringUtils;
import com.incs83.app.constants.misc.RpcConstants;

public enum DeviceAction {
    DISCONNECT("disconnect", RpcConstants.ACTION_KICK_WIFI_STA);

    private final String frontendAction;
    private final String rpcAction;

    DeviceAction(String frontendAction, String rpcAction) {
        this.frontendAction = frontendAction;
        this.rpcAction = rpcAction;
    }

    public String getFrontendAction() {
        return frontendAction;
    }

    public String getRpcAction() {
        return rpcAction;
    }

    public static String toRpcAction(String frontendAction) {
        for (DeviceAction action : values()) {
            if (action.getFrontendAction().equals(frontendAction)) {
                return action.rpcAction;
            }
        }
        return CustomStringUtils.EMPTY;
    }
}