package com.actiontec.optim.platform.api.v6.controller;

import com.actiontec.optim.platform.api.v5.exception.OptimApiException;
import com.actiontec.optim.platform.api.v6.dto.LegalDocumentCreateRequest;
import com.actiontec.optim.platform.api.v6.dto.LegalDocumentDetailsResponse;
import com.actiontec.optim.platform.api.v6.dto.LegalDocumentResponse;
import com.actiontec.optim.platform.api.v6.dto.LegalDocumentUpdateRequest;
import com.actiontec.optim.platform.service.LegalDocumentService;
import com.actiontec.optim.service.AuditService;
import com.actiontec.optim.util.ServletRequestUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;

@RestController
@Api(value = "Legal Documents", description = "APIs for Legal Documents Management", tags = {"Misc"})
@RequestMapping(value = "/actiontec/api/v6/legal-documents")
public class LegalDocumentController {
    private final Logger logger = LogManager.getLogger(this.getClass());

    @Autowired
    private LegalDocumentService legalDocumentService;
    @Autowired
    private AuditService auditService;

    @ApiOperation(value = "List legal documents and versions")
    @RequestMapping(method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public List<LegalDocumentDetailsResponse> getAllLegalDocuments(@ApiParam(value = "Bearer Access Token required for Authentication", required = true)
                                                                       @RequestHeader(name = "X-Authorization") String accessToken,
                                                                   HttpServletRequest httpServletRequest) throws Exception {
        return legalDocumentService.getAllLegalDocuments(ServletRequestUtil.getServerUrl(httpServletRequest));
    }

    @ApiOperation(value = "Create a new legal document")
    @RequestMapping(method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_VALUE,
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<LegalDocumentResponse> createLegalDocument(
            @ApiParam(value = "Legal document creation request", required = true)
            @Valid @RequestBody LegalDocumentCreateRequest request,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        LegalDocumentResponse response = legalDocumentService.createLegalDocument(request);
        return new ResponseEntity<>(response, HttpStatus.CREATED);
    }

    @ApiOperation(value = "Get a specific legal document by ID")
    @RequestMapping(value = "/{documentId}", method = RequestMethod.GET,
            produces = MediaType.APPLICATION_JSON_VALUE)
    public LegalDocumentDetailsResponse getLegalDocument(
            @ApiParam(value = "Unique identifier of the legal document", required = true)
            @PathVariable String documentId,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        return legalDocumentService.getLegalDocumentById(documentId, ServletRequestUtil.getServerUrl(httpServletRequest));
    }

    @ApiOperation(value = "Update a specific legal document")
    @RequestMapping(value = "/{documentId}", method = RequestMethod.PATCH,
            consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Void> updateLegalDocument(
            @ApiParam(value = "Unique identifier of the legal document", required = true)
            @PathVariable String documentId,
            @ApiParam(value = "Legal document update request", required = true)
            @Valid @RequestBody LegalDocumentUpdateRequest request,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {

        boolean ret = legalDocumentService.updateLegalDocument(documentId, request);
        if (ret)
            return new ResponseEntity<>(HttpStatus.OK);

        throw new OptimApiException(HttpStatus.BAD_REQUEST, "Fail to update Legal Document");
    }

    @ApiOperation(value = "Delete a specific legal document")
    @RequestMapping(value = "/{documentId}", method = RequestMethod.DELETE)
    public ResponseEntity<Void> deleteLegalDocument(
            @ApiParam(value = "Unique identifier of the legal document to delete", required = true)
            @PathVariable String documentId,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        boolean ret = legalDocumentService.deleteLegalDocument(documentId);
        if (ret)
            return new ResponseEntity<>(HttpStatus.OK);

        throw new OptimApiException(HttpStatus.BAD_REQUEST, "Fail to delete Legal Document");
    }
}
