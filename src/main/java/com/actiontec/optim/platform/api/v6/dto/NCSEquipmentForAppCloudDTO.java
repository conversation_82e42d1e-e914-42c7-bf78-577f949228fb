package com.actiontec.optim.platform.api.v6.dto;

import java.util.Map;

import com.actiontec.optim.mongodb.dto.ApDetailDto;
import com.actiontec.optim.platform.model.enums.ConnectionStatus;
import com.actiontec.optim.platform.service.NCSEquipmentService;
import com.actiontec.optim.util.CustomStringUtils;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class NCSEquipmentForAppCloudDTO {

    private String id;

    private String fwVersion;

    private String macAddress;

    private String modelName;

    private String name;

    private String iconUrl;

    private String serialNumber;

    private ConnectionStatus status;

    private String type;

    public static NCSEquipmentForAppCloudDTO convertToNCSEquipmentForAppCloudDto(ApDetailDto apDetailDto,
            Map<String, String> ncsEquipmentSerialAndIdMap, String displayName, ConnectionStatus status, String iconUrl)
            throws Exception {

        return NCSEquipmentForAppCloudDTO.builder()
                .id(ncsEquipmentSerialAndIdMap.get(apDetailDto.getSerialNumber()))
                .serialNumber(apDetailDto.getSerialNumber())
                .fwVersion(CustomStringUtils.getNAIfIsBlank(apDetailDto.getFwVersion()))
                .macAddress(apDetailDto.getMacAddress())
                .modelName(CustomStringUtils.getNAIfIsBlank(apDetailDto.getModelName()))
                .type(apDetailDto.getType())
                .name(displayName)
                .iconUrl(iconUrl)
                .status(status)
                .build();
    }

}
