package com.actiontec.optim.platform.api.v6.dto;

/** DTO for device details from hostnameDetail collection */
public class HostnameDetailDto {
    private String friendlyName;
    private Long firstSeenTime;
    private Boolean verified;
    private String serialNumber;
    private String userDefineDeviceType;

    public HostnameDetailDto() {}

    public HostnameDetailDto(String friendlyName, Long firstSeenTime, Boolean verified, String serialNumber, String userDefineDeviceType) {
        this.friendlyName = friendlyName;
        this.firstSeenTime = firstSeenTime;
        this.verified = verified;
        this.serialNumber = serialNumber;
        this.userDefineDeviceType = userDefineDeviceType;
    }

    public String getFriendlyName() {
        return friendlyName;
    }

    public void setFriendlyName(String friendlyName) {
        this.friendlyName = friendlyName;
    }

    public Long getFirstSeenTime() {
        return firstSeenTime;
    }

    public void setFirstSeenTime(Long firstSeenTime) {
        this.firstSeenTime = firstSeenTime;
    }

    public Boolean getVerified() {
        return verified;
    }

    public void setVerified(<PERSON><PERSON><PERSON> verified) {
        this.verified = verified;
    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
    }

    public String getUserDefineDeviceType() {
        return userDefineDeviceType;
    }

    public void setUserDefineDeviceType(String userDefineDeviceType) {
        this.userDefineDeviceType = userDefineDeviceType;
    }
}