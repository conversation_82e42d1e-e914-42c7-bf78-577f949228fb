package com.actiontec.optim.platform.api.v6.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * DTO for CPE Speed Test Result API response
 * Maps the response from CPE API for speed test results
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CpeSpeedTestResultDTO {
    
    private String id;

    private String status;

    private String url;

    // unit: bps
    private Double uploadRate;

    // unit: bps
    private Double downloadRate;

    private Integer latency;

    private Long time;
}