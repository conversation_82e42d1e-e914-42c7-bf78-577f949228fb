package com.actiontec.optim.platform.api.v6.mapper;

import com.actiontec.optim.platform.api.v6.dto.CpeApiResponse;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Component
public class ProxyCpeApiResponseMapper {
    @Autowired
    private ObjectMapper objectMapper;

    public CpeApiResponse parseProxyResult(String rawResponse) throws Exception {
        // proxy cpe api support multiple request with path and payload
        CpeApiResponse cpeApiResponse = new CpeApiResponse();
        Map<String, Object> proxyResultMap = objectMapper.readValue(rawResponse, Map.class);
        long requestTime = proxyResultMap.containsKey("timestamp")
                ? ((Number) proxyResultMap.get("timestamp")).longValue()
                : System.currentTimeMillis();

        List<Map<String, Object>> payloadList = objectMapper.convertValue(proxyResultMap.get("payload"),
                new TypeReference<List<Map<String, Object>>>() {});

        List<CpeApiResponse.ResponseEndpoint> responseEndpointList = new ArrayList<>();
        for (Map<String, Object> endpointMap: payloadList) {
            CpeApiResponse.ResponseEndpoint endpoint = new CpeApiResponse.ResponseEndpoint();
            endpoint.setPath((String) endpointMap.get("path"));
            endpoint.setMethod((String) endpointMap.get("method"));
            endpoint.setHttpStatusCode((Integer) endpointMap.get("code"));

            // msg: cpe api real response
            Object msg = endpointMap.get("msg");
            List<Object> response = new ArrayList<>();
            if (msg == null) {
                response = null;
            } else if (msg instanceof List) {
                response = (List<Object>) msg;
            } else {
                response.add(msg);
            }

            endpoint.setResponse(response);
            responseEndpointList.add(endpoint);
        }

        cpeApiResponse.setRequestTime(requestTime);
        cpeApiResponse.setEndpoints(responseEndpointList);
        return cpeApiResponse;
    }
}
