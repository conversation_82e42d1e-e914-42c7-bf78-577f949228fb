package com.actiontec.optim.platform.api.v6.serializer;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;

/**
 * Custom serializer that capitalizes the first letter of a string for JSON output
 * while keeping the internal value unchanged
 */
public class CapitalizeFirstLetterSerializer extends JsonSerializer<String> {
    
    @Override
    public void serialize(String value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
        if (StringUtils.isEmpty(value)) {
            gen.writeString(value);
        } else {
            // Capitalize first letter for JSON output
            String capitalizedValue = StringUtils.capitalize(value);
            gen.writeString(capitalizedValue);
        }
    }
}
