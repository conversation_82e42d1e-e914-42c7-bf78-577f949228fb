package com.actiontec.optim.platform.api.v6.dto;

import java.util.List;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@Data
@AllArgsConstructor
@Builder
@EqualsAndHashCode(callSuper = true)
public class EquipmentModelIspConfigRequest extends PaginationRequest {

    public interface Create {
    }

    public interface Patch {
    }

    public interface BatchPatch {
    }

    public interface Delete {
    }
    
    @NotEmpty(groups = { BatchPatch.class }, message = "IDs must not be empty")
    private String id;

    @NotBlank(groups = { Create.class, Patch.class, BatchPatch.class }, message = "ISP ID must not be blank")
    private String ispId;

    @NotBlank(groups = { Create.class, Patch.class, BatchPatch.class }, message = "Model ID must not be blank")
    private String modelId;

    @NotBlank(groups = { Create.class, Patch.class, BatchPatch.class}, message = "Firmware Version must not be blank")
    private String requiredFirmwareId;

    @NotEmpty(groups = { Delete.class }, message = "IDs must not be empty")
    private List<String> ids;

}
