package com.actiontec.optim.platform.api.v6.dto;

import com.actiontec.optim.platform.api.v6.serializer.CapitalizeFirstLetterSerializer;
import com.actiontec.optim.platform.api.v6.serializer.LowercaseFirstLetterDeserializer;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import java.util.List;

public class SsidDTO {
    private String id;
    private Boolean enabled;
    private String name;
    private String passphrase;
    private String radio;
    private String securityMode;
    
    @JsonSerialize(using = CapitalizeFirstLetterSerializer.class)
    @JsonDeserialize(using = LowercaseFirstLetterDeserializer.class)
    private String type;

    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    private List<String> securityModesSupported;

    public SsidDTO() {}

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPassphrase() {
        return passphrase;
    }

    public void setPassphrase(String passphrase) {
        this.passphrase = passphrase;
    }

    public String getRadio() {
        return radio;
    }

    public void setRadio(String radio) {
        this.radio = radio;
    }

    public String getSecurityMode() {
        return securityMode;
    }

    public void setSecurityMode(String securityMode) {
        this.securityMode = securityMode;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public List<String> getSecurityModesSupported() {
        return securityModesSupported;
    }

    public void setSecurityModesSupported(List<String> securityModesSupported) {
        this.securityModesSupported = securityModesSupported;
    }
}
