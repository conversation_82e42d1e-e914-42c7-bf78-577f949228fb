package com.actiontec.optim.platform.api.v6.dto;

import java.util.Date;

import org.springframework.format.annotation.DateTimeFormat;

import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class CustomerProfileDTO {

    private String id;

    private String ispId;

    private String modelId;

    private String requiredFirmwareId;

    @DateTimeFormat(pattern = "yyyy-MM-dd' 'HH:mm:ss")
    private Long lastChangedTime;

}
