package com.actiontec.optim.platform.api.v6.dto;

import javax.validation.constraints.NotEmpty;
import java.util.List;
import java.util.UUID;

public class PortForwardRulesDelRequest {
    
    @NotEmpty(message = "Rule IDs are required")
    private List<String> ids;
    
    public List<String> getIds() {
        return ids;
    }
    
    public void setIds(List<String> ids) {
        this.ids = ids;
    }
}
