package com.actiontec.optim.platform.api.v6.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.*;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PortForwardRuleRequest {
    
    @NotBlank(message = "Name is required")
    private String name;
    
    @NotNull(message = "Protocol is required")
    private Protocol protocol;
    
    @NotNull(message = "Source is required")
    private Source source;
    
    @NotNull(message = "Destination is required")
    private Destination destination;
    
    private Boolean enabled;

    // Embedded Protocol Enum within PortForwardRule class
    public enum Protocol {
        TCP,
        UDP,
        TCP_UDP;

        public static Protocol fromString(String value) {
            if (value == null) return null;

            switch (value.toUpperCase()) {
                case "TCP": return TCP;
                case "UDP": return UDP;
                case "TCP_UDP": return TCP_UDP;
                default: throw new IllegalArgumentException("Invalid protocol value: " + value);
            }
        }
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Source {
        @Min(value = 0, message = "Port must be between 0 and 65535")
        @Max(value = 65535, message = "Port must be between 0 and 65535")
        private int portStart;
        
        @Min(value = 0, message = "Port must be between 0 and 65535")
        @Max(value = 65535, message = "Port must be between 0 and 65535")
        private int portEnd;

        // Custom validation
        @JsonIgnore
        public boolean isValid() {
            return portEnd == 0 ||  portEnd >= portStart;
        }
    }
    
    @Data
    public static class Destination {
        @Pattern(regexp = "^([1-9]|([1-9][0-9])|(1[0-9][0-9])|(2[0-4][0-9])|(25[0-5]))(\\.([0-9]|([1-9][0-9])|(1[0-9][0-9])|(2[0-4][0-9])|(25[0-5]))){3}$",
                message = "Invalid IPv4 address format")
        @NotBlank(message = "IP address is required")
        private String ipAddress;
        
        @Min(value = 0, message = "Port must be between 0 and 65535")
        @Max(value = 65535, message = "Port must be between 0 and 65535")
        private int portStart;
        
        @Min(value = 0, message = "Port must be between 0 and 65535")
        @Max(value = 65535, message = "Port must be between 0 and 65535")
        private int portEnd;
        
        // Custom validation
        @JsonIgnore
        public boolean isValid() {
            return portEnd == 0 ||  portEnd >= portStart;
        }
    }
    
    // Validate the entire request
    @JsonIgnore
    public boolean isValid() {
        return source != null && source.isValid() && 
               destination != null && destination.isValid() &&
                ((source.portEnd != 0 && (Math.abs(source.portEnd - source.portStart) == Math.abs(destination.portEnd - destination.portStart))) ||
                        (source.portEnd == 0 && destination.portEnd == 0));
    }
}
