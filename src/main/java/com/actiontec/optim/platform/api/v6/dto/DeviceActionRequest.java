package com.actiontec.optim.platform.api.v6.dto;

import javax.validation.constraints.NotNull;

public class DeviceActionRequest {
    @NotNull
    private String action;

    public DeviceActionRequest() {
    }

    public DeviceActionRequest(String action) {
        this.action = action;
    }

    public String getAction() {
        return action;
    }

    public void setAction(String action) {
        this.action = action;
    }
}
