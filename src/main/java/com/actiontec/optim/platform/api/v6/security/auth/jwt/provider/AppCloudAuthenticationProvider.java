package com.actiontec.optim.platform.api.v6.security.auth.jwt.provider;

import com.actiontec.optim.platform.api.v6.enums.AppCloudValidationErrorCode;
import com.actiontec.optim.platform.api.v6.exception.AppCloudAuthenticationException;
import com.actiontec.optim.platform.api.v6.security.auth.jwt.validator.AppCloudJwtTokenValidator;
import com.actiontec.optim.platform.api.v6.security.tokenFactory.AppCloudJwtAuthenticationToken;
import com.actiontec.optim.platform.service.AppCloudService;
import com.actiontec.optim.platform.service.NCSNetworkService;
import com.actiontec.optim.util.AppCloudJwtTokenUtils;
import com.actiontec.optim.util.CustomStringUtils;
import com.incs83.app.constants.misc.ApplicationConstants;
import com.incs83.app.entities.AppCloudConfig;
import com.incs83.app.entities.NCSNetwork;
import com.incs83.config.JwtConfig;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.authentication.AuthenticationServiceException;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.InsufficientAuthenticationException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import javax.xml.bind.ValidationException;

@Component
public class AppCloudAuthenticationProvider implements AuthenticationProvider {
    private final Logger logger = LogManager.getLogger(AppCloudAuthenticationProvider.class);

    @Autowired
    private NCSNetworkService ncsNetworkService;

    @Autowired
    private AppCloudService appCloudService;

    @Autowired
    private AppCloudJwtTokenValidator appCloudJwtTokenValidator;

    @Autowired
    private JwtConfig jwtConfig;

    @Override
    public Authentication authenticate(Authentication authentication) throws AuthenticationException {
        AppCloudJwtAuthenticationToken authToken = (AppCloudJwtAuthenticationToken) authentication;
        String exchangeToken = (String) authToken.getCredentials();
        String networkId = authToken.getNetworkId();
        String networkTokenId = authToken.getNetworkTokenId();

        logger.info("Validating exchange token: {}", exchangeToken);
        logger.info("networkId: {}", networkId);
        logger.info("networkTokenId: {}", networkTokenId);

        if (!appCloudJwtTokenValidator.verifyExchangeToken(exchangeToken, jwtConfig.getTokenSigningKey())) {
            throw new AppCloudAuthenticationException(AppCloudValidationErrorCode.INVALID_EXCHANGE_TOKEN);
        }

        logger.info("Exchange token verified. Proceeding to validate kid and App Cloud configuration.");

        String kid;
        AppCloudConfig appCloudConfig;
        try {
            kid = AppCloudJwtTokenUtils.extractKid(exchangeToken);
            appCloudConfig = appCloudService.getAppCloudConfigByKid(kid);
        } catch (Exception e) {
            logger.error(AppCloudValidationErrorCode.MISSING_KID.getMessage() + ": " + exchangeToken);
            throw new AppCloudAuthenticationException(AppCloudValidationErrorCode.MISSING_KID, exchangeToken);
        }

        if (ObjectUtils.isEmpty(appCloudConfig)) {
            logger.error(AppCloudValidationErrorCode.APP_CLOUD_CONFIG_NOT_FOUND + ": " + kid);
            throw new AppCloudAuthenticationException(AppCloudValidationErrorCode.APP_CLOUD_CONFIG_NOT_FOUND, kid);
        }

        // validate network token id
        if (CustomStringUtils.isNotEmpty(networkId)) {
            NCSNetwork network;
            try {
                network = ncsNetworkService.getNetworkById(networkId);
            } catch (Exception e) {
                logger.error(AppCloudValidationErrorCode.NETWORK_NOT_FOUND.getMessage() + ": " + networkId);
                throw new AppCloudAuthenticationException(AppCloudValidationErrorCode.NETWORK_NOT_FOUND, networkId);
            }

            if (ObjectUtils.isEmpty(network)) {
                logger.error(AppCloudValidationErrorCode.NETWORK_NOT_FOUND.getMessage() + ": " + networkId);
                throw new AppCloudAuthenticationException(AppCloudValidationErrorCode.NETWORK_NOT_FOUND, networkId);
            }

            if (!CustomStringUtils.equals(networkTokenId, network.getNetworkTokenId())) {
                logger.error("networkTokenId not match, request networkTokenId: {}, db networkTokenId: {}",
                        networkTokenId, network.getNetworkTokenId());
                throw new AppCloudAuthenticationException(AppCloudValidationErrorCode.NETWORK_TOKEN_MISMATCH);
            }
        }

        return new AppCloudJwtAuthenticationToken(exchangeToken, networkId, networkTokenId, appCloudConfig);
    }

    @Override
    public boolean supports(Class<?> authentication) {
        return AppCloudJwtAuthenticationToken.class.isAssignableFrom(authentication);
    }
}
