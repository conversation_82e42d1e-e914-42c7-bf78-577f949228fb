package com.actiontec.optim.platform.api.v6.dto;

public class LegalDocumentDetailsResponse extends LegalDocumentResponse {
    private String name;
    private Integer version;
    private String url;
    private String fileName;
    private String md5Checksum;
    private Long lastModified;
    public LegalDocumentDetailsResponse(String id, String fileEntry) {
        super(id, fileEntry);
    }

    public LegalDocumentDetailsResponse(String id, String fileEntry, String name, Integer version, String url, String fileName, String md5Checksum, Long lastModified) {
        super(id, fileEntry);
        this.name = name;
        this.version = version;
        this.url = url;
        this.fileName = fileName;
        this.md5Checksum = md5Checksum;
        this.lastModified = lastModified;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getMd5Checksum() {
        return md5Checksum;
    }

    public void setMd5Checksum(String md5Checksum) {
        this.md5Checksum = md5Checksum;
    }

    public Long getLastModified() {
        return lastModified;
    }

    public void setLastModified(Long lastModified) {
        this.lastModified = lastModified;
    }
}
