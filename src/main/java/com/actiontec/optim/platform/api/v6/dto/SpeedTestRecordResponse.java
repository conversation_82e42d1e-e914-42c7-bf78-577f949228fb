package com.actiontec.optim.platform.api.v6.dto;

import com.actiontec.optim.mongodb.dto.WanSpeedTestDto;
import com.actiontec.optim.platform.api.v6.enums.NcsCpeSpeedTestStatus;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.ObjectUtils;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SpeedTestRecordResponse {
    private String id;
    private double downloadRate;
    private String equipmentId;
    private int latency;
    private NcsCpeSpeedTestStatus status;
    private long time;
    private double uploadRate;
    private String url;

    /**
     * Create SpeedTestRecordResponse from WanSpeedTestDto
     */
    public static SpeedTestRecordResponse fromWanSpeedTestDto(WanSpeedTestDto dto) {
        if (ObjectUtils.isEmpty(dto)) {
            return new SpeedTestRecordResponse();
        }

        return new SpeedTestRecordResponse(
                dto.getRecordId(),
                dto.getDownloadSpeed(),
                StringUtils.EMPTY,
                dto.getLatency(),
                dto.getStatus(),
                dto.getDate().getTime(),
                dto.getUploadSpeed(),
                dto.getTestServer());
    }
}