package com.actiontec.optim.platform.api.v6.dto;

public class NetworkClaimResponse {
    private String networkId;
    private Boolean isConfigured;
    private String networkTokenId;
    private IspInfo isp;

    public NetworkClaimResponse() {
    }

    public NetworkClaimResponse(String networkId, Boolean isConfigured, String networkTokenId, IspInfo isp) {
        this.networkId = networkId;
        this.isConfigured = isConfigured;
        this.networkTokenId = networkTokenId;
        this.isp = isp;
    }

    public String getNetworkId() {
        return networkId;
    }

    public void setNetworkId(String networkId) {
        this.networkId = networkId;
    }

    public Boolean getIsConfigured() {
        return isConfigured;
    }

    public void setIsConfigured(Boolean isConfigured) {
        this.isConfigured = isConfigured;
    }

    public String getNetworkTokenId() {
        return networkTokenId;
    }

    public void setNetworkTokenId(String networkTokenId) {
        this.networkTokenId = networkTokenId;
    }

    public IspInfo getIsp() {
        return isp;
    }

    public void setIsp(IspInfo isp) {
        this.isp = isp;
    }

    public static class IspInfo {
        private String logo;
        private String message;

        public IspInfo() {
        }

        public IspInfo(String logo, String message) {
            this.logo = logo;
            this.message = message;
        }

        public String getLogo() {
            return logo;
        }

        public void setLogo(String logo) {
            this.logo = logo;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }
    }
}
