package com.actiontec.optim.platform.api.v6.security.auth.jwt.filter;

import com.actiontec.optim.platform.api.v6.enums.AppCloudValidationErrorCode;
import com.actiontec.optim.platform.api.v6.exception.AppCloudAuthenticationException;
import com.actiontec.optim.platform.api.v6.security.auth.jwt.validator.AppCloudJwtTokenValidator;
import com.actiontec.optim.platform.api.v6.security.tokenFactory.AppCloudJwtAuthenticationToken;

import com.actiontec.optim.platform.service.AppCloudService;
import com.actiontec.optim.util.AppCloudJwtTokenUtils;
import com.actiontec.optim.util.CustomStringUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.incs83.app.constants.misc.ApplicationConstants;
import com.incs83.config.JwtConfig;
import com.incs83.security.auth.jwt.extractor.TokenExtractor;
import com.incs83.security.config.WebSecurityConfig;
import io.netty.util.HashedWheelTimer;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

@Component
public class AppCloudJwtTokenFilter extends OncePerRequestFilter {
    private final Logger logger = LogManager.getLogger(AppCloudJwtTokenFilter.class);

    private final ObjectProvider<AuthenticationManager> authenticationManagerProvider;
    private final AppCloudJwtTokenValidator appCloudJwtTokenValidator;
    private final JwtConfig jwtConfig;
    private final TokenExtractor tokenExtractor;
    private final AppCloudService appCloudService;
    private final AntPathMatcher pathMatcher = new AntPathMatcher();

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    public AppCloudJwtTokenFilter(
            ObjectProvider<AuthenticationManager> authenticationManagerProvider,
            AppCloudJwtTokenValidator appCloudJwtTokenValidator, JwtConfig jwtConfig, TokenExtractor tokenExtractor,
            AppCloudService appCloudService) {
        this.authenticationManagerProvider = authenticationManagerProvider;
        this.appCloudJwtTokenValidator = appCloudJwtTokenValidator;
        this.jwtConfig = jwtConfig;
        this.tokenExtractor = tokenExtractor;
        this.appCloudService = appCloudService;
    }

    @Override
    protected boolean shouldNotFilter(HttpServletRequest request) {
        String requestUri = request.getRequestURI();
        // Only filter App Cloud API paths
        return !pathMatcher.match("/**/api/v6/networks/**", requestUri);
    }

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
            throws ServletException, IOException {
        logger.info("AppCloudJwtTokenFilter triggered: {}", request.getRequestURI());
        String networkApiPattern = "/**/api/v6/networks/{networkId}/**";
        String requestUri = request.getRequestURI();
        String networkId = null;
        String networkTokenId = null;

        // prevent duplicate authentication
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication instanceof AppCloudJwtAuthenticationToken && authentication.isAuthenticated()) {
            filterChain.doFilter(request, response);
            return;
        }

        try {
            // Extract the JWT token payload from the header using tokenExtractor
            String rawHeader = request.getHeader(WebSecurityConfig.JWT_TOKEN_HEADER_PARAM);
            String token = tokenExtractor.extract(rawHeader);
            logger.info("AppCloudJwtTokenFilter: token extracted: {}", token);

            if (CustomStringUtils.isEmpty(token)) {
                throw new AppCloudAuthenticationException(AppCloudValidationErrorCode.MISSING_TOKEN);
            }

            // only app cloud admin need to verify network_token_id
            try {
                String roleId = AppCloudJwtTokenUtils.extractRoleId(token, jwtConfig.getTokenSigningKey());
                if (!CustomStringUtils.equals(roleId, ApplicationConstants.APP_CLOUD_ADMIN_ROLE_ID)) {
                    filterChain.doFilter(request, response);
                    return;
                }
            } catch (Exception e) {
                logger.error("Error extracting role ID from token: {}", e.getMessage(), e);
                throw new AppCloudAuthenticationException(AppCloudValidationErrorCode.MISSING_ROLEID);
            }

            // extract networkId from pathUri
            if (pathMatcher.match(networkApiPattern, requestUri)) {
                Map<String, String> variables = pathMatcher.extractUriTemplateVariables(networkApiPattern, requestUri);
                String candidateId = variables.get("networkId");
                // check uuid format
                if (CustomStringUtils.isNotEmpty(candidateId) && candidateId.matches("^[0-9a-fA-F]{32}$")) {
                    networkId = candidateId;
                }
            }

            if (CustomStringUtils.isNotEmpty(networkId)) {
                networkTokenId = request.getHeader("networkTokenId");

                if (CustomStringUtils.isEmpty(networkTokenId)) {
                    logger.error("NetworkTokenId cannot be empty.");
                    throw new AppCloudAuthenticationException(AppCloudValidationErrorCode.MISSING_NETWORK_TOKEN_ID);
                }
            }

            // set authentication success
            Authentication authenResult = authenticationManagerProvider.getObject()
                    .authenticate(new AppCloudJwtAuthenticationToken(token, networkId, networkTokenId));
            SecurityContextHolder.getContext().setAuthentication(authenResult);
        } catch (AppCloudAuthenticationException  authenticationException) {
            logger.error("Authentication failed: [{}] {}", authenticationException.getErrorCode(), authenticationException.getMessage());
            sendUnauthorizedResponse(response, authenticationException);
        } catch (Exception ex) {
            logger.error("Unexpected error in authentication filter: {}", ex.getMessage(), ex);
            sendUnauthorizedResponse(response, new AppCloudAuthenticationException(AppCloudValidationErrorCode.UNEXPECTED_ERROR));
        }

        filterChain.doFilter(request, response);
    }

    private void sendUnauthorizedResponse(HttpServletResponse response, AppCloudAuthenticationException authenticationException) throws IOException {
        Map<String, Object> responseBody = new HashMap<>();
        responseBody.put("errorCode", authenticationException.getErrorCode());
        responseBody.put("message", authenticationException.getMessage());
        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        response.setContentType(MediaType.APPLICATION_JSON_UTF8_VALUE);
        objectMapper.writeValue(response.getWriter(), responseBody);
    }
}
