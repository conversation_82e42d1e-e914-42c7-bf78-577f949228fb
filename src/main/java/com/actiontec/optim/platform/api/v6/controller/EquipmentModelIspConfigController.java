package com.actiontec.optim.platform.api.v6.controller;

import java.util.List;
import java.util.Set;

import javax.servlet.http.HttpServletRequest;
import javax.validation.ConstraintViolation;
import javax.validation.Valid;
import javax.validation.Validator;
import javax.validation.constraints.Size;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.actiontec.optim.platform.api.v6.dto.EquipmentModelIspConfigDTO;
import com.actiontec.optim.platform.api.v6.dto.EquipmentModelIspConfigRequest;
import com.actiontec.optim.platform.api.v6.dto.EquipmentModelIspConfigRequest.BatchPatch;
import com.actiontec.optim.platform.api.v6.dto.EquipmentModelIspConfigRequest.Create;
import com.actiontec.optim.platform.api.v6.dto.EquipmentModelIspConfigRequest.Delete;
import com.actiontec.optim.platform.api.v6.dto.EquipmentModelIspConfigRequest.Patch;
import com.actiontec.optim.platform.api.v6.dto.IdDTO;
import com.actiontec.optim.platform.api.v6.dto.IdListDTO;
import com.actiontec.optim.platform.api.v6.dto.PaginationResponse;
import com.actiontec.optim.platform.constant.ValidationConstants;
import com.actiontec.optim.platform.service.EquipmentModelIspConfigService;
import com.actiontec.optim.service.AuditService;
import com.actiontec.optim.util.ValidationUtils;
import com.incs83.app.constants.misc.ApplicationConstants;
import com.incs83.app.enums.ErrorResponseEnum;
import com.incs83.util.CommonUtils;

@RestController
@Validated
@RequestMapping(value = "/actiontec/api/v6/custom/profiles")
public class EquipmentModelIspConfigController {

    private final String AUDIT_LOG_CATEGORY = "Customer Profile";

    @Autowired
    private Validator validator;

    @Autowired
    private EquipmentModelIspConfigService equipmentModelIspConfigService;

    @Autowired
    private AuditService auditService;

    @GetMapping
    public ResponseEntity<PaginationResponse<EquipmentModelIspConfigDTO>> getAllEquipmentModelIspConfigs(
            @ModelAttribute @Valid EquipmentModelIspConfigRequest request,
            HttpServletRequest httpServletRequest) throws Exception {

        // only allow system or group administrators
        ValidationUtils.validateIsSysAdminOrGroupAdmin();

        PaginationResponse<EquipmentModelIspConfigDTO> equipmentModelIspConfigResponse = equipmentModelIspConfigService
                .getAllEquipmentModelIspConfigs(request);

        auditService.createAuditLog(CommonUtils.getUserIdOfLoggedInUser(), "",
                AUDIT_LOG_CATEGORY, request, HttpStatus.OK.toString(),
                equipmentModelIspConfigResponse, httpServletRequest);

        return ResponseEntity.ok(equipmentModelIspConfigResponse);
    }

    @GetMapping("/{profileId}")
    public ResponseEntity<EquipmentModelIspConfigDTO> getEquipmentModelIspConfig(
            @PathVariable String profileId, HttpServletRequest httpServletRequest) throws Exception {

        // only allow system or group administrators
        ValidationUtils.validateIsSysAdminOrGroupAdmin();

        EquipmentModelIspConfigDTO equipmentModelIspConfig = equipmentModelIspConfigService
                .getEquipmentModelIspConfig(profileId);

        auditService.createAuditLog(CommonUtils.getUserIdOfLoggedInUser(), "",
                AUDIT_LOG_CATEGORY, null, HttpStatus.OK.toString(),
                equipmentModelIspConfig, httpServletRequest);

        return ResponseEntity.ok(equipmentModelIspConfig);
    }

    @PostMapping
    public ResponseEntity<IdListDTO> createEquipmentModelIspConfigs(
            @RequestBody @Size(min = 1, max = ValidationConstants.BATCH_REQUEST_SIZE, message = ValidationConstants.REQUEST_SIZE_EXCEEDS_LIMIT) List<EquipmentModelIspConfigRequest> requests,
            HttpServletRequest httpServletRequest) throws Exception {

        // validate the requests
        for (EquipmentModelIspConfigRequest req : requests) {
            ValidationUtils.validateRequest(req, Create.class, validator);
        }

        // only allow system
        ValidationUtils.validateIsSystemAdmin();

        IdListDTO rsp = equipmentModelIspConfigService.createEquipmentModelIspConfigs(requests);

        auditService.createAuditLog(CommonUtils.getUserIdOfLoggedInUser(), "",
                AUDIT_LOG_CATEGORY, requests, HttpStatus.CREATED.toString(),
                rsp, httpServletRequest);

        return ResponseEntity.status(HttpStatus.CREATED)
                .body(rsp);
    }

    @PatchMapping()
    public ResponseEntity<IdListDTO> updateEquipmentModelIspConfigs(
            @RequestBody @Size(min = 1, max = ValidationConstants.BATCH_REQUEST_SIZE, message = ValidationConstants.REQUEST_SIZE_EXCEEDS_LIMIT) List<EquipmentModelIspConfigRequest> requests,
            HttpServletRequest httpServletRequest) throws Exception {

        // validate the requests
        for (EquipmentModelIspConfigRequest req : requests) {
            ValidationUtils.validateRequest(req, BatchPatch.class, validator);
        }

        // only allow system
        ValidationUtils.validateIsSystemAdmin();

        IdListDTO rsp = equipmentModelIspConfigService.updateEquipmentModelIspConfigs(requests);

        auditService.createAuditLog(CommonUtils.getUserIdOfLoggedInUser(), "",
                AUDIT_LOG_CATEGORY, null, HttpStatus.OK.toString(),
                rsp, httpServletRequest);

        return ResponseEntity.status(HttpStatus.OK)
                .body(rsp);
    }

    @PatchMapping("/{profileId}")
    public ResponseEntity<IdDTO> updateEquipmentModelIspConfig(
            @PathVariable String profileId,
            @RequestBody @Validated(Patch.class) EquipmentModelIspConfigRequest request,
            HttpServletRequest httpServletRequest) throws Exception {

        // only allow system
        ValidationUtils.validateIsSystemAdmin();

        IdDTO rsp = equipmentModelIspConfigService.updateEquipmentModelIspConfig(profileId, request);

        auditService.createAuditLog(CommonUtils.getUserIdOfLoggedInUser(), "",
                AUDIT_LOG_CATEGORY, null, HttpStatus.OK.toString(),
                rsp, httpServletRequest);

        return ResponseEntity.status(HttpStatus.OK)
                .body(rsp);
    }

    @DeleteMapping()
    public ResponseEntity<IdListDTO> deleteEquipmentModelIspConfigs(
            @RequestBody @Validated(Delete.class) EquipmentModelIspConfigRequest request,
            HttpServletRequest httpServletRequest) throws Exception {

        // only allow system
        ValidationUtils.validateIsSystemAdmin();

        IdListDTO rsp = equipmentModelIspConfigService.deleteEquipmentModelIspConfigs(request.getIds());

        auditService.createAuditLog(CommonUtils.getUserIdOfLoggedInUser(), "",
                AUDIT_LOG_CATEGORY, null, HttpStatus.NO_CONTENT.toString(),
                rsp, httpServletRequest);

        return ResponseEntity.status(HttpStatus.NO_CONTENT)
                .body(rsp);
    }

    @DeleteMapping("/{profileId}")
    public ResponseEntity<IdDTO> deleteEquipmentModelIspConfig(@PathVariable String profileId,
            HttpServletRequest httpServletRequest) throws Exception {

        // only allow system
        ValidationUtils.validateIsSystemAdmin();

        IdDTO rsp = equipmentModelIspConfigService.deleteEquipmentModelIspConfig(profileId);

        auditService.createAuditLog(CommonUtils.getUserIdOfLoggedInUser(), "",
                AUDIT_LOG_CATEGORY, null, HttpStatus.NO_CONTENT.toString(),
                rsp, httpServletRequest);

        return ResponseEntity.status(HttpStatus.NO_CONTENT)
                .body(rsp);
    }
}
