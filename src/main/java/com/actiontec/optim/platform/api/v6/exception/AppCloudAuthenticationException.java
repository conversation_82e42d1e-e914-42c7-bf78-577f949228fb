package com.actiontec.optim.platform.api.v6.exception;

import com.actiontec.optim.platform.api.v6.enums.AppCloudValidationErrorCode;
import org.springframework.security.core.AuthenticationException;

public class AppCloudAuthenticationException extends AuthenticationException {
    private final int errorCode;
    private final String message;

    // 普通 constructor
    public AppCloudAuthenticationException(AppCloudValidationErrorCode error) {
        super(error.getMessage());
        this.errorCode = error.getCode();
        this.message = error.getMessage();
    }

    // 支援動態參數插入
    public AppCloudAuthenticationException(AppCloudValidationErrorCode error, String dynamicValue) {
        super(String.format(error.getMessage(), dynamicValue));
        this.errorCode = error.getCode();
        this.message = String.format(error.getMessage(), dynamicValue);
    }

    public int getErrorCode() {
        return errorCode;
    }

    @Override
    public String getMessage() {
        return message;
    }
}
