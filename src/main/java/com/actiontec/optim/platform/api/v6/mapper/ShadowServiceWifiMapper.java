package com.actiontec.optim.platform.api.v6.mapper;

import com.actiontec.optim.platform.api.v6.dto.SsidDTO;
import com.actiontec.optim.platform.api.v6.dto.SsidSettingsDTO;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.ReportingPolicy;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * a mapper for optim shadow setting, specifically for serviceWiFi section
 *
 */

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
@Component
public interface ShadowServiceWifiMapper {

    /**
     * Map serviceWiFi JsonNode to list of SsidSettingsDto with band-specific security modes
     * This method processes each profile (primary, guest, etc.) from the serviceWiFi section
     */
    default List<SsidSettingsDTO> mapServiceWifiNodeToSsidSettingsList(JsonNode serviceWiFiNode, Map<String, List<String>> bandSecurityModesMap) {
        List<SsidSettingsDTO> ssidSettingsList = new ArrayList<>();

        // Process each profile (primary, guest, etc.)
        Iterator<Map.Entry<String, JsonNode>> profileIterator = serviceWiFiNode.fields();
        while (profileIterator.hasNext()) {
            Map.Entry<String, JsonNode> profileEntry = profileIterator.next();
            String profileKey = profileEntry.getKey();
            JsonNode profileNode = profileEntry.getValue();

            // Use mapper to convert profile to SsidSettingsDto
            SsidSettingsDTO ssidSettings = mapProfileToSsidSettings(profileKey, profileNode);

            // Process radios for this profile with band-specific security modes
            List<SsidDTO> ssidList = mapRadiosToSsidList(profileNode.path("radios"), ssidSettings.getType(), bandSecurityModesMap);

            ssidSettings.setSsids(ssidList);
            ssidSettingsList.add(ssidSettings);
        }

        return ssidSettingsList;
    }

    /**
     * Map radios JsonNode to list of SsidDto with band-specific security modes
     */
    default List<SsidDTO> mapRadiosToSsidList(JsonNode radiosNode, String profileType, Map<String, List<String>> bandSecurityModesMap) {
        List<SsidDTO> ssidList = new ArrayList<>();

        Iterator<Map.Entry<String, JsonNode>> radioIterator = radiosNode.fields();
        while (radioIterator.hasNext()) {
            Map.Entry<String, JsonNode> radioEntry = radioIterator.next();
            String radioKey = radioEntry.getKey();
            JsonNode radioNode = radioEntry.getValue();

            // Map radio band from radioKey to capability band format
            String capabilityBand = "";
            switch (radioKey) {
                case "2.4G":
                    capabilityBand = "2g";
                    break;
                case "5G":
                    capabilityBand = "5gl";  // Using 5gl as primary 5G band
                    break;
                case "6G":
                    capabilityBand = "6g";
                    break;
                default:
                    capabilityBand = radioKey;
                    break;
            }

            // Get security modes for this specific band
            List<String> securityModesForBand = bandSecurityModesMap.getOrDefault(capabilityBand, Collections.emptyList());

            // Use mapper to convert radio to SsidDto
            SsidDTO ssid = mapRadioToSsid(radioKey, radioNode, profileType, securityModesForBand);
            ssidList.add(ssid);
        }

        return ssidList;
    }

    /**
     * Map JsonNode profile to SsidSettingsDto
     */
    @Mapping(target = "id", source = "profileKey")
    @Mapping(target = "type", source = "profileKey")
    @Mapping(target = "separatedSsid", source = "profileNode", qualifiedByName = "mapSeparatedSsid")
    @Mapping(target = "isolated", source = "profileNode", qualifiedByName = "mapIsolated")
    @Mapping(target = "mloEnabled", source = "profileNode", qualifiedByName = "mapMloEnabled")
    @Mapping(target = "ssids", ignore = true) // Will be set separately
    SsidSettingsDTO mapProfileToSsidSettings(String profileKey, JsonNode profileNode);

    /**
     * Map JsonNode radio to SsidDto
     */
    @Mapping(target = "id", source = "radioKey")
    @Mapping(target = "radio", source = "radioKey")
    @Mapping(target = "enabled", source = "radioNode", qualifiedByName = "mapEnabled")
    @Mapping(target = "name", source = "radioNode", qualifiedByName = "mapName")
    @Mapping(target = "passphrase", source = "radioNode", qualifiedByName = "mapPassphrase")
    @Mapping(target = "securityMode", source = "radioNode", qualifiedByName = "mapSecurityMode")
    @Mapping(target = "type", source = "profileType")
    @Mapping(target = "securityModesSupported", source = "securityModesSupported")
    SsidDTO mapRadioToSsid(String radioKey, JsonNode radioNode, String profileType, List<String> securityModesSupported);

    // Named mapping methods for profile level fields
    @Named("mapSeparatedSsid")
    default Boolean mapSeparatedSsid(JsonNode profileNode) {
        return profileNode.path("separatedSsid").asBoolean(false);
    }

    @Named("mapIsolated")
    default Boolean mapIsolated(JsonNode profileNode) {
        return profileNode.path("isolated").asBoolean(false);
    }

    @Named("mapMloEnabled")
    default Boolean mapMloEnabled(JsonNode profileNode) {
        return profileNode.path("mlo").path("enabled").asBoolean(false);
    }

    // Named mapping methods for SSID level fields
    @Named("mapEnabled")
    default Boolean mapEnabled(JsonNode radioNode) {
        return radioNode.path("enabled").asBoolean(false);
    }

    @Named("mapName")
    default String mapName(JsonNode radioNode) {
        return radioNode.path("name").asText("");
    }

    @Named("mapPassphrase")
    default String mapPassphrase(JsonNode radioNode) {
        JsonNode passphraseNode = radioNode.path("passphrase");
        if (passphraseNode.isObject()) {
            return passphraseNode.path("value").asText("");
        } else {
            return passphraseNode.asText("");
        }
    }

    @Named("mapSecurityMode")
    default String mapSecurityMode(JsonNode radioNode) {
        return radioNode.path("securityMode").asText("");
    }

    // Update methods for modifying shadow data

    /**
     * Update profile-level settings in shadow data
     */
    default void updateProfileInShadow(ObjectNode profileNode, SsidSettingsDTO request) {
        if (Objects.nonNull(request.getSeparatedSsid())) {
            profileNode.put("separatedSsid", request.getSeparatedSsid());
        }

        if (Objects.nonNull(request.getIsolated())) {
            profileNode.put("isolated", request.getIsolated());
        }

        if (Objects.nonNull(request.getMloEnabled())) {
            ObjectNode mloNode = profileNode.has("mlo") ?
                    (ObjectNode) profileNode.path("mlo") :
                    profileNode.putObject("mlo");
            mloNode.put("enabled", request.getMloEnabled());
        }
    }

    /**
     * Update radio-level settings in shadow data
     */
    default void updateRadioInShadow(ObjectNode radioNode, SsidDTO ssidRequest) {
        if (Objects.nonNull(ssidRequest.getEnabled())) {
            radioNode.put("enabled", ssidRequest.getEnabled());
        }

        if (Objects.nonNull(ssidRequest.getName())) {
            radioNode.put("name", ssidRequest.getName());
        }

        if (Objects.nonNull(ssidRequest.getSecurityMode())) {
            radioNode.put("securityMode", ssidRequest.getSecurityMode());
        }

        // Handle passphrase - complex logic that checks existing structure
        if (Objects.nonNull(ssidRequest.getPassphrase())) {
            updatePassphraseInRadio(radioNode, ssidRequest.getPassphrase());
        }
    }

    /**
     * Update passphrase handling both object and string formats
     */
    default void updatePassphraseInRadio(ObjectNode radioNode, String newPassphrase) {
        JsonNode currentPassphrase = radioNode.path("passphrase");
        if (currentPassphrase.isObject()) {
            // If passphrase is an object with "value" field
            ((ObjectNode) currentPassphrase).put("value", newPassphrase);
        } else {
            // If passphrase is a simple string
            radioNode.put("passphrase", newPassphrase);
        }
    }
}