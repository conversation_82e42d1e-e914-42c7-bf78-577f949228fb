package com.actiontec.optim.platform.api.v6.controller;

import com.actiontec.optim.platform.api.v6.dto.NCSEquipmentDTO;
import com.actiontec.optim.platform.api.v6.dto.NCSEquipmentQueryDTO;
import com.actiontec.optim.platform.api.v6.dto.PaginationResponse;
import com.actiontec.optim.platform.service.NCSEquipmentService;
import com.actiontec.optim.service.AuditService;
import com.incs83.app.business.v2.ManageCommonService;
import com.incs83.exceptions.handler.ValidationException;
import com.incs83.util.CommonUtils;
import io.swagger.annotations.Api;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

@RestController
@Api(value = "(V6) Equipment", description = "API's for NCSEquipment Operations", tags = { "Optim - (V6) Equipment" })
@RequestMapping(value = "/actiontec/api/v6/iam/equipment")
public class NCSEquipmentController {

    @Autowired
    AuditService auditService;

    @Autowired
    NCSEquipmentService ncsEquipmentService;

    @Autowired
    ManageCommonService manageCommonService;

    @RequestMapping(method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public PaginationResponse<NCSEquipmentDTO> getAllEquipment(
            @ModelAttribute NCSEquipmentQueryDTO queryDTO) throws Exception {
        if (CommonUtils.isEndUser()) {
            throw new ValidationException(HttpStatus.FORBIDDEN.value(),
                    "Insufficient permissions to access this resource");
        } else if (CommonUtils.isGroupAdmin()) {
            manageCommonService.checkIspIdForGroupAdmin(queryDTO.getIspId());

            // group admin may not bring ispId parameter, default set query ispId by login
            // user ispId.
            if (StringUtils.isEmpty(queryDTO.getIspId())) {
                String userGroupId = CommonUtils.getGroupIdOfLoggedInUser();
                String userIspId = manageCommonService.getIspByGroupId(userGroupId);
                queryDTO.setIspId(userIspId);
            }
        }
        ncsEquipmentService.checkQueryParameter(queryDTO);
        return ncsEquipmentService.getAllEquipment(queryDTO);
    }

    // @RequestMapping(method = RequestMethod.POST, produces =
    // MediaType.APPLICATION_JSON_VALUE)
    // @PreHandle(requestMethod = RequestMethod.POST, resourceType =
    // NCSEquipment.class)
    // @ResponseStatus(HttpStatus.CREATED)
    // public Map<String, String> postEquipment(
    // @ApiParam(value = "EquipmentId or Serial Number or STN", required = true)
    // @PathVariable(name = "equipmentIdOrSerialOrSTN") String
    // equipmentIdOrSerialOrSTN,
    // @ApiParam(value = "Post epuipment properties", required = true) @RequestBody
    // List<EquipmentBase> request,
    // @ApiParam(value = "Bearer Access Token required for Authentication", required
    // = true)
    // @RequestHeader(name = "X-Authorization") String accessToken,
    // HttpServletRequest httpServletRequest) throws Exception {
    // Map<String, String> result = new HashMap<>();
    //
    // for (EquipmentBase equipment : request) {
    // String id = ncsNetworkService.postEquipment(equipmentIdOrSerialOrSTN,
    // equipment);
    // result.put("id", id);
    // auditService.createAuditLog(CommonUtils.getUserIdOfLoggedInUser(),
    // equipment.getSerialNumber(), AuditorConstants.POST_EQUIPMENT, null, "201",
    // "", httpServletRequest);
    // }
    // return result;
    // }

    @RequestMapping(value = "/download/template", method = RequestMethod.GET, produces = "text/csv")
    public void downloadTemplate(HttpServletResponse response) throws IOException {
        ncsEquipmentService.downloadTemplate(response);
    }

}
