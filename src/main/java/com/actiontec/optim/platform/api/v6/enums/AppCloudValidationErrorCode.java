package com.actiontec.optim.platform.api.v6.enums;

public enum AppCloudValidationErrorCode {
    // Authentication / Token Issues
    MISSING_TOKEN(1001, "Token is missing or empty"),
    MISSING_ROLEID(1002, "Failed to extract role ID from token"),
    MISSING_NETWORK_TOKEN_ID(1003, "NetworkTokenId cannot be empty"),
    INVALID_EXCHANGE_TOKEN(1004, "Invalid exchange token"),
    MISSING_KID(1005, "Failed to extract kid from token: %s"),
    // Configuration Lookup
    APP_CLOUD_CONFIG_NOT_FOUND(1101, "Cannot found AppCloudConfig for kid: %s"),
    // Network Domain
    NETWORK_NOT_FOUND(1201, "Network not found for networkId: %s"),
    NETWORK_TOKEN_MISMATCH(1202, "Invalid network token id"),
    UNEXPECTED_ERROR(10000, "Unexpected authentication error");

    private final int code;
    private final String message;

    AppCloudValidationErrorCode(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public int getCode() { return code; }
    public String getMessage() { return message; }
}

