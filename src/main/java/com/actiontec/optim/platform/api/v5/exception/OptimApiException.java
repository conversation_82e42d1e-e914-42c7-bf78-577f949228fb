package com.actiontec.optim.platform.api.v5.exception;

import com.actiontec.optim.platform.api.v6.dto.ErrorResponseDTO;
import lombok.Getter;
import lombok.ToString;
import org.springframework.http.HttpStatus;
import org.springframework.web.server.ResponseStatusException;

@Getter
@ToString(callSuper = true)
public class OptimApiException extends ResponseStatusException {
    // Customizable error code for API or frontend usage
    private final int code;
    private final String message;

    // use default status code as error code
    public OptimApiException(HttpStatus httpStatus, String message) {
        super(httpStatus, message);
        this.code = httpStatus.value();
        this.message = message;
    }


    // use custom error code
    public OptimApiException(HttpStatus httpStatus, int code, String message) {
        super(httpStatus, message);
        this.code = code;
        this.message = message;
    }

    public ErrorResponseDTO toErrorResponseDTO() {
        return new ErrorResponseDTO(code, message);
    }
}
