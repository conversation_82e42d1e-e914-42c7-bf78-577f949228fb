package com.actiontec.optim.platform.api.v6.controller;

import com.actiontec.optim.platform.api.v5.model.EquipmentActionRequest;
import com.actiontec.optim.platform.api.v6.dto.*;
import com.actiontec.optim.platform.service.NCSEquipmentService;
import com.actiontec.optim.platform.service.NCSNetworkService;
import com.actiontec.optim.platform.service.TopologyService;
import com.actiontec.optim.util.CustomStringUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@Api(value = "(V6) Network", description = "API's for Network Statistics", tags = { "Optim - (V6) Network" })
@RequestMapping(value = "/actiontec/api/v6/networks")
public class NetworkOperationsController {

    @Autowired
    private TopologyService topologyService;

    @Autowired
    private NCSNetworkService ncsNetworkService;

    @Autowired
    private NCSEquipmentService ncsEquipmentService;

    @RequestMapping(method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public PaginationResponse<NetworkOperationsDTO> getNetworkByKeyword(
            @ModelAttribute NetworkOperationsQueryDTO queryDTO) throws Exception {
        return ncsNetworkService.getNetworksByKeyword(queryDTO);
    }

    @RequestMapping(value = "/{networkId}/topology/history", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public List<SnapshotDataDTO> getTopologyHistory(
            @ApiParam(value = "Network Id", required = true) @PathVariable(name = "networkId") String networkId,
            @ApiParam(value = "startTime") @RequestParam(required = false, name = "startTime") Long startTime,
            @ApiParam(value = "endTime") @RequestParam(required = false, name = "endTime") Long endTime,
            @ApiParam(value = "deviceId") @RequestParam(required = false, name = "deviceId") String deviceId)
            throws Exception {

        if (startTime == null) {
            startTime = Instant.now().minus(1, ChronoUnit.DAYS).toEpochMilli();
        }

        if (endTime == null) {
            endTime = Instant.now().toEpochMilli();
        }

        return topologyService.getHistory(networkId, startTime, endTime, deviceId);
    }

    @RequestMapping(value = "/{networkId}/proxy", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public CpeApiResponse getCpeApiProxy(
            @PathVariable(name = "networkId") String networkId,
            @ApiParam(value = "Proxy request details", required = true) @RequestBody CpeApiRequest proxyRequest)
            throws Exception {
        return ncsNetworkService.getCpeApiProxy(networkId, proxyRequest, CustomStringUtils.EMPTY);
    }

    @RequestMapping(value = "/{networkId}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public NetworkOperationsForAppCloudDTO getNetworkForAppCloud(
            @ApiParam(value = "Network Id", required = true) @PathVariable(name = "networkId") String networkId)
            throws Exception {
        return ncsNetworkService.getNetworkForAppCloud(networkId);
    }

    @RequestMapping(method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public NetworkClaimResponse claimNetwork(
            @ApiParam(value = "Network claim request", required = true) @RequestBody @Valid NetworkClaimRequest claimRequest,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true) @RequestHeader(name = "X-Authorization") String accessToken)
            throws Exception {
        return ncsNetworkService.claimNetwork(claimRequest.getSerialNumber(), claimRequest.getAppCloudUserId(),
                accessToken);
    }

    @RequestMapping(value = "/{networkId}", method = RequestMethod.PATCH, produces = MediaType.APPLICATION_JSON_VALUE)
    public void updateNetworkName(
            @PathVariable(name = "networkId") String networkId,
            @ApiParam(value = "New network name", required = true) @RequestBody NCSNetworkRequest renameNetworkRequest)
            throws Exception {
        ncsNetworkService.updateNetworkForAppCloud(networkId, renameNetworkRequest);
    }

    @RequestMapping(value = "/{networkId}", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public void disclaimNetwork(
            @PathVariable(name = "networkId") String networkId) throws Exception {
        ncsNetworkService.disclaimNetwork(networkId);
    }

    @RequestMapping(value = "/{networkId}/internet-connection", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public HashMap<String, Object> getInternetConnectionStatus(
            @PathVariable(name = "networkId") String networkId) throws Exception {
        return ncsNetworkService.getInternetConnectionStatus(networkId);
    }

    @RequestMapping(value = "/{networkId}/devices", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public List<Map<String, Object>> getNetworkDevices(
            @PathVariable(name = "networkId") String networkId) throws Exception {
        return ncsNetworkService.getNetworkDevices(networkId);
    }

    @RequestMapping(value = "/{networkId}/devices/{deviceId}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public Map<String, Object> getNetworkDevice(
            @PathVariable(name = "networkId") String networkId,
            @PathVariable(name = "deviceId") String deviceId) throws Exception {
        return ncsNetworkService.getNetworkDevice(networkId, deviceId);
    }

    @RequestMapping(value = "/{networkId}/devices/{deviceId}", method = RequestMethod.PATCH, produces = MediaType.APPLICATION_JSON_VALUE)
    public void updateNetworkDevice(
            @PathVariable(name = "networkId") String networkId,
            @PathVariable(name = "deviceId") String deviceId,
            @RequestBody DeviceUpdateRequest updateRequest) throws Exception {
        ncsNetworkService.updateNetworkDevice(networkId, deviceId, updateRequest);
    }

    @RequestMapping(value = "/{networkId}/devices/{deviceId}/action", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public void executeDeviceAction(
            @PathVariable("networkId") String networkId,
            @PathVariable("deviceId") String deviceId,
            @RequestBody @Valid DeviceActionRequest actionRequest) throws Exception {

        ncsNetworkService.executeDeviceAction(networkId, deviceId, actionRequest.getAction());
    }

    @RequestMapping(value = "/{networkId}/ssid-settings", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public List<SsidSettingsDTO> getSsidSettings(
            @PathVariable(name = "networkId") String networkId) throws Exception {
        return ncsNetworkService.getSsidSettings(networkId);
    }

    @RequestMapping(value = "/{networkId}/ssid-settings", method = RequestMethod.PATCH, produces = MediaType.APPLICATION_JSON_VALUE)
    public void updateSsidSettings(
            @PathVariable(name = "networkId") String networkId,
            @ApiParam(value = "SSID settings to update", required = true) @RequestBody @Valid List<SsidSettingsDTO> ssidSettingsRequest)
            throws Exception {
        ncsNetworkService.updateSsidSettings(networkId, ssidSettingsRequest);
    }

    @RequestMapping(value = "/{networkId}/system")
    public void updateNetworkSystemConfig(
            @PathVariable("networkId") String networkId,
            @ApiParam(value = "System config to update", required = true) @RequestBody @Valid SystemConfigDTO systemConfigRequest)
            throws Exception {
        ncsNetworkService.updateNetworkSystemConfig(networkId, systemConfigRequest);
    }

    @RequestMapping(value = "/{networkId}/equipment", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Void> addExtenderPairing(
            @ApiParam(value = "Network Id", required = true) @PathVariable String networkId,
            @ApiParam(value = "Extender pairing request", required = true) @RequestBody @Valid ExtenderPairingRequest request) throws Exception {
        ncsNetworkService.processExtenderPairing(networkId, request.getExtenderSerialNumber(), RequestMethod.POST);
        return ResponseEntity.status(HttpStatus.ACCEPTED).build();
    }

    @RequestMapping(value = "/{networkId}/equipment/{equipmentId}", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Void> deleteExtenderPairing(
            @ApiParam(value = "Network Id", required = true) @PathVariable String networkId,
            @PathVariable(name = "equipmentId") String extenderEquipmentId) throws Exception {
        ncsNetworkService.processExtenderPairing(networkId, extenderEquipmentId, RequestMethod.DELETE);
        return ResponseEntity.status(HttpStatus.ACCEPTED).build();
    }

    @RequestMapping(value = "/{networkId}/equipment", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public List<NCSEquipmentForAppCloudDTO> getNetworkAllEquipment(
            @PathVariable(name = "networkId") String networkId) throws Exception {
        return ncsEquipmentService.getNetworkAllEquipment(networkId);
    }

    @RequestMapping(value = "/{networkId}/equipment/{equipmentId}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public NCSEquipmentForAppCloudDTO getNetworkEquipment(
            @PathVariable(name = "networkId") String networkId,
            @PathVariable(name = "equipmentId") String equipmentId) throws Exception {
        return ncsEquipmentService.getNCSEquipmentByNetworkIdAndEquipmentId(networkId, equipmentId);
    }

    @RequestMapping(value = "/{networkId}/equipment/{equipmentId}", method = RequestMethod.PATCH, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Void> updateNetworkEquipment(
            @PathVariable(name = "networkId") String networkId,
            @PathVariable(name = "equipmentId") String equipmentId,
            @RequestBody @Valid NCSEquipmentRequest updateRequest) throws Exception {
        ncsEquipmentService.updateFriendlyName(networkId, equipmentId, updateRequest);
        return ResponseEntity.status(HttpStatus.OK).build();
    }

    @RequestMapping(value = "/{networkId}/equipment/{equipmentId}/actions", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Void> executeNetworkEquipmentActions(
            @PathVariable(name = "networkId") String networkId,
            @PathVariable(name = "equipmentId") String equipmentId,
            @RequestBody EquipmentActionRequest request) throws Exception {
        ncsEquipmentService.executeNetworkEquipmentActions(networkId, equipmentId, request);
        return ResponseEntity.status(HttpStatus.OK).build();
    }

    @RequestMapping(value = "/{networkId}/speed-test", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public SpeedTestResponse initiateSpeedTest(
            @PathVariable(name = "networkId") String networkId,
            @ApiParam(value = "Speed test request", required = true) @RequestBody @Valid SpeedTestRequest speedTestRequest) throws Exception {
        return ncsNetworkService.initiateSpeedTest(networkId, speedTestRequest);
    }

    @RequestMapping(value = "/{networkId}/speed-test/records/{recordId}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public SpeedTestRecordResponse getSpeedTestRecord(
            @PathVariable(name = "networkId") String networkId,
            @PathVariable(name = "recordId") String recordId) throws Exception {
        return ncsNetworkService.getSpeedTestRecord(networkId, recordId);
    }

    @RequestMapping(value = "/{networkId}/speed-test/records", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public List<SpeedTestRecordResponse> getSpeedTestRecords(
            @PathVariable(name = "networkId") String networkId,
            @ApiParam(value = "Equipment ID filter") @RequestParam(required = false, name = "equipmentId") String equipmentId) throws Exception {
        return ncsNetworkService.getSpeedTestRecords(networkId, equipmentId);
    }

    @GetMapping("/{networkId}/port-forwarding/rules")
    public ResponseEntity<List<PortForwardRule>> getRules(
            @RequestHeader(value = "Network-Token", required = false) String networkToken,
            @PathVariable String networkId) throws Exception {

        List<PortForwardRule> rules = ncsNetworkService.getPortForwardRules(networkId);
        return ResponseEntity.ok(rules);
    }

    /**
     * GET /actiontec/api/v6/networks/{networkId}/port-forwarding/rules/{ruleId}
     * Get a specific port forwarding rule
     */
    @GetMapping("/{networkId}/port-forwarding/rules/{ruleId}")
    public ResponseEntity<PortForwardRule> getRule(
            @RequestHeader(value = "Network-Token", required = false) String networkToken,
            @PathVariable String networkId,
            @PathVariable String ruleId) throws Exception {

        PortForwardRule rule = ncsNetworkService.getPortForwardRule(networkId, ruleId);
        return ResponseEntity.ok(rule);
    }

    /**
     * POST /actiontec/api/v6/networks/{networkId}/port-forwarding/rules
     * Create port forwarding rules
     */
    @PostMapping("/{networkId}/port-forwarding/rules")
    public ResponseEntity<Void> createRules(
            @RequestHeader(value = "Network-Token", required = false) String networkToken,
            @PathVariable String networkId,
            @Valid @RequestBody List<PortForwardRuleRequest> requests) throws Exception {
        ncsNetworkService.addPortForwardRules(networkId, requests);
        return ResponseEntity.status(HttpStatus.CREATED).build();
    }

    /**
     * PATCH /actiontec/api/v6/networks/{networkId}/port-forwarding/rules/{ruleId}
     * Update a port forwarding rule
     */
    @PatchMapping("/{networkId}/port-forwarding/rules/{ruleId}")
    public ResponseEntity<Void> updateRule(
            @RequestHeader(value = "Network-Token", required = false) String networkToken,
            @PathVariable String networkId,
            @PathVariable String ruleId,
            @Valid @RequestBody PortForwardRuleRequest request) throws Exception {
        ncsNetworkService.updatePortForwardRule(networkId, ruleId, request);
        return ResponseEntity.status(HttpStatus.OK).build();
    }

    /**
     * DELETE /actiontec/api/v6/networks/{networkId}/port-forwarding/rules/{ruleId}
     * Delete a specific port forwarding rule
     */
    @DeleteMapping("/{networkId}/port-forwarding/rules/{ruleId}")
    public ResponseEntity<Void> deleteRule(
            @PathVariable String networkId,
            @PathVariable String ruleId) throws Exception {
        ncsNetworkService.deletePortForwardRule(networkId, ruleId);
        return ResponseEntity.status(HttpStatus.NO_CONTENT).build();
    }

    /**
     * DELETE /actiontec/api/v6/networks/{networkId}/port-forwarding/rules
     * Delete multiple port forwarding rules
     */
    @DeleteMapping("/{networkId}/port-forwarding/rules")
    public ResponseEntity<Void> deleteRules(
            @PathVariable String networkId,
            @Valid @RequestBody PortForwardRulesDelRequest request) throws Exception {
        ncsNetworkService.deletePortForwardRules(networkId, request);
        return ResponseEntity.status(HttpStatus.NO_CONTENT).build();
    }
}
