package com.actiontec.optim.platform.service;

import com.actiontec.optim.mongodb.dao.InternetServiceProviderDao;
import com.actiontec.optim.platform.adapter.At3Adapter;
import com.actiontec.optim.platform.api.v5.exception.OptimApiException;
import com.actiontec.optim.platform.model.EquipmentModel;
import com.actiontec.optim.platform.repository.EquipmentModelIspConfigRepo;
import com.actiontec.optim.platform.repository.EquipmentModelRepo;
import com.actiontec.optim.util.CustomStringUtils;
import com.actiontec.optim.platform.repository.EquipmentModelFirmwareRepo;
import com.incs83.app.entities.Compartment;
import com.incs83.mt.DataAccessService;
import com.incs83.util.CommonUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Service
public class EquipmentModelService {

    private final Logger logger = LogManager.getLogger(this.getClass());

    @Autowired
    private At3Adapter at3Adapter;

    @Autowired
    private EquipmentModelRepo equipmentModelRepo;

    @Autowired
    private DataAccessService dataAccessService;

    @Autowired
    private EquipmentModelFirmwareRepo equipmentModelFirmwareRepo;

    @Autowired
    private InternetServiceProviderDao internetServiceProviderDao;

    @Autowired
    private EquipmentModelIspConfigRepo equipmentModelIspConfigRepo;

    public EquipmentModel createModel(EquipmentModel equipmentModel) throws Exception {

        if (CommonUtils.isSysAdmin()) {
            EquipmentModel result = equipmentModelRepo.addEquipmentModel(equipmentModel);
            return result;
        }
        return null;
    }

    public List<EquipmentModel> findModels() throws Exception {

        List<EquipmentModel> equipmentModelList = new ArrayList<>();

        if (CommonUtils.isSysAdmin()) {
            equipmentModelList = equipmentModelRepo.findEquipmentModels(null);
        } else if (CommonUtils.isGroupAdmin()) {
            Compartment compartment = (Compartment) dataAccessService.read(Compartment.class,
                    CommonUtils.getGroupIdOfLoggedInUser());
            equipmentModelList = equipmentModelRepo.findEquipmentModels(compartment.getIspId());
        }
        return equipmentModelList;
    }

    public EquipmentModel findModelById(String modelId) throws Exception {

        EquipmentModel equipmentModel = null;

        if (CommonUtils.isSysAdmin()) {
            equipmentModel = equipmentModelRepo.findEquipmentModelById(modelId, null);
        } else if (CommonUtils.isGroupAdmin()) {
            String groupId = CommonUtils.getGroupIdOfLoggedInUser();
            equipmentModel = equipmentModelRepo.findEquipmentModelById(modelId, groupId);
        }
        return equipmentModel;
    }

    public Boolean updateModelById(String modelId, EquipmentModel equipmentModel) throws Exception {

        if (CommonUtils.isSysAdmin()) {
            equipmentModelRepo.updateEquipmentModelById(modelId, equipmentModel);
            return true;
        }
        return false;
    }

    public Boolean deleteModelById(String modelId) throws Exception {

        if (CommonUtils.isSysAdmin()) {
            // Check if the equipmentModel is referenced by any equipmentModelFirmware
            if (equipmentModelFirmwareRepo.existsByEquipmentModelId(modelId)) {
                throw new OptimApiException(HttpStatus.BAD_REQUEST,
                        "Cannot delete equipment model because it is referenced by one or more irmware");
            }
            // Check if the equipmentModel is referenced by any equipmentModelIspConfig
            if (equipmentModelIspConfigRepo.existsByEquipmentModelId(modelId)) {
                throw new OptimApiException(HttpStatus.BAD_REQUEST,
                        "Cannot delete equipment model because it is referenced by one or more equipment model ISP config");
            }
            equipmentModelRepo.deleteEquipmentModelById(modelId);
            return true;
        }
        return false;
    }

    public String getIconUrl(String modelName) throws Exception {

        EquipmentModel equipmentModel = equipmentModelRepo.findEquipmentModelByModelName(modelName);

        if (!ObjectUtils.isEmpty(equipmentModel) && !ObjectUtils.isEmpty(equipmentModel.getSnapshot())) {
            return equipmentModel.getSnapshot().getFileEntry();
        } else {
            logger.error("EquipmentModel not found or snapshot is null: {}", modelName);
        }

        return CustomStringUtils.EMPTY;
    }
}
