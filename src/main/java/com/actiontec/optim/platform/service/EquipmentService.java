package com.actiontec.optim.platform.service;

import com.actiontec.optim.mongodb.dao.ApDetailDao;
import com.actiontec.optim.mongodb.dao.EquipmentStatsDao;
import com.actiontec.optim.mongodb.dao.RebootHistoryDao;
import com.actiontec.optim.mongodb.dto.ApDetailDto;
import com.actiontec.optim.mongodb.dto.EquipmentStatsDto;
import com.actiontec.optim.mongodb.dto.RebootHistoryDto;
import com.actiontec.optim.platform.adapter.At3Adapter;
import com.actiontec.optim.platform.annotation.PermissionHandle;
import com.actiontec.optim.platform.api.v5.model.EquipmentActionRequest;
import com.actiontec.optim.platform.api.v5.model.EquipmentBase;
import com.actiontec.optim.platform.api.v5.model.EquipmentInfoDto;
import com.actiontec.optim.platform.api.v5.model.NameBase;
import com.actiontec.optim.platform.constant.ActiontecSQL;
import com.actiontec.optim.platform.constant.ApplicationConstants;
import com.actiontec.optim.platform.mapper.EquipmentModelMapper;
import com.actiontec.optim.platform.model.*;
import com.actiontec.optim.platform.model.enums.EquipmentType;
import com.actiontec.optim.platform.repository.EquipmentModelRepo;
import com.actiontec.optim.service.CpeRpcService;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.incs83.app.business.v2.ManageCommonService;
import com.incs83.app.business.v2.ManageEquipmentService;
import com.incs83.app.business.v2.RPCUtilityService;
import com.incs83.app.common.v2.AgentVersion;
import com.incs83.app.constants.misc.RpcConstants;
import com.incs83.app.constants.templates.MqttTemplate;
import com.incs83.app.entities.Equipment;
import com.incs83.app.entities.TrackingEquipment;
import com.incs83.app.service.data.MongoServiceImpl;
import com.incs83.app.utils.ManageLongWaitAPI;
import com.incs83.constants.ApplicationCommonConstants;
import com.incs83.context.ExecutionContext;
import com.incs83.exceptions.handler.ValidationException;
import com.incs83.mt.DataAccessService;
import com.incs83.service.CommonService;
import com.incs83.util.CommonUtils;
import com.mongodb.BasicDBObject;
import com.mongodb.DBObject;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import java.util.*;

import static com.incs83.app.constants.misc.ActiontecConstants.*;
import static com.incs83.app.constants.misc.ActiontecConstants.AP_DETAIL;
import static com.incs83.app.constants.misc.ApplicationConstants.*;
import static com.incs83.app.constants.misc.ApplicationConstants.GATEWAY;
import static com.incs83.app.constants.misc.ApplicationConstants.TIMESTAMP;
import static com.incs83.app.constants.queries.EquipmentSQL.GET_EQUIPMENT_COUNT_BY_COMPARTMENT;
import static com.incs83.app.constants.queries.EquipmentSQL.GET_EQUIPMENT_COUNT_SQL;
import static com.incs83.constants.ApplicationCommonConstants.*;

@Service
public class EquipmentService {
    private final Logger logger = LogManager.getLogger(this.getClass());

    @Autowired
    private MongoServiceImpl mongoService;
    @Autowired
    private ManageCommonService manageCommonService;
    @Autowired
    private EquipmentModelRepo equipmentModelRepo;
    @Autowired
    private EquipmentModelMapper equipmentModelMapper;
    @Autowired
    private ObjectMapper objectMapper;
    @Autowired
    private CpeRpcService cpeRpcService;
    @Autowired
    private At3Adapter at3Adapter;
    @Autowired
    private CommonService commonService;
    @Autowired
    private ApDetailDao apDetailDao;
    @Autowired
    private RPCUtilityService rpcUtilityService;
    @Autowired
    private ManageLongWaitAPI manageLongWaitAPI;
    @Autowired
    private DataAccessService dataAccessService;
    @Autowired
    private RebootHistoryDao rebootHistoryDao;
    @Autowired
    private EquipmentStatsDao equipmentStatsDao;
    @Autowired
    private ManageEquipmentService manageEquipmentService;

    @Value("${elastic-search.enable}")
    private Boolean elasticSearchEnable;
    @Autowired
    private SmmAppService smmAppService;


    public enum EquipmentActions {
        Reboot("reboot"),
        RestoreDefault("restoreDefault"),
        EnableBurstMode("burst"),
        DisableBurstMode("burst"),
        Burst("burst"),
        EnableWPS("wpsButtonTrigger");
        String value;

        EquipmentActions(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public void setValue(String value) {
            this.value = value;
        }

        public static String getAction(String action) {
            for (EquipmentActions p : values()) {
                if (StringUtils.equals(p.name(), action))
                    return p.getValue();
            }
            return null;
        }
    }


    public List<EquipmentInfoDto> getEquipmentInfo(String equipmentIdOrSerialOrSTN, String serialNumber, String
            infoType) throws Exception {
        Equipment userEquipment = manageCommonService.getUserAPFromSubscriberIdOrApId(equipmentIdOrSerialOrSTN);
        if (Objects.isNull(userEquipment.getRgwSerial()) || userEquipment.getRgwSerial().isEmpty())
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "RGW Serial cannot be empty");

        if (infoType.equals(system)) {
            manageCommonService.checkSerialNumOfEquipment(serialNumber, userEquipment);
        }

        //manageCommonService.subscriberDataExistInMongo(userEquipment);
        //manageCommonService.subscriberGroupMisMatch(userEquipment);
        HashMap<String, Object> params = new HashMap<>();
        params.put("userId", userEquipment.getRgwSerial());

        if (infoType.equals(system)) {
            params.put("serialNumber", serialNumber);
        }

        BasicDBObject mongoFieldOptions = new BasicDBObject();
        mongoFieldOptions.clear();
        mongoFieldOptions.put("DSL", 0);
        mongoFieldOptions.put("network", 0);
        mongoFieldOptions.put("wan", 0);
        mongoFieldOptions.put("wifiConf", 0);
        mongoFieldOptions.put("ethPorts", 0);
        mongoFieldOptions.put("wifiRadios", 0);
        mongoFieldOptions.put("bssid24G", 0);
        mongoFieldOptions.put("bssid5G", 0);
        mongoFieldOptions.put("ssids", 0);

        List<BasicDBObject> equipmentList = mongoService.findList(params, AP_DETAIL, mongoFieldOptions);
        equipmentList = manageCommonService.filterEquipmentsAndCheckForRgwAndExt(equipmentList);

        List<EquipmentInfoDto> equipmentInfoDtoList = new ArrayList<>();
        for (BasicDBObject equipmentObject : equipmentList) {
            EquipmentInfoDto equipmentInfo = new EquipmentInfoDto();
            equipmentInfo.setId(String.valueOf(equipmentObject.get("serialNumber")));
            equipmentInfo.setStatus(manageCommonService.getCurrentDevStatus(equipmentObject) ? "Down" : "Up");
            equipmentInfo.setSerialNumber(Objects.isNull((equipmentObject.get("serialNumber"))) ? null : (equipmentObject.get("serialNumber")).toString());
            equipmentInfo.setMacAddress(Objects.isNull((equipmentObject.get("macAddress"))) ? null : (equipmentObject.get("macAddress")).toString());
            equipmentInfo.setModelName(Objects.nonNull(equipmentObject.get("modelName")) ? equipmentObject.get("modelName").toString() : "N/A");
            equipmentInfo.setName(manageCommonService.getDisplayNameForEquipment(equipmentObject));
            equipmentInfo.setFwVersion(Objects.nonNull(equipmentObject.get("fwVersion")) ? equipmentObject.get("fwVersion").toString() : "N/A");
            equipmentInfo.setAgentVersion(Objects.nonNull(equipmentObject.get("buildVersion")) ? equipmentObject.get("buildVersion").toString() : "N/A");
            equipmentInfo.setCpuUsage(Objects.isNull(equipmentObject.get("cpuUsage")) ? 0 : Integer.valueOf(equipmentObject.get("cpuUsage").toString()));
            equipmentInfo.setMemoryUsage(Objects.isNull(equipmentObject.get("memUsage")) ? 0 : Integer.valueOf(equipmentObject.get("memUsage").toString()));
//            equipmentInfo.setTotalMemory(Objects.isNull(equipmentObject.get("totalMemory")) ? 0 : Long.valueOf(equipmentObject.get("totalMemory").toString()));
//            equipmentInfo.setAvailableMemory(Objects.isNull(equipmentObject.get("availableMemory")) ? 0 : Long.valueOf(equipmentObject.get("availableMemory").toString()));
//            equipmentInfo.setTotalFlashSpace(Objects.isNull(equipmentObject.get("totalFlashSpace")) ? 0 : Long.valueOf(equipmentObject.get("totalFlashSpace").toString()));
//            equipmentInfo.setAvailableFlashSpace(Objects.isNull(equipmentObject.get("availableFlashSpace")) ? 0 : Long.valueOf(equipmentObject.get("availableFlashSpace").toString()));
            equipmentInfo.setEquipmentType(Objects.isNull((equipmentObject.get("type"))) ? null : EquipmentType.getFormalName(String.valueOf(equipmentObject.get("type"))));
            //DBObject health = (DBObject) equipmentObject.get("health");
            equipmentInfo.setLastRebootTime(Objects.isNull(equipmentObject.get("uptime")) ? 0 : Long.valueOf(equipmentObject.get("uptime").toString()));
            equipmentInfo.setLastDataProcessedTime(Objects.isNull(equipmentObject.get("dbReportTimestamp")) ? 0 : Long.valueOf(equipmentObject.get("dbReportTimestamp").toString()));
            equipmentInfo.setLastReportTime(Objects.isNull(equipmentObject.get("ntReportTimestamp")) ? 0 : Long.valueOf(equipmentObject.get("ntReportTimestamp").toString()));

            EquipmentModel equipmentModel = equipmentModelRepo.findEquipmentModelByModelName(equipmentObject.get("modelName").toString());
            if (equipmentModel != null) {
                equipmentInfo.setSnapshot(equipmentModelMapper.toModelSnapshotResponse(equipmentModel.getSnapshot()));
                equipmentInfo.setHardware(equipmentModelMapper.toModelHardwareDto(equipmentModel.getHardware()));
                equipmentInfo.setFeatures(equipmentModel.getFeatures());
                equipmentInfo.setEquipmentTypeId(equipmentModel.getId());

                long totalMemory = equipmentInfo.getHardware().getSmmMemorySize() > 0 ? equipmentInfo.getHardware().getSmmMemorySize() : equipmentInfo.getHardware().getMemorySize();
                long totalFlash = equipmentInfo.getHardware().getSmmFlashSpaceSize() > 0 ? equipmentInfo.getHardware().getSmmFlashSpaceSize() : equipmentInfo.getHardware().getFlashSpaceSize();
                equipmentInfo.setTotalMemory(totalMemory);
                equipmentInfo.setTotalFlashSpace(totalFlash);

                SmmUsage smmUsage = smmAppService.getUsage(equipmentInfo.getSerialNumber(), equipmentInfo.getFwVersion());
                equipmentInfo.setAvailableMemory(equipmentInfo.getTotalMemory() - smmUsage.getMemoryUsed());
                equipmentInfo.setAvailableFlashSpace(equipmentInfo.getTotalFlashSpace() - smmUsage.getFlashUsed());
            }

            equipmentInfoDtoList.add(equipmentInfo);
        }

        return equipmentInfoDtoList;
    }

    @PermissionHandle
    public void updateFriendlyName(String stn, String equipmentId, NameBase request) throws Exception {
        String name = request.getName();
        if (StringUtils.isBlank(name))
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Invalid name.");
        BasicDBObject insertField = new BasicDBObject();
        insertField.put("friendlyName", name);
        BasicDBObject basicDBObjectUpdate = new BasicDBObject();
        basicDBObjectUpdate.put("$set", insertField);
        HashMap<String, Object> params = new HashMap<>();
        params.put("userId", at3Adapter.getRgwSerialByStn(stn));
        params.put("serialNumber", equipmentId);
        mongoService.findAndModify(params, AP_DETAIL, null, basicDBObjectUpdate);
    }

    private void sendLagcyReboot(String userId, String serial) throws Exception {

        String tid = CommonUtils.generateUUID();

        HashMap<String, String> publishParams = new HashMap<>();
        publishParams.put("-USER_ID-", userId);
        publishParams.put("-S_ID-", serial);
        publishParams.put("-ID-", String.valueOf(Calendar.getInstance().getTimeInMillis()));
        publishParams.put("-TID-", tid);

        HashMap<String, Object> data = new HashMap<>();
        data.put("_id", tid);
        data.put("rpcType", "AP-Reboot");
        data.put("userId", userId);

        mongoService.create(RPC_RESULT_INFO, data);
        HashMap<String, String> equipmentProps = commonService.read(EQUIPMENT_DIAGNOSTIC_CONFIG);
        Integer max_Tries = Integer.valueOf(equipmentProps.get(RPC_POLL_COUNT));
        rpcUtilityService.publishToTopic(publishParams, MqttTemplate.REBOOT_AP_FOR_USER, MqttTemplate.TOPIC_FOR_RPC_CALL, max_Tries);
        manageCommonService.processRPCResult(tid, max_Tries, THREAD_TO_SLEEP);

        BasicDBObject query = new BasicDBObject();
        query.put("_id", tid);
        DBObject rpcResult = mongoService.findOne(RPC_RESULT_INFO, query);
        if (Objects.nonNull(rpcResult) && RPC_RESULT.equals(rpcResult.get("result"))) {

            HashMap<String, Object> queryParams = new HashMap<>();
            queryParams.put("userId", userId);
            queryParams.put("serialNumber", serial);

            DBObject aPDetails = mongoService.findOne(queryParams, new HashMap<>(), AP_DETAIL, TIMESTAMP, DESC, new BasicDBObject("_id", com.incs83.app.constants.misc.ApplicationConstants.ZERO));
            if (Objects.nonNull(aPDetails)) {
                long lastMetricReceived = Long.valueOf(String.valueOf(("v3".equalsIgnoreCase(String.valueOf(aPDetails.get("etlVersion"))) || Objects.isNull(aPDetails.get("sendingTime"))) ? aPDetails.get("timestamp") : aPDetails.get("sendingTime")));

                HashMap<String, String> commonProps = commonService.read(COMMON_CONFIG);
                int equipmentOfflineTime = 7;
                try {
                    equipmentOfflineTime = Integer.valueOf(commonProps.get(COMMON_DEVICE_STATUS_RETENTION_TIME));
                } catch (Exception e) {

                }

                BasicDBObject dataToUpdate = new BasicDBObject();
                dataToUpdate.put("$set", new BasicDBObject("sendingTime", lastMetricReceived - ((equipmentOfflineTime + 10) * 60 * 1000L)));

                query.clear();
                query.put("userId", userId);
                query.put("serialNumber", serial);

                mongoService.update(query, dataToUpdate, true, false, AP_DETAIL);
            }
        }
    }

    @PermissionHandle(checkStatus = true)
    public void postActionsForEquipment(String stn, String equipmentId, EquipmentActionRequest request) throws Exception {
        String action = EquipmentActions.getAction(request.getAction());
        if (null == action) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Invalid Action.");
        }

        String userId = at3Adapter.getRgwSerialByStn(stn);
        Optional<ApDetailDto> optApDetailDto = apDetailDao.findByUserIdAndSerial(userId, equipmentId);
        AgentVersion agentVersion = new AgentVersion(optApDetailDto.get().getBuildVersion());

        String rpcUri = null;
        String method = "POST";
        Long sendingTime = 0L;
        Map<String, Object> payloadMap = new HashMap<>();
        BasicDBObject dataToUpdate = new BasicDBObject();
        DBObject data = new BasicDBObject();
        if (action.equals(EquipmentActions.Reboot.getValue())) {
            manageEquipmentService.invokeInternetRPCMethodsForEquipment(stn, equipmentId, AP_REBOOT, null, null, null);

//            if (agentVersion.getMajorVersion() < 4) {
//                sendLagcyReboot(userId, equipmentId);
//            } else {
//                rpcUri = RpcConstants.DIAG_ACTION_URI;
//                payloadMap.put("action", action);
//                sendingTime = CommonUtils.getCurrentTimeInMillis();
//
//                Map<String, Object> rpcResult = cpeRpcService.sendRpcAndExpectSucceeded(userId, equipmentId, rpcUri, method, objectMapper.writeValueAsString(payloadMap), ApplicationConstants.RPC_DEFAULT_MAX_TRIES, ApplicationConstants.RPC_DEFUALT_RETRY_INTERVAL_MILLIS);
//                if (rpcResult != null) {
//                    data.put("connectionStatus", "offline");
//                }
//            }
        }
        /*
        else if (action.equals(EquipmentActions.RestoreDefault.getValue())) {
            Map<String, Object> rpcResult = cpeRpcService.sendRpcAndExpectSucceeded(equipmentId, equipmentId, rpcUri, method, objectMapper.writeValueAsString(payloadMap), ApplicationConstants.RPC_DEFAULT_MAX_TRIES, ApplicationConstants.RPC_DEFUALT_RETRY_INTERVAL_MILLIS);
            if (rpcResult != null) {
                data.put("connectionStatus", "offline");
            }
        }
         */
        else if (action.equals(EquipmentActions.Burst.getValue())) {
            method = "PUT";
            rpcUri = RpcConstants.BURST_MODE_URI;
            HashMap<String, String> equipmentProps = commonService.read(EQUIPMENT_DIAGNOSTIC_CONFIG);
            EquipmentActionRequest.BurstMode burstMode = request.getBurstMode();
            Long duration = 0L;
            if (EquipmentActions.EnableBurstMode.name().equals(request.getAction())) {
                if (Objects.nonNull(burstMode)) {
                    duration = burstMode.getDuration();

                    if (duration.longValue() < 0L || duration.longValue() > ApplicationConstants.SECONDS_OF_DAY.longValue()) {
                        throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "duration must be between 0 and 86400.");
                    }
                } else {
                    duration = Long.valueOf(equipmentProps.get(DIAGNOSTIC_DURATION_SECONDS));
                }
            }
            Long repInterval = Long.valueOf(equipmentProps.get(DIAGNOSTIC_REPORTING_INTERVAL_SECONDS));
            Long colInterval = Long.valueOf(equipmentProps.get(DIAGNOSTIC_COLLECTION_INTERVAL_SECONDS));
            payloadMap.put("burstDuration", duration);
            payloadMap.put("collectionInterval", colInterval);
            payloadMap.put("reportInterval", repInterval);
            data.put("diagnosEnabled", StringUtils.equals((CharSequence) request.getAction(), EquipmentActions.DisableBurstMode.name()) ? false : true);

            sendingTime = CommonUtils.getCurrentTimeInMillis();
            cpeRpcService.sendRpcAndExpectSucceeded(userId, equipmentId, rpcUri, method, objectMapper.writeValueAsString(payloadMap), ApplicationConstants.RPC_DEFAULT_MAX_TRIES, ApplicationConstants.RPC_DEFUALT_RETRY_INTERVAL_MILLIS);
        } else if (action.equals(EquipmentActions.EnableWPS.getValue())) {
            rpcUri = RpcConstants.EQUIPMENT_ACTION_URI;
            payloadMap.put("action", action);
            sendingTime = CommonUtils.getCurrentTimeInMillis();
            cpeRpcService.sendRpcAndExpectSucceeded(userId, equipmentId, rpcUri, method, objectMapper.writeValueAsString(payloadMap), ApplicationConstants.RPC_DEFAULT_MAX_TRIES, ApplicationConstants.RPC_DEFUALT_RETRY_INTERVAL_MILLIS);
        }

        data.put("sendingTime", sendingTime);
        dataToUpdate.put("$set", data);
        BasicDBObject query = new BasicDBObject();
        query.put("userId", userId);
        query.put("serialNumber", equipmentId);

        mongoService.update(query, dataToUpdate, false, false, AP_DETAIL);
    }

    public String postEquipment(String stn, EquipmentBase request) throws Exception {
        Equipment userEquipment = manageCommonService.getUserAPFromSubscriberIdOrApId(stn);
        String serial = request.getSerialNumber();
        if (StringUtils.isBlank(serial))
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Invalid serialNumber.");

        HashMap<String, Object> params = new HashMap<>();
        params.put("serial", serial);
        List<TrackingEquipment> trackingEquipmentList = (List<TrackingEquipment>) dataAccessService.read(TrackingEquipment.class, ActiontecSQL.GET_TRACKING_EQUIPMENT_BY_SN, params);
        if (Objects.nonNull(trackingEquipmentList) && !trackingEquipmentList.isEmpty()) {
               if (!StringUtils.equals(trackingEquipmentList.get(0).getIspId(), manageCommonService.getIspByGroupId(userEquipment.getGroupId())))
                   throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "This Network is conflicted with TrackingEquipment table.");
               else if (Objects.nonNull(trackingEquipmentList.get(0).getuId()))
                   throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "This device is onboard already!");
        }

//        BasicDBObject query = new BasicDBObject();
//        query.put("userId", new BasicDBObject("$ne", serial));
//        query.put("serialNumber", serial);
//        DBObject dbObject = at3Adapter.getMongoDbCollection(AP_DETAIL).findOne(query, new BasicDBObject().append("_id", 0));
//        if (Objects.nonNull(dbObject))
//            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "This device is onboard already.");

        HashMap<String, Object> payloadMap = new HashMap<>();
        payloadMap.put("uid", userEquipment.getRgwSerial());
        payloadMap.put("cid", at3Adapter.getIspNameByGroupId(userEquipment.getGroupId()));

        String jsonString = objectMapper.writeValueAsString(payloadMap);
        Map<String, Object> rpcResult = cpeRpcService.sendRpcAndExpectSucceeded(serial, serial, RpcConstants.HOUSEHOLD_MGR_URI, "PUT", jsonString, ApplicationConstants.RPC_DEFAULT_MAX_TRIES, ApplicationConstants.RPC_DEFUALT_RETRY_INTERVAL_MILLIS);

        if (Objects.nonNull(rpcResult)) {
            HashMap<String, Object> queryParam = new HashMap<>();
            queryParam.put("serialNumber", serial);
            queryParam.put("userId", userEquipment.getRgwSerial());
            DBObject dbObject = mongoService.findOne(AP_DETAIL, queryParam);

            if (Objects.isNull(dbObject)) {
                HashMap<String, Object> query = new HashMap<>();
                query.put("serialNumber", serial);
                query.put("userId", serial);
                DBObject apObject = mongoService.findOne(AP_DETAIL, query);

                if(Objects.isNull(apObject)) {
                    HashMap<String, Object> apDetail = new HashMap<>();
                    apDetail.put("userId", userEquipment.getRgwSerial());
                    apDetail.put("serialNumber", serial);
                    apDetail.put("macAddress", userEquipment.getRgwMAC());
                    apDetail.put("type", GATEWAY);
                    apDetail.put("isp", at3Adapter.getIspNameByGroupId(userEquipment.getGroupId()));
                    apDetail.put("processed", true);
                    apDetail.put("mysqlProcessed", true);
                    apDetail.put("esProcessed", true);
                    apDetail.put("onBoarding", true);
                    mongoService.create(AP_DETAIL, apDetail);
                } else {
                    apObject.removeField("_id");
                    apObject.removeField("userId");
                    apObject.put("userId", userEquipment.getRgwSerial());

                    logger.info(apObject.toMap());
                    mongoService.create(AP_DETAIL, (HashMap<String, Object>) apObject.toMap());
                }
            } else {
                BasicDBObject updateQuery = new BasicDBObject();
                updateQuery.put("serialNumber", serial);
                updateQuery.put("userId", userEquipment.getRgwSerial());

                BasicDBObject updateField = new BasicDBObject();
                updateField.put("processed", true);
                updateField.put("mysqlProcessed", true);
                updateField.put("esProcessed", true);
                updateField.put("onBoarding", true);

                BasicDBObject update = new BasicDBObject();
                update.put("$set", updateField);

                mongoService.update(updateQuery, update, false, false, AP_DETAIL);
            }

            HashMap<String, String> equipmentProps = commonService.read(EQUIPMENT_DIAGNOSTIC_CONFIG);
            Integer maxTries = Integer.valueOf(equipmentProps.get(LONG_RPC_POLL_COUNT));

            queryParam.clear();
            queryParam.put("userId", userEquipment.getRgwSerial());
            queryParam.put("serialNumber", serial);
            queryParam.put("connectionStatus", new BasicDBObject("$exists", true));

            for (int i = 0; i < maxTries; i++) {
                Thread.sleep(1000L);

                dbObject = mongoService.findOne(AP_DETAIL, queryParam);
                if(dbObject != null) {
                    break;
                }
            }

//            BasicDBObject updateField = new BasicDBObject();
//            if (!StringUtils.isBlank(request.getName()))
//                updateField.put("friendlyName", request.getName());
//            updateField.put("userId", userEquipment.getRgwSerial());
//            updateField.put("isp", payloadMap.get("cid"));
//            updateField.put("updatedBy", CommonUtils.isEndUser()? CommonUtils.getUserIdOfLoggedInUser(): "System");
//            if (Objects.nonNull(trackingEquipmentList))
//                updateField.put("esProcessed", true);
//            BasicDBObject basicDBObjectUpdate = new BasicDBObject();
//            basicDBObjectUpdate.put("$set", updateField);
//
//            params.clear();
//            params.put("userId", serial);
//            params.put("serialNumber", serial);
//            mongoService.findAndModify(params, AP_DETAIL, null, basicDBObjectUpdate);

//            BasicDBObject updateObject = new BasicDBObject();
//            updateObject.put("userId", serial);
//            updateObject.put("serialNumber", serial);
//            updateObject.put("updatedAt", new Date());
//            updateObject.put("updatedBy", CommonUtils.isEndUser()? CommonUtils.getUserIdOfLoggedInUser(): "System");
//            at3Adapter.findAndModifyCollection(params, DELAYED_AP, null, updateObject, true);

            if (Objects.nonNull(trackingEquipmentList) && !trackingEquipmentList.isEmpty()) {
                params.clear();
                params.put("id", trackingEquipmentList.get(0).getId());
                params.put("uId", userEquipment.getRgwSerial());
                params.put("onboardAt", new Date());
                params.put("onboardBy", CommonUtils.getUserIdOfLoggedInUser());
                dataAccessService.update(TrackingEquipment.class, ActiontecSQL.UPDATE_TRACKING_EQUIPMENT_ONBOARD_INFO, params);
            }
        }
        return serial;
    }

    public void deleteEquipment(String stn, String equipmentId) throws Exception {
        Equipment userEquipment = manageCommonService.getUserAPFromSubscriberIdOrApId(stn);

        HashMap<String, Object> params = new HashMap<>();
        params.put("userId", userEquipment.getRgwSerial());
        params.put("serialNumber", equipmentId);

        HashMap<String, Object> appendableParams = new HashMap<>();
        BasicDBObject fieldsToRemove = new BasicDBObject();
        fieldsToRemove.put("_id", ApplicationConstants.ZERO);

        DBObject dbObject = mongoService.findOne(params, appendableParams, ApplicationCommonConstants.AP_DETAIL, fieldsToRemove);
        if (Objects.isNull(dbObject)) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Invalid stn and serialNumber.");
        }

        Map<String, Object> rpcResult = cpeRpcService.sendRpcAndExpectSucceeded(userEquipment.getRgwSerial(), equipmentId, RpcConstants.HOUSEHOLD_MGR_URI, "DELETE", "{}", ApplicationConstants.RPC_DEFAULT_MAX_TRIES, ApplicationConstants.RPC_DEFUALT_RETRY_INTERVAL_MILLIS);
        if (Objects.nonNull(rpcResult)) {
//            BasicDBObject query = new BasicDBObject();
//            query.put("serialNumber", equipmentId);
//            query.put("userId", userEquipment.getRgwSerial());
//
//            BasicDBObject updateField = new BasicDBObject();
//            updateField.put("onBoarding", false);
//
//            BasicDBObject update = new BasicDBObject();
//            update.put("$set", updateField);
//
//            mongoService.update(query, update, false, false, AP_DETAIL);

            BasicDBObject updateObject = new BasicDBObject();
            updateObject.put("userId", userEquipment.getRgwSerial());
            updateObject.put("serialNumber", equipmentId);
            at3Adapter.getMongoDbCollection(AP_DETAIL).remove(updateObject);
//
//            updateObject.put("updatedAt",  new Date());
//            updateObject.put("updatedBy", CommonUtils.isEndUser()? CommonUtils.getUserIdOfLoggedInUser(): "System");
//
//            at3Adapter.findAndModifyCollection(params, DELAYED_AP, null, updateObject, true);

            params.clear();
            params.put("serial", equipmentId);
            List<TrackingEquipment> trackingEquipmentList = (List<TrackingEquipment>) dataAccessService.read(TrackingEquipment.class, ActiontecSQL.GET_TRACKING_EQUIPMENT_BY_SN, params);
            if (Objects.nonNull(trackingEquipmentList) && !trackingEquipmentList.isEmpty()) {
                params.clear();
                params.put("id", trackingEquipmentList.get(0).getId());
                dataAccessService.update(TrackingEquipment.class, ActiontecSQL.CLEAR_TRACKING_EQUIPMENT_UID, params);
            }
        }
    }

    private List<EquipmentLog> getEquipmentLogs(List<RebootHistoryDto> rebootHistoryDtos) {
        List<EquipmentLog> result = new ArrayList<>();
        for(RebootHistoryDto rebootHistoryDto : rebootHistoryDtos) {
            EquipmentLogDetail equipmentLogDetail = new EquipmentLogDetail();
            equipmentLogDetail.setUptime(rebootHistoryDto.getUptime());
            equipmentLogDetail.setReason(rebootHistoryDto.getRebootReason());

            EquipmentLog equipmentLog = new EquipmentLog();
            equipmentLog.setTime(rebootHistoryDto.getTimestamp());
            equipmentLog.setType(RebootHistoryDto.EVENT);
            equipmentLog.setDetails(equipmentLogDetail);
            result.add(equipmentLog);
        }
        return result;
    }

    public List<EquipmentLog> getEquipmentLogs(String stn, String equipmentId, Long duration, String type) throws Exception {
        Equipment userEquipment = manageCommonService.getUserAPFromSubscriberIdOrApId(stn);
        manageCommonService.checkSerialNumOfEquipment(equipmentId, userEquipment);

        List<RebootHistoryDto> rebootHistoryDtoList = rebootHistoryDao.listRebootHistoryByUserIdAndSerial(userEquipment.getRgwSerial(), equipmentId, duration);

        List<EquipmentLog> result = new ArrayList<>();

        if(type.equals(ApplicationCommonConstants.EMPTY_STRING)) {
            result = getEquipmentLogs(rebootHistoryDtoList);
        } else {
            List<String> types = new ArrayList<String>(Arrays.asList(type.split(",")));
            if(types.contains(RebootHistoryDto.EVENT)) {
                result = getEquipmentLogs(rebootHistoryDtoList);
            }
        }
        return result;
    }

    public List<EquipmentStats> getEquipmentStats(String stn, String equipmentId, Long duration) throws Exception {
        Equipment userEquipment = manageCommonService.getUserAPFromSubscriberIdOrApId(stn);
        manageCommonService.checkSerialNumOfEquipment(equipmentId, userEquipment);

        List<EquipmentStats> equipmentStatsList = new ArrayList<>();

        List<EquipmentStatsDto> equipmentStatsDtoList = equipmentStatsDao.listEquipmentStatsByUserIdAndSerial(userEquipment.getRgwSerial(), equipmentId, duration);
        for(EquipmentStatsDto equipmentStatsDto : equipmentStatsDtoList) {
            EquipmentStats equipmentStats = new EquipmentStats();
            equipmentStats.setTimestamp(equipmentStatsDto.getTimestamp());
            equipmentStats.setCpuUsage(equipmentStatsDto.getCpuUsage());
            equipmentStats.setMemoryUsage(equipmentStatsDto.getMemUsage());
            equipmentStatsList.add(equipmentStats);
        }

        return equipmentStatsList;
    }

    public Long getEquipmentCountByGroupId(String groupId) throws Exception {

        HashMap<String, Object> params = new HashMap<>();
        params.put("groupId", groupId);

        return Long.valueOf(dataAccessService.read(Equipment.class, GET_EQUIPMENT_COUNT_BY_COMPARTMENT, params).iterator().next().toString());
    }
}
