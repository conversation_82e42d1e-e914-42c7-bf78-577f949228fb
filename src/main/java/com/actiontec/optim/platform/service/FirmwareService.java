package com.actiontec.optim.platform.service;

import com.actiontec.optim.platform.adapter.At3Adapter;
import com.actiontec.optim.platform.annotation.PermissionHandle;
import com.actiontec.optim.platform.api.v5.model.FirmwareActionRequest;
import com.actiontec.optim.platform.constant.ActiontecSQL;
import com.actiontec.optim.platform.exception.RpcException;
import com.actiontec.optim.platform.mapper.FwMapper;
import com.actiontec.optim.platform.model.firmware.FwInfo;
import com.actiontec.optim.platform.model.firmware.FwLog;
import com.actiontec.optim.platform.model.firmware.ResponsePayload;
import com.actiontec.optim.rpc.core.RpcResponse;
import com.actiontec.optim.rpc.facade.RpcServiceFacade;
import com.actiontec.optim.service.CpeRpcService;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.incs83.app.constants.misc.ActiontecConstants;
import com.incs83.mt.DataAccessService;
import com.incs83.service.CommonService;
import com.incs83.util.CommonUtils;
import com.mongodb.BasicDBObject;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

import static com.incs83.app.constants.misc.ActiontecConstants.EQUIPMENT_DIAGNOSTIC_CONFIG;
import static com.incs83.app.constants.misc.ActiontecConstants.RPC_POLL_COUNT;
import static com.incs83.app.constants.misc.RpcConstants.CPE_API_METHOD_GET;
import static com.incs83.app.constants.misc.RpcConstants.CPE_API_METHOD_POST;

@Service
public class FirmwareService {
    private final Logger logger = LogManager.getLogger(this.getClass());
    @Autowired
    private FwMapper fwMapper;
    @Autowired
    private FwImageService fwImageService;
    @Autowired
    private CpeRpcService cpeRpcService;
    @Autowired
    private At3Adapter at3Adapter;
    @Autowired
    private DataAccessService dataAccessService;
    @Autowired
    private ObjectMapper objectMapper;
    @Autowired
    private CommonService commonService;
    @Autowired
    private RpcServiceFacade rpcServiceFacade;

    private static final String FW_URL = "/cpe-acs/firmwares";
    private static final String Fw_ACTIONS_URL = "/cpe-acs/firmwares/actions";

    private int getRpcTimeout() {
        HashMap<String, String> equipmentProps = commonService.read(EQUIPMENT_DIAGNOSTIC_CONFIG);
        return Integer.parseInt(equipmentProps.get(RPC_POLL_COUNT));
    }

    @PermissionHandle
    public List<FwInfo> getFirmwares(String stn, String equipmentId) throws InterruptedException {
        List<FwInfo> fwInfoList = new LinkedList<>();
        String networkId = at3Adapter.getRgwSerialByStn(stn);
        int RPC_TIMEOUT_SECONDS = getRpcTimeout();

        try {
            CompletableFuture<RpcResponse> future = rpcServiceFacade.sendFirmwaresRequest(networkId, equipmentId, RPC_TIMEOUT_SECONDS);

            RpcResponse rpcResponse = future.get(RPC_TIMEOUT_SECONDS, TimeUnit.SECONDS);

            Map<String, Object> data = rpcResponse.getData();
            int rpcRetCode = Integer.parseInt(data.get("code").toString());
            if (rpcRetCode != 200) {
                throw new RpcException(HttpStatus.BAD_REQUEST.value(), "RPC returned error code: " + rpcRetCode);
            }

            if (data.get("payload") != null) {
                List<HashMap<String, String>> payloadList = objectMapper.convertValue(data.get("payload"), new TypeReference<List<HashMap<String, String>>>(){});
                String activeVersion = String.valueOf(payloadList.get(0).get("activeVersion"));
                String backupVersion = String.valueOf(payloadList.get(0).get("backupVersion"));

                if (StringUtils.isNotEmpty(activeVersion))
                    fwInfoList.add(new FwInfo(activeVersion, true));
                if (StringUtils.isNotEmpty(backupVersion))
                    fwInfoList.add(new FwInfo(backupVersion, false));
            }

            return fwInfoList;

        } catch (ExecutionException e) {
            Throwable cause = e.getCause();
            if (cause instanceof TimeoutException) {
                logger.error("Firmwares API timeout for network:[{}]", networkId);
                throw new RpcException(HttpStatus.REQUEST_TIMEOUT.value(), "Request timeout");
            } else {
                logger.error("Firmwares API execution failed for network:[{}]", networkId);
                throw new RpcException(HttpStatus.BAD_REQUEST.value(), e.getMessage());
            }
        } catch (Exception e) {
            logger.error("Firmwares API error for network:[{}]", networkId);
            throw new RpcException(HttpStatus.BAD_REQUEST.value(), "Unexpected error");
        }
    }

    @PermissionHandle
    public List<FwLog> getFirmwareLogs(String stn, String equipmentId, String actionId) throws InterruptedException {
        List<FwLog> fwLogList = new LinkedList<>();
        String networkId = at3Adapter.getRgwSerialByStn(stn);
        int RPC_TIMEOUT_SECONDS = getRpcTimeout();

        try {
            CompletableFuture<RpcResponse> future = rpcServiceFacade.sendFwActionRequest(networkId, equipmentId,
                    CPE_API_METHOD_GET, actionId, null, RPC_TIMEOUT_SECONDS);

            RpcResponse rpcResponse = future.get(RPC_TIMEOUT_SECONDS, TimeUnit.SECONDS);

            Map<String, Object> data = rpcResponse.getData();
            int rpcRetCode = Integer.parseInt(data.get("code").toString());
            if (rpcRetCode != 200) {
                throw new RpcException(HttpStatus.BAD_REQUEST.value(), "RPC returned error code: " + rpcRetCode);
            }

            if (data.get("payload") != null) {
                List<ResponsePayload> responsePayloadList = objectMapper.convertValue(data.get("payload"), at3Adapter.getCollectionType(List.class, ResponsePayload.class));
                responsePayloadList.forEach(p -> fwLogList.add(fwMapper.toFwlog(p)));
                fwLogList.forEach(p -> {
                    String ver = "";
                    if (StringUtils.isNotEmpty(p.getFileEntry())) {
                        HashMap<String, Object> param = new HashMap<>();
                        param.put("secureUrl", p.getFileEntry());
                        try {
                            ver = dataAccessService.readNative(ActiontecSQL.GET_VERSION_FROM_FILE, param).iterator().next().toString();
                        } catch (Exception e) {
                            logger.error("get version error from [{}]", p.getFileEntry(), e);
                        }
                    }
                    p.setVersion(ver);
                });
            }

            return fwLogList;

        } catch (ExecutionException e) {
            Throwable cause = e.getCause();
            if (cause instanceof TimeoutException) {
                logger.error("Firmware action API timeout for network:[{}]", networkId);
                throw new RpcException(HttpStatus.REQUEST_TIMEOUT.value(), "Request timeout");
            } else {
                logger.error("Firmware action API execution failed for network:[{}]", networkId);
                throw new RpcException(HttpStatus.BAD_REQUEST.value(), e.getMessage());
            }
        } catch (Exception e) {
            logger.error("Firmware action API error for network:[{}]", networkId);
            throw new RpcException(HttpStatus.BAD_REQUEST.value(), "Unexpected error");
        }
    }

    @PermissionHandle
    public Map<String, String> postAction(String stn, String equipmentId, FirmwareActionRequest actionRequest) throws Exception {
        Map<String, String> retVal = new HashMap<>();
        String networkId = at3Adapter.getRgwSerialByStn(stn);
        String actionId = CommonUtils.getCurrentTimeInMillis() / 1000 + "-" + CommonUtils.generateUUID();
        int RPC_TIMEOUT_SECONDS = getRpcTimeout();

        try {
            CompletableFuture<RpcResponse> future = rpcServiceFacade.sendFwActionRequest(networkId, equipmentId,
                    CPE_API_METHOD_POST, actionId, actionRequest, RPC_TIMEOUT_SECONDS);

            RpcResponse rpcResponse = future.get(RPC_TIMEOUT_SECONDS, TimeUnit.SECONDS);

            Map<String, Object> data = rpcResponse.getData();
            int rpcRetCode = Integer.parseInt(data.get("code").toString());
            if (rpcRetCode != 200) {
                throw new RpcException(HttpStatus.BAD_REQUEST.value(), "RPC returned error code: " + rpcRetCode);
            }

            BasicDBObject query = new BasicDBObject();
            query.put("userId", networkId);
            query.put("serialNumber", equipmentId);
            BasicDBObject updateFields = new BasicDBObject();
            updateFields.put("connectionStatus", "offline");
            BasicDBObject updatedDBObject = new BasicDBObject();
            updatedDBObject.put("$set", updateFields);
            at3Adapter.findAndModifyCollection(query, ActiontecConstants.AP_DETAIL, null, updatedDBObject, false);

            retVal.put("id", actionId);
            return retVal;

        } catch (ExecutionException e) {
            Throwable cause = e.getCause();
            if (cause instanceof TimeoutException) {
                logger.error("Firmware action API timeout for network:[{}]", networkId);
                throw new RpcException(HttpStatus.REQUEST_TIMEOUT.value(), "Request timeout");
            } else {
                logger.error("Firmware action API execution failed for network:[{}]", networkId);
                throw new RpcException(HttpStatus.BAD_REQUEST.value(), e.getMessage());
            }
        } catch (Exception e) {
            logger.error("Firmware action API error for network:[{}]", networkId);
            throw new RpcException(HttpStatus.BAD_REQUEST.value(), "Unexpected error");
        }
    }
}
