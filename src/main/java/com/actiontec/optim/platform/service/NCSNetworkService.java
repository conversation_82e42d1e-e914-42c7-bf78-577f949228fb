package com.actiontec.optim.platform.service;

import com.actiontec.optim.mongodb.dao.ApCapabilityDao;
import com.actiontec.optim.mongodb.dao.ApDetailDao;
import com.actiontec.optim.mongodb.dao.HostDao;
import com.actiontec.optim.mongodb.dto.HostDto;
import static com.actiontec.optim.platform.constant.ApplicationConstants.STRING_EMPTY;
import static com.incs83.app.constants.misc.ActiontecConstants.EQUIPMENT_DIAGNOSTIC_CONFIG;
import static com.incs83.app.constants.misc.ActiontecConstants.HOSTNAME_DETAIL;
import static com.incs83.app.constants.misc.ActiontecConstants.RPC_POLL_COUNT;

import com.actiontec.optim.platform.api.v6.dto.*;
import com.actiontec.optim.platform.api.v6.enums.DeviceAction;
import com.actiontec.optim.platform.api.v6.enums.NetworkClaimStatus;
import com.actiontec.optim.platform.api.v6.mapper.ProxyCpeApiResponseMapper;
import com.actiontec.optim.platform.api.v6.mapper.ShadowServiceWifiMapper;
import com.actiontec.optim.platform.constant.ProvisionConstants;
import com.actiontec.optim.platform.exception.RpcException;
import com.actiontec.optim.platform.model.EquipmentModel;
import com.actiontec.optim.platform.repository.*;
import com.actiontec.optim.rpc.core.RpcResponse;
import com.actiontec.optim.rpc.facade.RpcServiceFacade;
import com.actiontec.optim.service.CpeRpcService;
import com.actiontec.optim.util.AppCloudJwtTokenUtils;
import com.actiontec.optim.util.CustomStringUtils;
import com.actiontec.optim.util.MongoDbUtils;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.incs83.app.business.v2.ManageCommonService;
import com.incs83.app.constants.misc.ActiontecConstants;
import com.incs83.app.constants.misc.ApplicationConstants;
import com.incs83.app.constants.misc.RpcConstants;
import com.incs83.app.entities.*;
import com.incs83.app.service.data.MongoServiceImpl;
import com.incs83.exceptions.handler.ValidationException;
import com.incs83.security.auth.jwt.extractor.TokenExtractor;
import com.incs83.service.CommonService;
import com.incs83.util.CommonUtils;
import com.mongodb.BasicDBObject;
import com.mongodb.DBObject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.RequestMethod;

import java.time.Instant;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;

@Service
public class NCSNetworkService {
    private final Logger logger = LogManager.getLogger(this.getClass());

    @Autowired
    private ManageCommonService manageCommonService;

    @Autowired
    NCSIspService ispService;

    @Autowired
    NCSGroupService ncsGroupService;

    @Autowired
    NCSEquipmentService ncsEquipmentService;

    @Autowired
    private NCSNetworkRepo networkRepo;

    @Autowired
    private SubscriberNetworkRepo subscriberNetworkRepo;

    @Autowired
    private ClusterNetworkRepo clusterNetworkRepo;

    @Autowired
    private CommonService commonService;

    @Autowired
    private MongoServiceImpl mongoService;

    @Autowired
    private HostDao hostDao;

    @Autowired
    private CpeRpcService cpeRpcService;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private RpcServiceFacade rpcServiceFacade;

    @Autowired
    ProxyCpeApiResponseMapper proxyCpeApiResponseMapper;

    @Autowired
    private AppCloudService appCloudService;

    @Autowired
    private TokenExtractor tokenExtractor;

    @Autowired
    private EquipmentModelRepo equipmentModelRepo;

    @Autowired
    private FileRepo fileRepo;

    @Autowired
    private ProvisionService provisionService;

    @Autowired
    private ShadowServiceWifiMapper shadowServiceWifiMapper;

    @Autowired
    private ApCapabilityDao apCapabilityDao;

    @Autowired
    private ApDetailDao apDetailDao;

    // IAM network api
    public PaginationResponse<NCSNetworkDTO> getAllNetworks(NCSNetworkQueryDTO queryDTO) throws Exception {
        return networkRepo.getAllNetwork(queryDTO);
    }

    public Map<String, String> createNetwork(NCSNetworkRequest networkRequest) throws Exception {
        Map<String, String> result = new HashMap<>();
        String ispId = networkRequest.getIspId();
        String groupId = CommonUtils.getGroupIdOfLoggedInUser();
        String defaultClusterId;
        String subscriberId = networkRequest.getSubscriberId();
        String networkId;

        if (CommonUtils.isEndUser()) {
            throw new ValidationException(HttpStatus.FORBIDDEN.value(),
                    "Insufficient permissions to access this resource");
        } else if (CommonUtils.isGroupAdmin()) {
            String userIspId = manageCommonService.getIspByGroupId(groupId);
            if (!StringUtils.equalsIgnoreCase(ispId, userIspId)) {
                throw new ValidationException(HttpStatus.FORBIDDEN.value(),
                        "Insufficient permissions to access this resource");
            }
        }

        defaultClusterId = ncsGroupService.getDefaultClusterIdByIspId(ispId);

        // step 1: check ispId is existed
        String ispName = ispService.getIspNameById(ispId);
        if (StringUtils.isEmpty(ispName)) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "The isp doesn't exist, please create isp.");
        }

        // step 2: create network data
        networkId = networkRepo.createNetwork(networkRequest);

        if (StringUtils.isEmpty(networkId)) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Create network failed");
        }

        // step 3: check equipment is under this isp
        if (CollectionUtils.isNotEmpty(networkRequest.getEquipmentSerialList())) {
            List<NCSEquipment> equipmentList = ncsEquipmentService
                    .getNCSEquipmentListBySerialList(networkRequest.getEquipmentSerialList());
            if (equipmentList.size() != networkRequest.getEquipmentSerialList().size()) {
                networkRepo.deleteNetwork(networkId);
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(),
                        "Some equipment does not exist, please check equipment.");
            }

            List<String> equipmentIdRequest = equipmentList.stream()
                    .map(NCSEquipment::getId)
                    .collect(Collectors.toList());

            if (!ncsEquipmentService.checkEquipmentListByIspId(ispId, equipmentIdRequest)) {
                networkRepo.deleteNetwork(networkId);
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(),
                        "Some equipment is not under ispId: " + ispId + ", please check equipment.");
            }

            // step 4: update ncs_equipment networkId and bind to subscriber
            // check all equipments are not bound by other network
            List<NCSEquipment> ispUnbindEquipmentList = equipmentList.stream()
                    .filter(equipment -> StringUtils.equals(equipment.getIspId(), ispId)
                            && StringUtils.isEmpty(equipment.getNetworkId()))
                    .collect(Collectors.toList());
            if (equipmentList.size() != ispUnbindEquipmentList.size()) {
                networkRepo.deleteNetwork(networkId);
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(),
                        "Some equipment is under other network, please check equipment");
            }
            ncsEquipmentService.updateNetworkAndSubscriberByIdList(ispId, equipmentIdRequest, networkId, subscriberId);
        }

        // step 5: create subscriber_network data
        subscriberNetworkRepo.create(subscriberId, networkId);

        // step 6: create cluster_network data
        clusterNetworkRepo.create(defaultClusterId, networkId);

        result.put("networkId", networkId);

        return result;
    }

    public NCSNetworkDetailDTO getNetworkDetailDTO(String networkId) throws Exception {
        NCSNetworkDetailDTO networkDetailDTO = null;
        if (CommonUtils.isEndUser()) {
            throw new ValidationException(HttpStatus.FORBIDDEN.value(),
                    "Insufficient permissions to access this resource");
        } else if (CommonUtils.isGroupAdmin()) {
            String userIspId = manageCommonService.getIspByGroupId(CommonUtils.getGroupIdOfLoggedInUser());
            networkDetailDTO = networkRepo.getNetworkDetailDTOById(networkId);

            if (StringUtils.isEmpty(networkDetailDTO.getIspId())
                    || !StringUtils.equals(networkDetailDTO.getIspId(), userIspId)) {
                throw new ValidationException(HttpStatus.FORBIDDEN.value(),
                        "Insufficient permissions to access this resource");
            }
        }

        if (ObjectUtils.isEmpty(networkDetailDTO)) {
            networkDetailDTO = networkRepo.getNetworkDetailDTOById(networkId);
        }

        // prepare network equipment serial list
        List<String> equipmentSerialList = ncsEquipmentService.getNCSEquipmentListByNetworkId(networkId)
                .stream()
                .map(NCSEquipment::getSerial)
                .collect(Collectors.toList());
        networkDetailDTO.setEquipmentSerialList(equipmentSerialList);

        return networkDetailDTO;
    }

    public void updateNetwork(String networkId, NCSNetworkRequest networkRequest) throws Exception {
        NCSNetworkDetailDTO networkDetailDTO = null;
        String ispId = networkRequest.getIspId();
        String subscriberId = networkRequest.getSubscriberId();
        if (CommonUtils.isEndUser()) {
            throw new ValidationException(HttpStatus.FORBIDDEN.value(),
                    "Insufficient permissions to access this resource");
        } else if (CommonUtils.isGroupAdmin()) {
            String userIspId = manageCommonService.getIspByGroupId(CommonUtils.getGroupIdOfLoggedInUser());
            if (!StringUtils.equals(ispId, userIspId)) {
                throw new ValidationException(HttpStatus.FORBIDDEN.value(),
                        "Insufficient permissions to access this resource");
            }
        }

        // step 1: check ispId is existed
        String ispName = ispService.getIspNameById(ispId);
        if (StringUtils.isEmpty(ispName)) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "The isp doesn't exist, please create isp.");
        }

        // step 2: check subscriber_network mapping
        List<SubscriberNetwork> oldSubscriberNetworks = subscriberNetworkRepo
                .getSubscriberNetworkByNetworkId(networkId);
        if (CollectionUtils.isEmpty(oldSubscriberNetworks)) {
            // create subscriber_network data
            subscriberNetworkRepo.create(subscriberId, networkId);
        } else if (CollectionUtils.isNotEmpty(oldSubscriberNetworks) &&
                !StringUtils.equalsIgnoreCase(oldSubscriberNetworks.get(0).getSubscriberId(), subscriberId)) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "The network belongs to another subscriber.");
        }

        // step 3: check equipment is under this isp
        if (CollectionUtils.isNotEmpty(networkRequest.getEquipmentSerialList())) {
            List<NCSEquipment> equipmentList = ncsEquipmentService
                    .getNCSEquipmentListBySerialList(networkRequest.getEquipmentSerialList());
            List<String> equipmentIdRequest = equipmentList.stream().map(NCSEquipment::getId)
                    .collect(Collectors.toList());

            if (!ncsEquipmentService.checkEquipmentListByIspId(ispId, equipmentIdRequest)) {
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(),
                        "Some equipment is not under ispId: " + ispId + ", please check equipment.");
            }

            // step 4: clear ncs_equipment network mapping, just clear networkId
            ncsEquipmentService.clearNetworkMappingByNetworkId(networkId);

            // step 5: update ncs_equipment networkId and bind to subscriber
            // check all equipments are not bound by other network
            List<NCSEquipment> otherNetworkEquipmentList = equipmentList.stream()
                    .filter(equipment -> StringUtils.equals(equipment.getIspId(), ispId)
                            && StringUtils.isNotEmpty(equipment.getNetworkId())
                            && !StringUtils.equals(equipment.getNetworkId(), networkId))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(otherNetworkEquipmentList)) {
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(),
                        "Some equipment is under other network, please check equipment");
            }
            ncsEquipmentService.clearNetworkMappingByNetworkId(networkId);
            ncsEquipmentService.updateNetworkAndSubscriberByIdList(ispId, equipmentIdRequest, networkId, subscriberId);
        } else {
            // clear all equipment mapping
            ncsEquipmentService.clearNetworkMappingByNetworkId(networkId);
        }

        // step 7: update network name
        networkRepo.updateNetworkName(networkId, networkRequest, CommonUtils.getUserIdOfLoggedInUser());
    }

    public void deleteNetwork(String networkId) throws Exception {
        NCSNetworkDetailDTO networkDetailDTO = null;
        if (CommonUtils.isEndUser()) {
            throw new ValidationException(HttpStatus.FORBIDDEN.value(),
                    "Insufficient permissions to access this resource");
        } else if (CommonUtils.isGroupAdmin()) {
            String userIspId = manageCommonService.getIspByGroupId(CommonUtils.getGroupIdOfLoggedInUser());
            networkDetailDTO = networkRepo.getNetworkDetailDTOById(networkId);

            if (StringUtils.isEmpty(networkDetailDTO.getIspId())
                    || !StringUtils.equals(networkDetailDTO.getIspId(), userIspId)) {
                throw new ValidationException(HttpStatus.FORBIDDEN.value(),
                        "Insufficient permissions to access this resource");
            }
        }

        // clear subscriber_network mapping
        List<SubscriberNetwork> subscriberNetworks = subscriberNetworkRepo.getSubscriberNetworkByNetworkId(networkId);
        String subscriberId = null;
        if (CollectionUtils.isNotEmpty(subscriberNetworks)) {
            subscriberId = subscriberNetworks.get(0).getSubscriberId();
            subscriberNetworkRepo.deleteByNetworkId(networkId);

            // reset subscriberId in ncs_equipment
            ncsEquipmentService.clearSubscriberMappingBySubscriberId(subscriberId);
        }

        // clear ncs_equipment mapping, just clear networkId
        ncsEquipmentService.clearNetworkMappingByNetworkId(networkId);

        // delete cluster_network mapping
        clusterNetworkRepo.deleteByNetworkId(networkId);

        networkRepo.deleteNetwork(networkId);
    }

    public void checkQueryParameter(NCSNetworkQueryDTO queryDTO) throws Exception {
        if (StringUtils.isNotEmpty(queryDTO.getIspId())) {
            ispService.checkIspIdPresent(queryDTO.getIspId());
        }

        checkQuerySubscriber(queryDTO);
    }

    private void checkQuerySubscriber(NCSNetworkQueryDTO queryDTO) throws Exception {
        if (StringUtils.isNotEmpty(queryDTO.getSubscriberId())) {
            List<SubscriberNetwork> subscriberNetworkList = subscriberNetworkRepo
                    .getSubscriberNetworkBySubscriberId(queryDTO.getSubscriberId());
            if (CollectionUtils.isEmpty(subscriberNetworkList)) {
                throw new ValidationException(HttpStatus.FORBIDDEN.value(),
                        "Query subscriberId does not have any networks associated with it.");
            }
        }
    }

    // network api
    public CpeApiResponse getCpeApiProxy(String networkId, CpeApiRequest proxyRequest, String extenderSerial)
            throws Exception {
        NCSNetworkDetailDTO networkDetailDTO = getNetworkDetailDTO(networkId);

        if (CollectionUtils.isEmpty(networkDetailDTO.getEquipmentSerialList())) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Current network cannot find equipment.");
        }

        // some function need to send rpc to extender serial
        String serialNumber;
        if (CustomStringUtils.isNotEmpty(extenderSerial)) {
            serialNumber = extenderSerial;
        } else if (networkDetailDTO.getEquipmentSerialList().size() > 1) {
            // find gateway serial
            serialNumber = manageCommonService.getGatewaySerialByUserId(networkId).orElse(STRING_EMPTY);
            if (CustomStringUtils.isEmpty(serialNumber)) {
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(),
                        "Current network cannot find gateway serial, networkId: " + networkId);
            }
        } else {
            // find gateway serial
            serialNumber = networkDetailDTO.getEquipmentSerialList().get(0);
        }

        HashMap<String, String> equipmentProps = commonService.read(EQUIPMENT_DIAGNOSTIC_CONFIG);
        int RPC_TIMEOUT_SECONDS = Integer.parseInt(equipmentProps.get(RPC_POLL_COUNT));
        try {
            CompletableFuture<RpcResponse> future = rpcServiceFacade.sendCpeApiRequest(
                    networkId, serialNumber, proxyRequest, RPC_TIMEOUT_SECONDS);

            RpcResponse rpcResponse = future.get(RPC_TIMEOUT_SECONDS, TimeUnit.SECONDS);

            if (!rpcResponse.isSuccess()) {
                throw new RpcException(HttpStatus.BAD_REQUEST.value(), rpcResponse.getErrorMessage());
            }

            Map<String, Object> data = rpcResponse.getData();
            int rpcRetCode = Integer.parseInt(data.get("code").toString());
            if (rpcRetCode != 200) {
                throw new RpcException(HttpStatus.BAD_REQUEST.value(), "RPC returned error code: " + rpcRetCode);
            }

            String payload = objectMapper.writeValueAsString(data);
            logger.debug("CPE API success: {}", payload);
            return proxyCpeApiResponseMapper.parseProxyResult(payload);

        } catch (ExecutionException e) {
            Throwable cause = e.getCause();
            if (cause instanceof TimeoutException) {
                logger.error("CPE API timeout for network:[{}]", networkId);
                throw new RpcException(HttpStatus.REQUEST_TIMEOUT.value(), "Request timeout");
            } else {
                logger.error("CPE API execution failed for network:[{}]", networkId);
                throw new RpcException(HttpStatus.BAD_REQUEST.value(), e.getMessage());
            }
        } catch (Exception e) {
            logger.error("CPE API error for network:[{}]", networkId);
            throw new RpcException(HttpStatus.BAD_REQUEST.value(), "Unexpected error");
        }
    }

    // send request and create mongo record.
    // private void sendCpeApiRequestToRpc30(String tid, String networkId, String
    // serial, CpeApiRequest proxyRequest) throws Exception {
    // String rpcUri = NCS_NETWORK_CPE_URI;
    // HashMap<String, Object> payloadHashMap = new HashMap<>();
    // String payloadJsonString;
    //
    // payloadHashMap.put("paths", proxyRequest.getEndpoints());
    // payloadJsonString = objectMapper.writeValueAsString(payloadHashMap);
    // logger.info("cpe api payload: {}", payloadJsonString);
    //
    // HashMap<String, String> equipmentProps =
    // commonService.read(EQUIPMENT_DIAGNOSTIC_CONFIG);
    // Integer maxTries = Integer.valueOf(equipmentProps.get(RPC_POLL_COUNT));
    //
    // cpeRpcService.sendRpc(networkId, serial, rpcUri, CPE_API_METHOD,
    // payloadJsonString, tid);
    //
    // Map<String, Object> rpcResponseResult = null;
    // HashMap<String, Object> proxyResultMap = new HashMap<>();
    // proxyResultMap.put("_id", tid);
    // proxyResultMap.put("networkId", networkId);
    // proxyResultMap.put("serial", serial);
    // proxyResultMap.put("payload", payloadJsonString);
    // proxyResultMap.put("rpcVer", "3.0");
    // proxyResultMap.put("timestamp", Instant.now().toEpochMilli());
    // proxyResultMap.put("dateCreated", new Date());
    // proxyResultMap.put("rpcUri", rpcUri);
    // proxyResultMap.put("rawResponse", STRING_EMPTY);
    //
    // for (int i = 0; i < maxTries; i++) {
    // try {
    // Thread.sleep(THREAD_TO_SLEEP);
    // } catch (InterruptedException e) {
    // logger.error("waitingRpcResult interrupted. tid:[ " + tid + "]");
    // throw e;
    // }
    //
    // rpcResponseResult = cpeRpcService.readRpcResult(tid);
    // if (MapUtils.isNotEmpty(rpcResponseResult)) {
    // proxyResultMap.put("rawResponse", rpcResponseResult);
    // proxyResultMap.put("result", EXECUTE_OK);
    // mongoService.create(TOPOLOGY_PROXY_RESULT, proxyResultMap);
    // break;
    // }
    // }
    //
    // if (MapUtils.isEmpty(rpcResponseResult)) {
    // logger.error("rpcRequest timeout, serial: {} tid: {}", serial, tid);
    // proxyResultMap.put("result", TIME_OUT.toUpperCase());
    // mongoService.create(TOPOLOGY_PROXY_RESULT, proxyResultMap);
    // throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Request Timed
    // Out, please try again after sometime.");
    // }
    // }

    public PaginationResponse<NetworkOperationsDTO> getNetworksByKeyword(NetworkOperationsQueryDTO queryDTO)
            throws Exception {
        if (CustomStringUtils.isEmpty(queryDTO.getKeyword())) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "keyword cannot be empty or blank");
        }

        String ispId = null;
        if (CommonUtils.isGroupAdmin()) {
            ispId = manageCommonService.getIspByGroupId(CommonUtils.getGroupIdOfLoggedInUser());
        }

        return networkRepo.getNetworksByKeyword(queryDTO, ispId);
    }

    public NCSNetwork getNetworkById(String networkId) throws Exception {
        return networkRepo.getNetworkById(networkId);
    }

    // app cloud methods
    public NetworkOperationsForAppCloudDTO getNetworkForAppCloud(String networkId) throws Exception {
        NCSNetwork network = getNetworkById(networkId);

        // prepare network equipment serial list
        List<String> equipmentSerialList = ncsEquipmentService.getNCSEquipmentListByNetworkId(networkId)
                .stream()
                .map(NCSEquipment::getSerial)
                .collect(Collectors.toList());

        String ispName = ispService.getIspNameById(network.getIspId());

        return NetworkOperationsForAppCloudDTO.of(network, equipmentSerialList, ispName);
    }

    public NetworkClaimResponse claimNetwork(String serialNumber, String appCloudUserId, String accessToken)
            throws Exception {
        // 1. Extract token and kid from X-Authorization header
        String tokenValue = tokenExtractor.extract(accessToken);
        logger.info("token value: {}", tokenValue);
        String kid = AppCloudJwtTokenUtils.extractKid(tokenValue);
        logger.info("kid: {}", kid);

        if (CustomStringUtils.isEmpty(kid)) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Missing kid in JWT header.");
        }

        // 2. Find AppCloudConfig by kid
        AppCloudConfig appCloudConfig = appCloudService.getAppCloudConfigByKid(kid);

        // 3. Find NCSEquipment by serialNumber
        List<NCSEquipment> equipmentList = ncsEquipmentService
                .getNCSEquipmentListBySerialList(Collections.singletonList(serialNumber));
        if (CollectionUtils.isEmpty(equipmentList)) {
            throw new ValidationException(HttpStatus.NOT_FOUND.value(), "Serial number not found");
        }
        NCSEquipment ncsEquipment = equipmentList.get(0);

        // 4. Find the corresponding network from equipment
        if (CustomStringUtils.isEmpty(ncsEquipment.getNetworkId())) {
            throw new ValidationException(HttpStatus.CONFLICT.value(), "Corresponding network not found");
        }

        NCSNetwork network = networkRepo.getNetworkById(ncsEquipment.getNetworkId());
        if (ObjectUtils.isEmpty(network)) {
            throw new ValidationException(HttpStatus.CONFLICT.value(), "Corresponding network not found");
        }

        // 5. Check NetworkClaimStatus
        if (NetworkClaimStatus.CLAIMED.equals(network.getClaimStatus())) {
            throw new ValidationException(HttpStatus.FORBIDDEN.value(), "Network already claimed");
        }

        // Check PRECLAIMED status within 30 seconds
        if (NetworkClaimStatus.PRECLAIMED.equals(network.getClaimStatus()) &&
                network.getUpdatedAt() != null &&
                network.getUpdatedAt().plusSeconds(30).isAfter(LocalDateTime.now())) {
            throw new ValidationException(HttpStatus.FORBIDDEN.value(), "Network is claiming, please wait.");
        }

        // 6. Update network status to PRECLAIMED before sending RPC request
        networkRepo.updateNetworkClaimStatus(network.getId(), NetworkClaimStatus.PRECLAIMED);

        // 7. Send RPC request
        Boolean isConfigured = false;
        CpeApiRequest proxyRequest = new CpeApiRequest();
        CpeApiResponse cpeApiResponse;

        HashMap<String, String> equipmentProps = commonService.read(EQUIPMENT_DIAGNOSTIC_CONFIG);
        int RPC_TIMEOUT_SECONDS = Integer.parseInt(equipmentProps.get(RPC_POLL_COUNT));
        String networkId = network.getId();

        try {
            CompletableFuture<RpcResponse> future = rpcServiceFacade.sendEquipmentBlinkRequest(
                    networkId, serialNumber, new HashMap<>(), RPC_TIMEOUT_SECONDS);

            RpcResponse rpcResponse = future.get(RPC_TIMEOUT_SECONDS, TimeUnit.SECONDS);

            Map<String, Object> data = rpcResponse.getData();
            int rpcReturnCode = Integer.parseInt(data.get("code").toString());

            if (rpcReturnCode != 200) {
                throw new RpcException(HttpStatus.BAD_REQUEST.value(), "RPC returned error code: " + rpcReturnCode);
            }

            if (!ObjectUtils.isEmpty(data.get("payload"))
                    && CustomStringUtils.isNotEmpty(data.get("payload").toString())) {
                List<HashMap<String, Object>> payloadList = objectMapper.convertValue(data.get("payload"), List.class);
                if (CustomStringUtils.isNotEmpty(payloadList.get(0).get("isConfigured").toString())) {
                    logger.debug("rpc result isConfigured: {}", payloadList.get(0).get("isConfigured"));
                    isConfigured = CustomStringUtils.parseBoolean(payloadList.get(0).get("isConfigured").toString());
                }
            }
        } catch (ExecutionException e) {
            Throwable cause = e.getCause();
            if (cause instanceof TimeoutException) {
                logger.error("Equipment Blink API timeout for network:[{}]", networkId);
                throw new RpcException(HttpStatus.REQUEST_TIMEOUT.value(), "Request timeout");
            } else {
                logger.error("Equipment Blink API execution failed for network:[{}]", networkId);
                throw new RpcException(HttpStatus.BAD_REQUEST.value(), e.getMessage());
            }
        } catch (Exception e) {
            logger.error("Equipment Blink API error for network:{}, message: {}", networkId, e.getMessage());
            throw new RpcException(HttpStatus.BAD_REQUEST.value(), "Unexpected error");
        }

        // 8. Handle successful result
        // Generate new networkTokenId
        String networkTokenId = CommonUtils.generateUUID();

        // Update network: claimStatus = CLAIMED, networkTokenId, appCloudConfig,
        // appCloudUserId
        networkRepo.updateNetworkClaimStatus(network.getId(), NetworkClaimStatus.CLAIMED);
        networkRepo.updateNetworkCloudConfig(network.getId(), networkTokenId, appCloudConfig.getId(), appCloudUserId);

        // Assemble response
        NetworkClaimResponse.IspInfo ispInfo = getIspInfo(ncsEquipment.getModelName(), ncsEquipment.getIspId());

        return new NetworkClaimResponse(network.getId(), isConfigured, networkTokenId, ispInfo);
    }

    private NetworkClaimResponse.IspInfo getIspInfo(String modelName, String ispId) throws Exception {
        NetworkClaimResponse.IspInfo ispInfo = new NetworkClaimResponse.IspInfo();

        try {
            // 1. Find EquipmentModel id by modelName
            EquipmentModel equipmentModel = equipmentModelRepo.findEquipmentModelByModelName(modelName);
            if (ObjectUtils.isEmpty(equipmentModel)) {
                logger.error("EquipmentModel not found for modelName: {}", modelName);
                return ispInfo;
            }

            // 2. Find MongoDB ispDefaultConfig by equipmentModel id and ispId
            HashMap<String, Object> queryParams = new HashMap<>();
            queryParams.put("modelId", equipmentModel.getId());
            queryParams.put("ispId", ispId);

            DBObject ispDefaultConfig = mongoService.findOne(queryParams, new HashMap<>(), "ispDefaultConfig", null);
            if (ObjectUtils.isEmpty(ispDefaultConfig)) {
                logger.error("ispDefaultConfig not found for equipmentModelId: {} and ispId: {}",
                        equipmentModel.getId(), ispId);
                return ispInfo;
            }

            // 3. Get isp.logo and isp.message
            Object ispObject = ispDefaultConfig.get("isp");
            if (ispObject instanceof DBObject) {
                DBObject isp = (DBObject) ispObject;

                // Set message
                Object messageObj = isp.get("message");
                if (messageObj != null) {
                    ispInfo.setMessage(String.valueOf(messageObj));
                }

                // Handle logo (fileId -> secureUrl)
                Object logoObj = isp.get("logo");
                if (logoObj instanceof DBObject) {
                    DBObject logo = (DBObject) logoObj;
                    Object fileIdObj = logo.get("fileId");
                    if (fileIdObj != null) {
                        String logoFileId = String.valueOf(fileIdObj);
                        logger.info("logoFileId: {}", logoFileId);
                        OptimFile logoFile = fileRepo.getOptimFileById(logoFileId);
                        if (logoFile != null && CustomStringUtils.isNotEmpty(logoFile.getSecureUrl())) {
                            ispInfo.setLogo(logoFile.getSecureUrl());
                        }
                    }
                }
            }

        } catch (Exception e) {
            logger.error("Error getting ISP info for modelName: {} and ispId: {}", modelName, ispId, e);
        }

        return ispInfo;
    }

    public void disclaimNetwork(String networkId) throws Exception {

        NCSNetwork ncsNetwork = networkRepo.getNetworkById(networkId);

        if (ncsNetwork.getClaimStatus() != NetworkClaimStatus.CLAIMED) {
            throw new ValidationException(
                    HttpStatus.CONFLICT.value(), "The network cannot be disclaimed");
        }

        networkRepo.updateNetworkClaimStatus(ncsNetwork.getId(), NetworkClaimStatus.PREDISCLAIMED);

        // Send DELETE /equipment/onboardingRegister RPC to disclaim network
        String serialNumber = manageCommonService.getGatewaySerialByUserId(networkId).orElse(STRING_EMPTY);

        HashMap<String, String> equipmentProps = commonService.read(EQUIPMENT_DIAGNOSTIC_CONFIG);
        int RPC_TIMEOUT_SECONDS = Integer.parseInt(equipmentProps.get(RPC_POLL_COUNT));

        try {
            CompletableFuture<RpcResponse> future = rpcServiceFacade.sendDisclaimNetworkRequest(
                    networkId, serialNumber, new HashMap<>(), RPC_TIMEOUT_SECONDS);

            RpcResponse rpcResponse = future.get(RPC_TIMEOUT_SECONDS, TimeUnit.SECONDS);

            Map<String, Object> data = rpcResponse.getData();
            int rpcReturnCode = Integer.parseInt(data.get("code").toString());
            if (rpcReturnCode != 200) {
                throw new RpcException(HttpStatus.BAD_REQUEST.value(), "RPC returned error code: " + rpcReturnCode);
            }

        } catch (ExecutionException e) {
            Throwable cause = e.getCause();
            if (cause instanceof TimeoutException) {
                logger.error("Disclaim Network API timeout for network:[{}]", networkId);
                throw new RpcException(HttpStatus.REQUEST_TIMEOUT.value(), "Request timeout");
            } else {
                logger.error("Disclaim Network API execution failed for network:[{}]", networkId);
                throw new RpcException(HttpStatus.BAD_REQUEST.value(), e.getMessage());
            }
        } catch (Exception e) {
            logger.error("Disclaim Network API error for network:[{}]", networkId);
            throw new RpcException(HttpStatus.BAD_REQUEST.value(), "Unexpected error");
        }

        // clear ncs_equipment mapping, just clear networkId
        ncsEquipmentService.clearNetworkMappingByNetworkId(networkId);

        // delete cluster_network mapping
        clusterNetworkRepo.deleteByNetworkId(networkId);

        networkRepo.deleteNetwork(networkId);
    }

    public void updateNetworkForAppCloud(String networkId, NCSNetworkRequest networkRequest) throws Exception {
        networkRepo.updateNetworkName(networkId, networkRequest, ApplicationConstants.APP_CLOUD_ADMIN);
    }

    public HashMap<String, Object> getInternetConnectionStatus(String networkId) throws Exception {
        CpeApiRequest proxyRequest = new CpeApiRequest();
        CpeApiResponse cpeApiResponse;
        List<CpeApiRequest.RequestEndpoint> requestEndpointList = new ArrayList<>();
        CpeApiRequest.RequestEndpoint requestEndpoint = new CpeApiRequest.RequestEndpoint(
                RpcConstants.NCS_CPE_API_INTERNET_CONNECTION,
                HttpMethod.GET.name(),
                null);
        requestEndpointList.add(requestEndpoint);
        proxyRequest.setEndpoints(requestEndpointList);
        cpeApiResponse = getCpeApiProxy(networkId, proxyRequest, CustomStringUtils.EMPTY);
        // get internet connection status always get one single response.
        if (ObjectUtils.isEmpty(cpeApiResponse.getEndpoints().get(0).getResponse().get(0))) {
            throw new ValidationException(HttpStatus.SERVICE_UNAVAILABLE.value(),
                    "RPC response is empty or RPC failed.");
        }

        return objectMapper.convertValue(cpeApiResponse.getEndpoints().get(0).getResponse().get(0), HashMap.class);
    }

    public List<Map<String, Object>> getNetworkDevices(String networkId) throws Exception {
        CpeApiRequest proxyRequest = new CpeApiRequest();
        CpeApiResponse cpeApiResponse;
        List<CpeApiRequest.RequestEndpoint> requestEndpointList = new ArrayList<>();
        CpeApiRequest.RequestEndpoint requestEndpoint = new CpeApiRequest.RequestEndpoint(
                RpcConstants.NCS_CPE_API_DEVICES,
                HttpMethod.GET.name(),
                null);
        requestEndpointList.add(requestEndpoint);
        proxyRequest.setEndpoints(requestEndpointList);
        cpeApiResponse = getCpeApiProxy(networkId, proxyRequest, CustomStringUtils.EMPTY);

        if (ObjectUtils.isEmpty(cpeApiResponse.getEndpoints().get(0).getResponse())) {
            throw new ValidationException(HttpStatus.SERVICE_UNAVAILABLE.value(),
                    "RPC response is empty or RPC failed.");
        }

        List<Object> responseList = cpeApiResponse.getEndpoints().get(0).getResponse();

        for (Object deviceObj : responseList) {
            Map<String, Object> deviceMap = (Map<String, Object>) deviceObj;
            String deviceId = (String) deviceMap.get("id");
            HostnameDetailDto hostnameDetailDTO = getHostnameDetailByNetworkIdAndMacAddress(networkId, deviceId);
            deviceMap.put("name", hostnameDetailDTO.getFriendlyName());
            deviceMap.put("firstSeenTime", hostnameDetailDTO.getFirstSeenTime());
            deviceMap.put("verified",
                    !ObjectUtils.isEmpty(hostnameDetailDTO.getVerified()) && hostnameDetailDTO.getVerified());

            // Response json "type" column source priority:
            // hostnameDetail.userDefineDeviceType, CPE-API /api/v1/data/devices
            // reponse.type
            if (!ObjectUtils.isEmpty(hostnameDetailDTO.getUserDefineDeviceType())) {
                deviceMap.put("type", hostnameDetailDTO.getUserDefineDeviceType());
            }

        }

        return (List<Map<String, Object>>) (List<?>) responseList;
    }

    public Map<String, Object> getNetworkDevice(String networkId, String deviceId)
            throws Exception {
        List<Map<String, Object>> allDevices = getNetworkDevices(networkId);

        return allDevices.stream()
                .filter(device -> deviceId.equals(device.get("id")))
                .findFirst()
                .orElseThrow(() -> new ValidationException(HttpStatus.NOT_FOUND.value(),
                        "Device not found with ID: " + deviceId));
    }

    /**
     * Get device details from hostnameDetail collection
     *
     * @return DeviceDetailDto containing friendlyName and firstSeenTime, or DTO
     *         with null values if
     *         not found
     */
    private HostnameDetailDto getHostnameDetailByNetworkIdAndMacAddress(String networkId, String deviceId) {

        try {
            HashMap<String, Object> queryParams = new HashMap<>();
            queryParams.put("userId", networkId);
            // deviceId from cpe-api is equal to macAddress
            queryParams.put("macAddress", deviceId);

            BasicDBObject sort = new BasicDBObject();
            sort.put("timestamp", ApplicationConstants.DESC);

            DBObject hostnameDetail = mongoService.findOne(queryParams, HOSTNAME_DETAIL, sort, new BasicDBObject());

            if (!ObjectUtils.isEmpty(hostnameDetail)) {
                String friendlyName = ObjectUtils.isEmpty(hostnameDetail.get("friendlyName")) ? CustomStringUtils.EMPTY
                        : hostnameDetail.get("friendlyName").toString();
                Boolean verified = ObjectUtils.isEmpty(hostnameDetail.get("verified")) ? false
                        : Boolean.parseBoolean(hostnameDetail.get("verified").toString());
                String serialNumber = ObjectUtils.isEmpty(hostnameDetail.get("serialNumber")) ? CustomStringUtils.EMPTY
                        : hostnameDetail.get("serialNumber").toString();
                String userDefineDeviceType = ObjectUtils.isEmpty(hostnameDetail.get("userDefineDeviceType"))
                        ? CustomStringUtils.EMPTY
                        : hostnameDetail.get("userDefineDeviceType").toString();

                // Extract firstSeenTime from _id field
                Long firstSeenTime = null;
                Object idObj = hostnameDetail.get("_id");
                if (!ObjectUtils.isEmpty(idObj)) {
                    String objectIdStr = idObj.toString();
                    firstSeenTime = MongoDbUtils.extractTimestampFromObjectId(objectIdStr);
                }

                return new HostnameDetailDto(friendlyName, firstSeenTime, verified, serialNumber, userDefineDeviceType);
            }
        } catch (Exception e) {
            logger.warn("Failed to get device details from hostnameDetail for MAC: {}", deviceId, e);
        }

        return new HostnameDetailDto(CustomStringUtils.EMPTY, Instant.now().toEpochMilli(), false,
                CustomStringUtils.EMPTY, CustomStringUtils.EMPTY);
    }

    /**
     * Update device information in hostnameDetail collection
     *
     * @param networkId     Network ID
     * @param deviceId      Device ID (MAC address)
     * @param updateRequest Device update request containing name, type, and
     *                      verified fields
     * @throws Exception if update fails
     */
    public void updateNetworkDevice(String networkId, String deviceId, DeviceUpdateRequest updateRequest)
            throws Exception {
        // 1. Validate device exists and get device information
        Map<String, Object> deviceMap = getDeviceMap(networkId, deviceId);

        // 2. Find upstream equipment serial number
        String serialNumber = findUpstreamEquipmentSerial(networkId, deviceId, deviceMap);

        // 3. Update or create hostnameDetail record
        updateHostnameDetailRecord(networkId, deviceId, serialNumber, updateRequest);
    }

    public void executeDeviceAction(String networkId, String deviceId, String action) throws Exception {
        // 1. Validate action
        if (CustomStringUtils.isEmpty(DeviceAction.toRpcAction(action))) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Invalid action");
        }

        // 2. Get device information through getNetworkDevice
        Map<String, Object> deviceMap = getDeviceMap(networkId, deviceId);
        String serialNumber = findUpstreamEquipmentSerial(networkId, deviceId, deviceMap);
        logger.info("Device is linked to equipment serialNumber: {}", serialNumber);
        if (CustomStringUtils.isEmpty(serialNumber)) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Device serial number not found");
        }

        // 3. Get RPC timeout configuration
        HashMap<String, String> equipmentProps = commonService.read(EQUIPMENT_DIAGNOSTIC_CONFIG);
        int RPC_TIMEOUT_SECONDS = Integer.parseInt(equipmentProps.get(RPC_POLL_COUNT));

        // 4. Send RPC request
        try {
            CompletableFuture<RpcResponse> future = rpcServiceFacade.sendDeviceActionRequest(
                    networkId, serialNumber, deviceId, RpcConstants.ACTION_KICK_WIFI_STA, RPC_TIMEOUT_SECONDS);

            RpcResponse rpcResponse = future.get(RPC_TIMEOUT_SECONDS, TimeUnit.SECONDS);

            Map<String, Object> data = rpcResponse.getData();
            int rpcReturnCode = Integer.parseInt(data.get("code").toString());

            // 5. Handle errors based on RPC status code
            if (rpcReturnCode != HttpStatus.OK.value()) {
                logger.error("device action rpc failed, rpcReturnCode: {}", rpcReturnCode);
                throw new RpcException(HttpStatus.BAD_REQUEST.value(), "RPC returned error code: " + rpcReturnCode);
            }

        } catch (ExecutionException e) {
            Throwable cause = e.getCause();
            if (cause instanceof TimeoutException) {
                logger.error("Device action API timeout for network:[{}]", networkId);
                throw new RpcException(HttpStatus.REQUEST_TIMEOUT.value(), "Request timeout");
            } else {
                logger.error("Device action API execution failed for network:[{}]", networkId);
                throw new RpcException(HttpStatus.BAD_REQUEST.value(), e.getMessage());
            }
        } catch (Exception e) {
            logger.error("Device action API error for network:[{}]", networkId);
            throw new RpcException(HttpStatus.BAD_REQUEST.value(), "Unexpected error");
        }
    }

    /**
     * return device information
     */
    private Map<String, Object> getDeviceMap(String networkId, String deviceId) throws Exception {
        Map<String, Object> deviceMap = getNetworkDevice(networkId, deviceId);
        if (ObjectUtils.isEmpty(deviceMap)) {
            throw new ValidationException(HttpStatus.NOT_FOUND.value(), "Device not found with ID: " + deviceId);
        }
        return deviceMap;
    }

    /**
     * Find upstream equipment serial number for the device
     */
    private String findUpstreamEquipmentSerial(String networkId, String deviceId, Map<String, Object> deviceMap)
            throws Exception {
        String upstreamId = (String) deviceMap.get("upstreamId");

        // Validate upstreamId
        if (StringUtils.equals(ActiontecConstants.INVALID_MAC_ADDRESS, upstreamId)) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "You can only update Device");
        }

        if (StringUtils.isEmpty(upstreamId)) {
            logger.info("deviceId:{} upstreamId is empty, cannot find upstream equipment", deviceId);
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(),
                    "deviceId:" + deviceId + " can not find upstream equipment");
        }

        // Find serial from ap_detail
        String serialNumber = apDetailDao.findSerialByUserIdAndMacAddress(networkId, upstreamId);
        if (StringUtils.isEmpty(serialNumber)) {
            logger.info("deviceId:{}, cannot find device with upstreamId:{}", deviceId, upstreamId);
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(),
                    "deviceId:" + deviceId + " can not find upstream equipment");
        }

        return serialNumber;
    }

    /**
     * Update or create hostnameDetail record
     */
    private void updateHostnameDetailRecord(String networkId, String deviceId, String serialNumber,
            DeviceUpdateRequest updateRequest) throws Exception {
        Optional<HostDto> hostDtoOptional = hostDao.findByUserIdAndDeviceMacAndSerialWithoutTimestamp(networkId,
                deviceId, serialNumber);

        if (hostDtoOptional.isPresent()) {
            // Update existing record
            updateUserDefineValues(networkId, deviceId, serialNumber, updateRequest);
        } else {
            // Upsert new record
            upsertUserDefinedValuesWithEmptyDefaults(networkId, deviceId, serialNumber, updateRequest);
        }
    }

    /**
     * Update existing hostnameDetail record
     */
    private void updateUserDefineValues(String networkId, String deviceId, String serialNumber,
            DeviceUpdateRequest updateRequest) throws Exception {
        Map<String, Object> updateFields = new HashMap<>();
        updateFields.put("friendlyName", updateRequest.getName());
        updateFields.put("userDefineDeviceType", updateRequest.getUserDefineDeviceType());
        updateFields.put("verified", updateRequest.getVerified());

        hostDao.updateByUserIdAndMacAddressAndSerial(networkId, deviceId, serialNumber, updateFields, false, false);
    }

    /**
     * upsert new hostnameDetail record
     */
    private void upsertUserDefinedValuesWithEmptyDefaults(String networkId, String deviceId, String serialNumber,
            DeviceUpdateRequest updateRequest) throws Exception {
        hostDao.upsertUserDefinedValuesWithEmptyDefaults(
                networkId,
                deviceId,
                serialNumber,
                updateRequest.getName(),
                updateRequest.getUserDefineDeviceType(),
                updateRequest.getVerified());
    }

    /**
     * Get SSID settings for a network
     *
     * @param networkId Network ID
     * @return List of SSID settings
     * @throws Exception if retrieval fails
     */
    public List<SsidSettingsDTO> getSsidSettings(String networkId) throws Exception {
        // Get network information
        NCSNetwork network = networkRepo.getNetworkById(networkId);
        if (Objects.isNull(network)) {
            throw new ValidationException(HttpStatus.NOT_FOUND.value(), "Network not found with ID: " + networkId);
        }

        // find gateway serial
        String serial = manageCommonService.getGatewaySerialByUserId(networkId).orElse(STRING_EMPTY);

        // Get shadow data from ProvisionService
        ObjectNode shadowData = provisionService.getShadow(networkId, serial);

        // Extract serviceWiFi section
        JsonNode serviceWiFiNode = shadowData.path(ProvisionConstants.OPTIM).path(ProvisionConstants.NETWORK)
                .path(ProvisionConstants.SERVICE_WI_FI);

        // Get band-specific security modes supported from ApCapabilityDao in one call
        Map<String, List<String>> bandSecurityModesMap = apCapabilityDao.getAllBandSecurityModes();

        // Use mapper to process all profiles and convert to SsidSettingsDto list
        return shadowServiceWifiMapper.mapServiceWifiNodeToSsidSettingsList(serviceWiFiNode, bandSecurityModesMap);
    }

    /**
     * Update SSID settings for a network
     *
     * @param networkId           Network ID
     * @param ssidSettingsRequest List of SSID settings to update
     * @throws Exception if update fails
     */
    public void updateSsidSettings(String networkId, List<SsidSettingsDTO> ssidSettingsRequest) throws Exception {
        // Get network information
        NCSNetwork network = networkRepo.getNetworkById(networkId);
        if (ObjectUtils.isEmpty(network)) {
            throw new ValidationException(HttpStatus.NOT_FOUND.value(), "Network not found with ID: " + networkId);
        }

        // find gateway serial
        String serial = manageCommonService.getGatewaySerialByUserId(networkId).orElse(STRING_EMPTY);

        // Get current shadow data from ProvisionService
        ObjectNode shadowData = provisionService.getShadow(networkId, serial);

        // Extract serviceWiFi section
        JsonNode serviceWiFiNode = shadowData.path(ProvisionConstants.OPTIM).path(ProvisionConstants.NETWORK)
                .path(ProvisionConstants.SERVICE_WI_FI);

        // Get band-specific security modes supported from ApCapabilityDao in one call
        Map<String, List<String>> bandSecurityModesMap = apCapabilityDao.getAllBandSecurityModes();

        // Create a mutable copy of the shadow data
        ObjectNode updatedShadowData = shadowData.deepCopy();
        ObjectNode serviceWiFiUpdateNode = (ObjectNode) updatedShadowData.path(ProvisionConstants.OPTIM)
                .path(ProvisionConstants.NETWORK).path(ProvisionConstants.SERVICE_WI_FI);

        // Validate and update each SSID setting
        for (SsidSettingsDTO ssidSettingRequest : ssidSettingsRequest) {
            validateSsidSettingRequest(ssidSettingRequest, serviceWiFiNode, bandSecurityModesMap);
            updateServiceWifiNodeWithSsidSetting(serviceWiFiUpdateNode, ssidSettingRequest);
        }

        // Save updated shadow data
        boolean updateResult = provisionService.setShadow(networkId, serial, updatedShadowData);
        if (!updateResult) {
            throw new ValidationException(HttpStatus.INTERNAL_SERVER_ERROR.value(), "Failed to update SSID settings");
        }
    }

    /**
     * Validate SSID setting request against existing data
     */
    private void validateSsidSettingRequest(SsidSettingsDTO request, JsonNode serviceWiFiNode,
            Map<String, List<String>> bandSecurityModesMap) throws ValidationException {
        String requestId = request.getId();
        String requestType = request.getType();

        // Check if profile exists in current data
        JsonNode currentProfile = serviceWiFiNode.path(requestId);
        if (currentProfile.isMissingNode()) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Profile not found: " + requestId);
        }

        // Validate that id and type are the same (they are keys and cannot be changed)
        if (!requestId.equals(requestType)) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "id and type must be the same");
        }

        // Validate SSIDs
        if (request.getSsids() != null) {
            JsonNode currentRadios = currentProfile.path("radios");

            for (SsidDTO ssidRequest : request.getSsids()) {
                String ssidId = ssidRequest.getId();
                String ssidRadio = ssidRequest.getRadio();
                String ssidType = ssidRequest.getType();

                // Check if radio exists in current data
                JsonNode currentRadio = currentRadios.path(ssidId);
                if (currentRadio.isMissingNode()) {
                    throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Radio not found: " + ssidId);
                }

                // Validate that ssid.id and ssid.radio are the same (they are keys and cannot
                // be changed)
                if (!ssidId.equals(ssidRadio)) {
                    throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "id and radio must be the same");
                }

                // Validate that ssid.type matches the profile type
                if (!requestType.equals(ssidType)) {
                    throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "SSID type must match profile type");
                }

                validateSecurityMode(bandSecurityModesMap, ssidRequest);

            }
        }
    }

    private void validateSecurityMode(Map<String, List<String>> bandSecurityModesMap, SsidDTO ssidRequest) {
        String securityMode = ssidRequest.getSecurityMode();
        if (ObjectUtils.isEmpty(securityMode)) {
            return; // No security mode to validate
        }

        // Map radio key to capability band format
        String radioKey = ssidRequest.getRadio(); // This will be "2.4G", "5G", or "6G"
        String capabilityBand = mapRadioKeyToCapabilityBand(radioKey);

        // Get security modes for this specific band
        List<String> securityModesForBand = bandSecurityModesMap.getOrDefault(capabilityBand, Collections.emptyList());

        // Validate that securityMode is within the band-specific securityModesSupported
        // range
        if (!securityModesForBand.contains(securityMode)) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(),
                    "Security mode " + securityMode + " is not supported for " + radioKey);
        }
    }

    /**
     * Map radio key from shadow data format to capability data format
     */
    private static String mapRadioKeyToCapabilityBand(String radioKey) {
        switch (radioKey) {
            case "2.4G":
                return "2g";
            case "5G":
                return "5gl"; // Using 5gl as primary 5G band
            case "6G":
                return "6g";
            default:
                return radioKey;
        }
    }

    /**
     * Update service wifi node with SSID setting
     */
    private void updateServiceWifiNodeWithSsidSetting(ObjectNode serviceWiFiNode, SsidSettingsDTO request) {
        String profileId = request.getId();
        ObjectNode profileNode = (ObjectNode) serviceWiFiNode.path(profileId);

        // Use mapper to update profile-level settings
        shadowServiceWifiMapper.updateProfileInShadow(profileNode, request);

        // Update SSID-level settings
        if (request.getSsids() != null) {
            ObjectNode radiosNode = (ObjectNode) profileNode.path("radios");

            for (SsidDTO ssidRequest : request.getSsids()) {
                String radioId = ssidRequest.getId();
                ObjectNode radioNode = (ObjectNode) radiosNode.path(radioId);

                // Use mapper to update radio-level settings
                shadowServiceWifiMapper.updateRadioInShadow(radioNode, ssidRequest);
            }
        }
    }

    public void updateNetworkSystemConfig(String networkId, SystemConfigDTO systemConfigRequest) throws Exception {
        String serial = manageCommonService.getGatewaySerialByUserId(networkId).orElse(STRING_EMPTY);
        if (CustomStringUtils.isEmpty(serial)) {
            logger.error("Gateway serial not found for network: {}", networkId);
            throw new ValidationException(HttpStatus.NOT_FOUND.value(),
                    "Gateway serial not found for network: " + networkId);
        }

        // Get current shadow data from ProvisionService
        ObjectNode shadowData = provisionService.getShadow(networkId, serial);
        if (ObjectUtils.isEmpty(shadowData)) {
            logger.error("Shadow data not found for network: {}", networkId);
            throw new ValidationException(HttpStatus.NOT_FOUND.value(),
                    "Shadow data not found for network: " + networkId);
        }

        // Update system config
        ObjectNode rootNode = shadowData.deepCopy();
        ObjectNode systemUpdateNode = (ObjectNode) rootNode.path(ProvisionConstants.OPTIM)
                .path(ProvisionConstants.NETWORK).path(ProvisionConstants.SYSTEM);

        if (systemUpdateNode.isMissingNode()) {
            logger.error("Invalid shadow data structure for network: {}", networkId);
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(),
                    "Invalid shadow data structure for network: " + networkId);
        }

        systemUpdateNode.put("password", systemConfigRequest.getPassword());
        systemUpdateNode.put("timezone", systemConfigRequest.getTimezone());

        boolean updateResult = provisionService.setShadow(networkId, serial, rootNode);
        if (!updateResult) {
            throw new ValidationException(HttpStatus.INTERNAL_SERVER_ERROR.value(), "Failed to update system config");
        }
    }

    @Autowired
    private NCSEquipmentRepo ncsEquipmentRepo;

    public void processExtenderPairing(String networkId, String extenderSerialNumber, RequestMethod requestMethod) throws Exception {
        // 1. Get gateway serial by networkId
        String gatewaySerial = manageCommonService.getGatewaySerialByUserId(networkId).orElse(CustomStringUtils.EMPTY);
        if (CustomStringUtils.isEmpty(gatewaySerial)) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Current network cannot find gateway serial, networkId: " + networkId);
        }

        // 2. Check if extender exists and get publicKey
        NCSEquipment extenderEquipment = ncsEquipmentRepo.getNCSEquipmentBySerial(extenderSerialNumber);
        if (ObjectUtils.isEmpty(extenderEquipment)) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Extender serial number not found: " + extenderSerialNumber);
        }

        String publicKey = extenderEquipment.getDppKey();
        if (CustomStringUtils.isEmpty(publicKey)) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Extender public key not found for serial: " + extenderSerialNumber);
        }

        // 3. Update equipment networkId based on request method
        switch (requestMethod) {
            case POST:
                extenderEquipment.setNetworkId(networkId);
                break;
            case DELETE:
                extenderEquipment.setNetworkId(null);
                break;
            default:
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Invalid request method: " + requestMethod);
        }
        ncsEquipmentRepo.update(extenderEquipment);

        // 4. Prepare RPC payload
        Map<String, Object> payloadMap = new HashMap<>();
        payloadMap.put("serialNumber", extenderSerialNumber);
        payloadMap.put("publicKey", publicKey);

        // 5. Send RPC request
        HashMap<String, String> equipmentProps = commonService.read(EQUIPMENT_DIAGNOSTIC_CONFIG);
        int RPC_TIMEOUT_SECONDS = Integer.parseInt(equipmentProps.get(RPC_POLL_COUNT));

        try {
            // todo: wait for fw team implementation rpc api
            CompletableFuture<RpcResponse> future = rpcServiceFacade.sendExtenderPairingRequest(
                    networkId, gatewaySerial, payloadMap, requestMethod.name(), RPC_TIMEOUT_SECONDS);

            RpcResponse rpcResponse = future.get(RPC_TIMEOUT_SECONDS, TimeUnit.SECONDS);

            if (!rpcResponse.isSuccess()) {
                throw new RpcException(HttpStatus.BAD_REQUEST.value(), rpcResponse.getErrorMessage());
            }

            Map<String, Object> data = rpcResponse.getData();
            int rpcReturnCode = Integer.parseInt(data.get("code").toString());
            if (rpcReturnCode != HttpStatus.OK.value()) {
                logger.error("extender pairing rpc failed, method: {}, rpcReturnCode: {}", requestMethod, rpcReturnCode);
                throw new RpcException(HttpStatus.BAD_REQUEST.value(), "RPC returned error code: " + rpcReturnCode);
            }

            logger.info("Extender pairing {} successful for networkId: {}, extenderSerial: {}", requestMethod, networkId, extenderSerialNumber);

        } catch (ExecutionException e) {
            Throwable cause = e.getCause();
            if (cause instanceof TimeoutException) {
                logger.error("Extender pairing {} timeout for network:[{}], extender:[{}]", requestMethod, networkId, extenderSerialNumber);
                throw new RpcException(HttpStatus.REQUEST_TIMEOUT.value(), "Request timeout");
            } else {
                logger.error("Extender pairing {} execution failed for network:[{}], extender:[{}]", requestMethod, networkId, extenderSerialNumber);
                throw new RpcException(HttpStatus.BAD_REQUEST.value(), e.getMessage());
            }
        }  catch (RpcException e) {
            logger.error("Rpc exception code: {}, message: {}", e.getCode(), e.getMessage());
            throw e;
        } catch (Exception e) {
            logger.error("Extender pairing {} error for network:[{}], extender:[{}]", requestMethod, networkId, extenderSerialNumber);
            throw new RpcException(HttpStatus.BAD_REQUEST.value(), "Unexpected error");
        }
    }
}
