package com.actiontec.optim.platform.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.actiontec.optim.platform.repository.UserRepo;
import com.actiontec.optim.util.CustomStringUtils;
import com.actiontec.optim.util.ValidationUtils;

@Service
public class UserService {

    @Autowired
    private UserRepo userRepo;

    public String getFullNameById(String id) {

        if (CustomStringUtils.isBlank(id)) {
            ValidationUtils.throwValidationException("User ID cannot be null or empty");
        }

        return userRepo.getFullNameById(id);
    }
    
}
