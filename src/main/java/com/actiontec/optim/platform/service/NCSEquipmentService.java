package com.actiontec.optim.platform.service;

import static com.actiontec.optim.platform.constant.ApplicationConstants.CONNECTION_STATUS_OFFLINE;
import static com.incs83.app.constants.misc.ApplicationConstants.COMMA;
import static com.incs83.app.constants.misc.ApplicationConstants.DESC;
import static com.incs83.app.constants.misc.ApplicationConstants.TIMESTAMP;
import static com.incs83.constants.ApplicationCommonConstants.AP_DETAIL;

import java.io.IOException;
import java.io.PrintWriter;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletResponse;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import com.actiontec.optim.mongodb.dao.ApDetailDao;
import com.actiontec.optim.mongodb.dto.ApDetailDto;
import com.actiontec.optim.platform.api.v5.model.EquipmentActionRequest;
import com.actiontec.optim.platform.api.v6.dto.NCSEquipmentDTO;
import com.actiontec.optim.platform.api.v6.dto.NCSEquipmentForAppCloudDTO;
import com.actiontec.optim.platform.api.v6.dto.NCSEquipmentQueryDTO;
import com.actiontec.optim.platform.api.v6.dto.NCSEquipmentRequest;
import com.actiontec.optim.platform.api.v6.dto.PaginationResponse;
import com.actiontec.optim.platform.model.enums.ConnectionStatus;
import com.actiontec.optim.platform.repository.EquipmentRepo;
import com.actiontec.optim.platform.repository.NCSEquipmentRepo;
import com.actiontec.optim.util.CustomStringUtils;
import com.actiontec.optim.util.ValidationUtils;
import com.incs83.app.business.v2.ManageEquipmentService;
import com.incs83.app.business.v2.NewManageEquipmentService;
import com.incs83.app.constants.misc.ActiontecConstants;
import com.incs83.app.entities.Equipment;
import com.incs83.app.entities.NCSEquipment;
import com.incs83.app.enums.ErrorResponseEnum;
import com.incs83.app.service.data.MongoServiceImpl;
import com.incs83.constants.ApplicationCommonConstants;
import com.incs83.exceptions.handler.ValidationException;
import com.incs83.service.CommonService;
import com.mongodb.BasicDBObject;
import com.mongodb.DBObject;

@Service
public class NCSEquipmentService {

    private final Logger logger = LogManager.getLogger(this.getClass());

    @Autowired
    private NCSIspService ispService;

    @Autowired
    NewManageEquipmentService newManageEquipmentService;

    @Autowired
    private ManageEquipmentService manageEquipmentService;

    @Autowired
    private MongoServiceImpl mongoServiceImpl;

    @Autowired
    EquipmentRepo equipmentRepo;

    @Autowired
    private NCSEquipmentRepo ncsEquipmentRepo;

    @Autowired
    private BrokerInfoService brokerInfoService;

    @Autowired
    private CommonService commonService;

    @Autowired
    private ApDetailDao apDetailDao;

    @Autowired
    private EquipmentModelService equipmentModelService;

    private final List<String> csvTitle;

    public NCSEquipmentService() {
        csvTitle = Arrays.asList("serial", "mac", "modelName", "productId");
    }

    public PaginationResponse<NCSEquipmentDTO> getAllEquipment(NCSEquipmentQueryDTO queryDTO) throws Exception {
        String defaultBrokerId = brokerInfoService.getDefaultBrokerId();
        PaginationResponse<NCSEquipmentDTO> response = ncsEquipmentRepo.getAllEquipment(queryDTO);

        // Set default broker_info_id when equipment has no redirect setting in
        // equipment_redirect table
        if (response != null && CollectionUtils.isNotEmpty(response.getData())) {
            response.getData().forEach(equipment -> {
                if (equipment.getBrokerInfoId() == null) {
                    equipment.setBrokerInfoId(defaultBrokerId);
                }
            });
        }

        return response;
    }

    public void downloadTemplate(HttpServletResponse response) throws IOException {
        response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"equipment_template.csv\"");
        PrintWriter writer = response.getWriter();
        writer.println(String.join(COMMA, csvTitle));
        writer.flush();
    }

    public void checkQueryParameter(NCSEquipmentQueryDTO queryDTO) throws Exception {
        if (StringUtils.isNotEmpty(queryDTO.getIspId())) {
            ispService.checkIspIdPresent(queryDTO.getIspId());
        }

        if (StringUtils.isNotEmpty(queryDTO.getNetworkId())) {
            checkQueryNetworkId(queryDTO);
        }
    }

    private void checkQueryNetworkId(NCSEquipmentQueryDTO queryDTO) throws Exception {
        if (StringUtils.isNotEmpty(queryDTO.getIspId())) {
            List<String> ispNetworkIdList = ncsEquipmentRepo.getNCSEquipmentListByIspId(queryDTO.getIspId())
                    .stream()
                    .map(NCSEquipment::getNetworkId)
                    .collect(Collectors.toList());
            if (StringUtils.isNotEmpty(queryDTO.getNetworkId()) && !ispNetworkIdList.contains(queryDTO.getNetworkId())) {
                throw new ValidationException(HttpStatus.FORBIDDEN.value(), "Query networkId is not belongs to the user's isp.");
            }
        }
    }

    public void updateNetworkAndSubscriberByIdList(String ispId, List<String> equipmentIdList, String networkId,
            String subscriberId) throws Exception {
        ncsEquipmentRepo.updateNetworkAndSubscriberByIdList(ispId, equipmentIdList, networkId, subscriberId);
    }

    // delete old equipment table
    public void deleteOldEquipmentBySubscriberId(String subscriberId) throws Exception {
        List<Equipment> equipmentList = equipmentRepo.getEquipmentListBySubscriberId(subscriberId);
        if (CollectionUtils.isNotEmpty(equipmentList)) {
            for (Equipment equipment : equipmentList) {
                // this method contains clean mongo apDetail data.
                newManageEquipmentService.deleteEquipmentById(equipment.getId());
            }
        }
    }

    public List<NCSEquipment> getNCSEquipmentListBySerialList(List<String> serialList) throws Exception {
        return ncsEquipmentRepo.getNCSEquipmentListBySerialList(serialList);
    }

    public List<NCSEquipment> getNCSEquipmentListByNetworkId(String networkId) throws Exception {
        return ncsEquipmentRepo.getNCSEquipmentListByNetworkId(networkId);
    }

    public NCSEquipment getNCSEquipmentById(String id) throws Exception {
        return ncsEquipmentRepo.getNCSEquipmentById(id);
    }

    // clear ncs_equipment table subscriber_id
    public void clearSubscriberMappingBySubscriberId(String subscriberId) throws Exception {
        ncsEquipmentRepo.clearSubscriberMappingBySubscriberId(subscriberId);
    }

    public void clearNetworkMappingByNetworkId(String networkId) throws Exception {
        List<NCSEquipment> networkEquipmentList = getNCSEquipmentListByNetworkId(networkId);
        if (CollectionUtils.isNotEmpty(networkEquipmentList)) {
            for (NCSEquipment ncsEquipment : networkEquipmentList) {
                deleteMongoApDetail(ncsEquipment);
            }
            ncsEquipmentRepo.clearNetworkMappingByNetworkId(networkId);
        }
    }

    private void deleteMongoApDetail(NCSEquipment ncsEquipment) {
        BasicDBObject mongoFieldOptions = new BasicDBObject();
        mongoFieldOptions.clear();
        HashMap<String, String> params = new HashMap<>();
        params.put("userId", ncsEquipment.getNetworkId());
        params.put("serialNumber", ncsEquipment.getSerial());
        DBObject apDetails = mongoServiceImpl.findOne(params, new HashMap<>(), AP_DETAIL, TIMESTAMP, DESC, mongoFieldOptions);
        if (Objects.nonNull(apDetails)) {
            String[] rgwCleanUp = ActiontecConstants.cleanupRGW.split(COMMA);
            newManageEquipmentService.cleanUpData(params, rgwCleanUp);
        }
    }

    public boolean checkEquipmentListByIspId(String ispId, List<String> equipmentIdList) throws Exception {
        return ncsEquipmentRepo.checkEquipmentListByIspId(ispId, equipmentIdList);
    }

    public String getDisplayNameForEquipment(String ncsEquipmentId, ApDetailDto apDetailDto) {

        String friendlyName = CustomStringUtils.defaultIfBlank(ncsEquipmentRepo.getFriendlyNameById(ncsEquipmentId),
                apDetailDto.getFriendlyName());

        if (CustomStringUtils.isBlank(friendlyName)) {
            return CustomStringUtils.EMPTY;
        }

        return friendlyName;
    }

    public ConnectionStatus getEquipmentConnectionStatus(ApDetailDto apDetailDto) throws Exception {

        ValidationUtils.throwNotFoundExceptionIfIsNull(apDetailDto, ErrorResponseEnum.NCS_EQUIPMENT_NOT_FOUND, "Equipment not found");

        if (CustomStringUtils.equalsIgnoreCase(CONNECTION_STATUS_OFFLINE, apDetailDto.getConnectionStatus())) {
            return ConnectionStatus.Down;
        }

        if (!ObjectUtils.isEmpty(apDetailDto.getTimestamp())) {

            HashMap<String, String> commonProps = commonService.read(ApplicationCommonConstants.COMMON_CONFIG);
            int equipmentOfflineTime = 7;
            try {
                equipmentOfflineTime = Integer.valueOf(commonProps.get(ApplicationCommonConstants.COMMON_DEVICE_STATUS_RETENTION_TIME));
            } catch (Exception e) {
                logger.error("Failed to get equipment offline time: {}", e);
            }

            long currentMillis = System.currentTimeMillis();
            long onlineBaseMillis = currentMillis - equipmentOfflineTime * 60_000L;
            long lastMetricReceived = apDetailDto.getNtInfoTimestamp();

            if (lastMetricReceived < onlineBaseMillis) {

                apDetailDao.updateConnectionStatusAndSendingTimeById(apDetailDto.getUserId(), apDetailDto.getSerialNumber(),
                        com.actiontec.optim.platform.constant.ApplicationConstants.CONNECTION_STATUS_OFFLINE,
                        lastMetricReceived - ((equipmentOfflineTime + 10) * 60 * 1000L));

                return ConnectionStatus.Down;
            }
        }

        return ConnectionStatus.Up;
    }

    public List<NCSEquipmentForAppCloudDTO> getNetworkAllEquipment(String networkId) throws Exception {

        // get ncs_equipment list by networkId
        List<NCSEquipment> ncsEquipmentList = getNCSEquipmentListByNetworkId(networkId);

        if (CollectionUtils.isEmpty(ncsEquipmentList)) {
            return Collections.emptyList();
        }

        // get ncs_equipment serial and id map
        Map<String, String> ncsEquipmentSerialAndIdMap = ncsEquipmentList.stream()
                .collect(Collectors.toMap(NCSEquipment::getSerial, NCSEquipment::getId));

        // get apDetail list by networkId
        List<ApDetailDto> apDetailList = apDetailDao.listByUserId(networkId);

        if (CollectionUtils.isEmpty(apDetailList)) {
            return Collections.emptyList();
        }

        // log error if ncsEquipmentList and apDetailList size mismatch
        if (CollectionUtils.size(apDetailList) != CollectionUtils.size(ncsEquipmentList)) {
            logger.error("networkId: {}, ncsEquipmentList and apDetailList size mismatch: {}, {}",
                    networkId, CollectionUtils.size(ncsEquipmentList), CollectionUtils.size(apDetailList));
        }

        List<NCSEquipmentForAppCloudDTO> networkEquipmentDtoList = apDetailList.stream()
                .map(apDetail -> {
                    try {
                        return getDetailsForEquipment(apDetail, ncsEquipmentSerialAndIdMap);
                    } catch (Exception e) {
                        logger.error("Failed to covert apDetail to NCSEquipmentForAppCloudDTO for network: {}, apDetail: {}", networkId, apDetail);
                        throw new ValidationException(HttpStatus.INTERNAL_SERVER_ERROR.value(), "Failed to covert apDetail to NCSEquipmentForAppCloudDTO");
                    }
                })
                .collect(Collectors.toList());

        return networkEquipmentDtoList;
    }

    public NCSEquipmentForAppCloudDTO getNCSEquipmentByNetworkIdAndEquipmentId(String networkId, String equipmentId) throws Exception {

        // get ncs_equipment by equipmentId
        NCSEquipment ncsEquipment;
        try {
            ncsEquipment = getNCSEquipmentById(equipmentId);
        } catch (ValidationException e) {
            ValidationUtils.throwNotFoundException(ErrorResponseEnum.NCS_EQUIPMENT_NOT_FOUND,
                    String.format("Equipment not found with ID: %s", equipmentId));
            return null;
        }

        if (ObjectUtils.isEmpty(ncsEquipment) || !CustomStringUtils.equals(networkId, ncsEquipment.getNetworkId())) {
            ValidationUtils.throwNotFoundException(ErrorResponseEnum.NCS_EQUIPMENT_NOT_FOUND,
                    String.format("Equipment not found with ID: %s or networkId mismatch for networkId: %s", equipmentId, networkId));
        }

        // get apDetail by networkId and serial
        ApDetailDto apDetail = apDetailDao.findByUserIdAndSerial(networkId, ncsEquipment.getSerial()).orElse(null);

        ValidationUtils.throwNotFoundExceptionIfIsNull(apDetail, ErrorResponseEnum.NCS_EQUIPMENT_NOT_FOUND,
                String.format("ApDetail not found with networkId: %s and serial: %s", networkId, ncsEquipment.getSerial()));

        // create single equipment map
        Map<String, String> singleEquipmentMap = Collections.singletonMap(
                ncsEquipment.getSerial(), ncsEquipment.getId());

        return getDetailsForEquipment(apDetail, singleEquipmentMap);
    }

    public Boolean updateFriendlyName(String networkId, String equipmentId, NCSEquipmentRequest updateRequest) throws Exception {

        NCSEquipmentForAppCloudDTO networkEquipment = getNCSEquipmentByNetworkIdAndEquipmentId(networkId, equipmentId);

        // update nc_equipment friendly_name
        ncsEquipmentRepo.updateFriendlyNameById(equipmentId, updateRequest.getName());

        // update apDetail friendly_name
        apDetailDao.updateFriendlyNameById(networkId, networkEquipment.getSerialNumber(), updateRequest.getName());

        return true;
    }

    public void executeNetworkEquipmentActions(String networkId, String equipmentId, EquipmentActionRequest request)
            throws Exception {

        NCSEquipmentForAppCloudDTO networkEquipment = getNCSEquipmentByNetworkIdAndEquipmentId(networkId, equipmentId);

        // only access reboot action
        if (!request.getAction().equals(EquipmentService.EquipmentActions.Reboot.getValue())) {
            ValidationUtils.throwValidationException(ErrorResponseEnum.NETWORK_EQUIPMENT_INVALID_ACTION, "Invalid action");
        }

        // validate equipment status is up
        if (!Objects.equals(networkEquipment.getStatus(), ConnectionStatus.Up)) {
            ValidationUtils.throwServiceUnavailableException(ErrorResponseEnum.NCS_EQUIPMENT_STATUS_DOWN,
                    String.format("Equipment: %s status is not up", equipmentId));
        }

        String serialNumber = networkEquipment.getSerialNumber();

        manageEquipmentService.invokeInternetRPCMethodsForEquipment(networkId, serialNumber, ActiontecConstants.AP_REBOOT);
    }

    private NCSEquipmentForAppCloudDTO getDetailsForEquipment(ApDetailDto apDetail,
      Map<String, String> ncsEquipmentSerialAndIdMap) throws Exception {

        String ncsEquipmentId = ncsEquipmentSerialAndIdMap.get(apDetail.getSerialNumber());

        ValidationUtils.throwNotFoundExceptionIfIsNull(ncsEquipmentId, ErrorResponseEnum.NCS_EQUIPMENT_NOT_FOUND,
                String.format("NCS Equipment not found with serial: %s", apDetail.getSerialNumber()));

        String displayName = getDisplayNameForEquipment(ncsEquipmentId, apDetail);

        // get current device status
        ConnectionStatus status = getEquipmentConnectionStatus(apDetail);

        // get icon url from equipment model
        String iconUrl = equipmentModelService.getIconUrl(apDetail.getModelName());

        return NCSEquipmentForAppCloudDTO.convertToNCSEquipmentForAppCloudDto(apDetail, ncsEquipmentSerialAndIdMap, displayName, status, iconUrl);
    }
}
