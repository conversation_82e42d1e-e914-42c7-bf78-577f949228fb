package com.actiontec.optim.platform.service;

import com.actiontec.optim.platform.api.v6.dto.NCSIspQueryDTO;
import com.actiontec.optim.platform.api.v6.model.IspRequest;
import com.actiontec.optim.platform.api.v6.model.IspUpdateRequest;
import com.actiontec.optim.platform.repository.NCSIspRepo;
import com.actiontec.optim.util.CustomStringUtils;
import com.incs83.app.business.v2.ManageCommonService;
import com.incs83.app.constants.queries.ClusterInfoSQL;
import com.incs83.app.entities.Compartment;
import com.incs83.app.responsedto.v2.isp.ISPDTOList;
import com.incs83.app.responsedto.v2.isp.ISPDetail;
import com.incs83.app.responsedto.v2.isp.ISPModel;
import com.actiontec.optim.platform.api.v6.dto.NCSIspDTO;
import com.actiontec.optim.platform.api.v6.dto.PaginationResponse;
import com.incs83.app.service.data.MongoServiceImpl;
import com.incs83.constants.ApplicationCommonConstants;
import com.incs83.exceptions.handler.ValidationException;
import com.incs83.mt.DataAccessService;
import com.incs83.mt.MongoTenantTemplate;
import com.incs83.request.CompartmentRequest;
import com.incs83.services.HazelcastService;
import com.incs83.util.CommonUtils;
import com.mongodb.BasicDBObject;
import com.mongodb.DBObject;
import org.apache.commons.collections4.MapUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.incs83.app.constants.misc.ActiontecConstants.INTERNET_SERVICE_PROVIDER;
import static com.incs83.app.constants.misc.ApplicationConstants.*;
import static com.incs83.constants.ApplicationCommonConstants.AP_DETAIL;
import static com.incs83.constants.ApplicationCommonConstants.GATEWAY;

@Service
public class NCSIspService {

    private final Logger logger = LogManager.getLogger(this.getClass());

    @Autowired
    private MongoServiceImpl mongoService;

    @Autowired
    private MongoTenantTemplate mongoTenantTemplate;

    @Autowired
    private ManageCommonService manageCommonService;

    @Autowired
    private DataAccessService dataAccessService;

    @Autowired
    private HazelcastService cacheService;

    @Autowired
    private NCSGroupService groupService;

    @Autowired
    private EquipmentService equipmentService;

    @Autowired
    private SubscriberService subscriberService;

    @Autowired
    NCSIspRepo ncsIspRepo;

    private void populateNetworkInfoInCache() throws Exception {
        HashMap<String, Object> data = new HashMap<>();
        HashMap<String, Object> query = new HashMap<>();
        ISPDTOList ispdtoList = getAllISPs();
        ArrayList<ISPDetail> ispDetailDataList = new ArrayList<>();
        if (Objects.nonNull(ispdtoList) && !ispdtoList.getData().isEmpty()) {
            ispdtoList.getData().forEach(isp -> {
                ISPDetail ispDetail = new ISPDetail();
                ispDetail.setIspName(isp.getName());

                query.clear();
                query.put("isp", isp.getName());
                query.put("type", GATEWAY);
                long totalAp = mongoService.count(query, null, ApplicationCommonConstants.AP_DETAIL);

                query.clear();
                query.put("isp", isp.getName());
                query.put("type", GATEWAY);
                query.put("mysqlProcessed", new BasicDBObject("$exists", true));
                query.put("processed", new BasicDBObject("$exists", true));
                long totalSubscriber = mongoService.count(query, null, ApplicationCommonConstants.AP_DETAIL);

                query.clear();
                query.put("isp", isp.getName());
                query.put("type", GATEWAY);
                query.put("processed", new BasicDBObject("$exists", true));
                long totalScannedRecord = mongoService.count(query, null, ApplicationCommonConstants.AP_DETAIL);

                ispDetail.setMapped(totalSubscriber);
                ispDetail.setUnMapped(totalAp - totalScannedRecord);
                ispDetail.setFailedRecord(totalScannedRecord - totalSubscriber);

                ispDetailDataList.add(ispDetail);
            });
        }
        data.put("ispDetails", ispDetailDataList);

        query.clear();
        query.put("type", GATEWAY);
        long rgwCount = mongoService.count(query, new HashMap<>(), AP_DETAIL);
        data.put("totalRGW", rgwCount);

        cacheService.create(NETWORK_INFO_KEY, data, NETWORK_INFO_MAP_NAME);
    }

    public String createIsp(IspRequest ispRequest) throws Exception {

        if (!CommonUtils.isSysAdmin()) {
            throw new ValidationException(HttpStatus.FORBIDDEN.value(), "Insufficient permissions to access this resource");
        }
        if (Objects.nonNull(ispRequest.getName()) && ispRequest.getName().equals(EMPTY_STRING))
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Name cannot be empty");

        if (Objects.nonNull(ispRequest.getDescription()) && ispRequest.getDescription().length() > 255)
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Description length must be between 1-255 characters");

        if (Objects.nonNull(ispRequest.getAlias()) && ispRequest.getAlias().equals(EMPTY_STRING))
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Display Name cannot be empty");

        String isp = null;

        ISPDTOList ispdtoList = (ISPDTOList) cacheService.read(ISP_KEY, ISP);
        if (Objects.nonNull(ispdtoList)) {
            ISPModel ispModel = ispdtoList.getData().stream().filter(q -> q.getName().equals(ispRequest.getName())).findAny().orElse(null);
            isp = Objects.nonNull(ispModel) ? ispModel.getName() : null;
        } else {
            isp = manageCommonService.getIspByName(ispRequest.getName());
        }

        if (Objects.nonNull(isp))
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "ISP already exist with name : " + ispRequest.getName());

        HashMap<String, Object> ispDetails = new HashMap<>();
        String ispId = CommonUtils.generateUUID();
        ispDetails.put("id", ispId);
        ispDetails.put("name", ispRequest.getName());
        ispDetails.put("displayName", ispRequest.getAlias());
        ispDetails.put("description", ispRequest.getDescription());
        ispDetails.put("default", false);
        ispDetails.put("dateCreated", new Date());

        boolean flag = mongoService.create(INTERNET_SERVICE_PROVIDER, ispDetails);
        if (!flag) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Request failed.Unable to create ISP");
        }

        cacheService.delete(ISP_KEY, ISP);

        populateNetworkInfoInCache();

        // create default group and cluster
        CompartmentRequest compartmentRequest = new CompartmentRequest();
        compartmentRequest.setServiceProvider(ispId);
        compartmentRequest.setName(ispRequest.getName() + UNDER_SCORE_STRING + DEFAULT_GROUP);
        compartmentRequest.setDescription(ispRequest.getDescription());
        compartmentRequest.setProcessed(false);
        groupService.createGroupAndCluster(compartmentRequest);

        return ispId;
    }

    public PaginationResponse<NCSIspDTO> getAllIsps(NCSIspQueryDTO queryDTO) throws Exception {
        return ncsIspRepo.getAllIsps(queryDTO);
    }

    public NCSIspDTO getIsp(String ispId) throws Exception {

        if (!CommonUtils.isSysAdmin()) {
            throw new ValidationException(HttpStatus.FORBIDDEN.value(), "Insufficient permissions to access this resource");
        }

        BasicDBObject query = new BasicDBObject();
        query.put("id", ispId);
        DBObject result = mongoService.findOne(INTERNET_SERVICE_PROVIDER, query);

        if (result == null) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "ISP not found with id: " + ispId);
        }

        NCSIspDTO ispData = new NCSIspDTO();
        ispData.setId(result.get("id").toString());
        ispData.setAlias(result.get("displayName").toString());
        ispData.setName(result.get("name").toString());
        ispData.setDescription(result.get("description").toString());

        return ispData;
    }

    public void updateIsp(String ispId, IspUpdateRequest ispRequest) throws Exception {

        if (!CommonUtils.isSysAdmin()) {
            throw new ValidationException(HttpStatus.FORBIDDEN.value(), "Insufficient permissions to access this resource");
        }

        BasicDBObject query = new BasicDBObject();
        query.put("id", ispId);

        BasicDBObject updateField = new BasicDBObject();
        updateField.put("displayName", ispRequest.getAlias());
        updateField.put("description", ispRequest.getDescription());

        BasicDBObject update = new BasicDBObject();
        update.put("$set", updateField);

        mongoService.update(query, update, false, false, INTERNET_SERVICE_PROVIDER);
    }

    public void deleteIsp(String ispId) throws Exception {

        if (!CommonUtils.isSysAdmin()) {
            throw new ValidationException(HttpStatus.FORBIDDEN.value(), "Insufficient permissions to access this resource");
        }

        Long totalEquipment = 0L;
        Long totalSubscriber = 0L;

        String groupId = groupService.getCompartmentIdByIspId(ispId);
        if (groupId != null) {
            totalEquipment = equipmentService.getEquipmentCountByGroupId(groupId);
            totalSubscriber = subscriberService.getSubscriberCountByGroupId(groupId);
        }

        if (totalEquipment > 0 || totalSubscriber > 0) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "This ISP is still in use and cannot be deleted");
        }

        try {
            if (CustomStringUtils.isNotEmpty(groupId)) {
                HashMap<String, Object> q = new HashMap<>();
                q.put("compartment_id", groupId);
                dataAccessService.deleteUpdateNative(ClusterInfoSQL.DELETE_CLUSTER_BY_GROUP_ID, q);
                dataAccessService.delete(Compartment.class, groupId);
            }

            BasicDBObject query = new BasicDBObject();
            query.put("id", ispId);
            mongoService.deleteOne(query, INTERNET_SERVICE_PROVIDER);
            cacheService.delete(ISP_KEY, ISP);
        } catch (Exception ex) {
            // do not need to throw exception
            logger.error("delete isp comparment failed or clean mongo isp failed.", ex);
        }

        populateNetworkInfoInCache();
    }

    public String getIspNameById(String ispId) throws Exception {
        String ispName = null;
        HashMap<String, Object> queryParams = new HashMap<>();

        if (Objects.nonNull(ispId)) {
            ISPDTOList ispdtoList = (ISPDTOList) cacheService.read(ISP_KEY, ISP);
            if (Objects.nonNull(ispdtoList)) {
                ISPModel ispModel = ispdtoList.getData().stream().filter(q -> q.getId().equals(ispId)).findAny().orElse(null);
                ispName = Objects.nonNull(ispModel) ? ispModel.getName() : null;
            } else {
                queryParams.clear();
                queryParams.put("id", ispId);

                HashMap<String, Object> appendableParams = new HashMap<>();
                BasicDBObject fieldsToRemove = new BasicDBObject();
                fieldsToRemove.put("_id", ZERO);

                DBObject dbObject = mongoService.findOne(queryParams, appendableParams, INTERNET_SERVICE_PROVIDER, fieldsToRemove);
                if (Objects.nonNull(dbObject)) {
                    ispName = String.valueOf(dbObject.get("name"));
                }
            }
        }

        return ispName;
    }

    public ISPDTOList getAllISPs() {
        String isp = null;
        if (!CommonUtils.isSysAdmin()) {
            try {
                Compartment compartment = (Compartment) dataAccessService.read(Compartment.class, CommonUtils.getGroupIdOfLoggedInUser());
                if (Objects.nonNull(compartment) && Objects.nonNull(compartment.getIspId())) {
                    isp = getIspNameById(compartment.getIspId());
                }
            } catch (Exception e) {
                logger.error("Error while fetching Data from DB");
            }
        }
        ISPDTOList ispdtoList = (ISPDTOList) cacheService.read(ISP_KEY, ISP);
        String loggedInUserIsp = isp;
        if (Objects.nonNull(ispdtoList)) {
            if (Objects.nonNull(loggedInUserIsp)) {
                ArrayList<ISPModel> modelArrayList = new ArrayList<>(ispdtoList.getData().stream().filter(q -> q.getName().equals(loggedInUserIsp)).collect(Collectors.toList()));
                ispdtoList.setData(modelArrayList);
            }
            return ispdtoList;
        } else {
            ispdtoList = new ISPDTOList();
        }
        ArrayList<ISPModel> ispModels = new ArrayList<>();

        BasicDBObject fieldsToremove = new BasicDBObject();
        fieldsToremove.put("_id", 0);
        List<DBObject> dbObjectList = mongoService.findList(new HashMap<>(), INTERNET_SERVICE_PROVIDER, fieldsToremove);
        if (Objects.nonNull(dbObjectList) && !dbObjectList.isEmpty()) {
            for (DBObject dbObject : dbObjectList) {
                ISPModel ispModel = new ISPModel();
                ispModel.setId(String.valueOf(dbObject.get("id")));
                ispModel.setName(String.valueOf(dbObject.get("name")));
                ispModel.setDisplayName(String.valueOf(dbObject.get("displayName")));
                ispModel.setDescription(String.valueOf(dbObject.get("description")));
                ispModel.setDefaultType(Boolean.valueOf(dbObject.get("default").toString()));
                ispModels.add(ispModel);
            }
        }
        ispdtoList.setData(ispModels);
        cacheService.create(ISP_KEY, ispdtoList, ISP);

        if (Objects.nonNull(loggedInUserIsp)) {
            ArrayList<ISPModel> modelArrayList = new ArrayList<>(ispdtoList.getData().stream().filter(q -> q.getName().equals(loggedInUserIsp)).collect(Collectors.toList()));
            ispdtoList.setData(modelArrayList);
        }

        return ispdtoList;
    }

    public void checkIspIdPresent(String ispId) {
        DBObject dbObject = mongoService.findById(ispId, INTERNET_SERVICE_PROVIDER);
        if(Objects.isNull(dbObject) || MapUtils.isEmpty(dbObject.toMap())) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "user ispId does not exist.");
        }
    }
}
