package com.actiontec.optim.platform.service;

import com.actiontec.optim.platform.repository.NCSNetworkRepo;
import com.actiontec.optim.platform.repository.ProvisionDataRepo;
import com.actiontec.optim.util.CustomStringUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.incs83.app.business.v2.ManageCommonService;
import com.incs83.app.entities.NCSNetwork;
import com.incs83.app.entities.ProvisionData;
import com.incs83.exceptions.handler.ValidationException;
import com.incs83.mt.DataAccessService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.erdtman.jcs.JsonCanonicalizer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Objects;
import java.util.Optional;

import static com.actiontec.optim.platform.constant.ApplicationConstants.STRING_EMPTY;

@Service
public class ProvisionService {

    private final Logger logger = LogManager.getLogger(this.getClass());

    @Autowired
    private ProvisionDataRepo provisionDataRepo;
    @Autowired
    private ObjectMapper objectMapper;
    @Autowired
    private DataAccessService dataAccessService;
    @Autowired
    private ManageCommonService manageCommonService;
    @Autowired
    private NCSNetworkRepo networkRepo;

    private Optional<ObjectNode> fetchShadowTemplate() {
        String path = "/templates/optim_shadow_default.json";
        try (InputStream in = getClass().getResourceAsStream(path)) {
            if (in == null) return Optional.empty();
            ObjectNode node = (ObjectNode) objectMapper.readTree(in);
            return Optional.of(node);
        } catch (Exception e) {
            return Optional.empty();
        }
    }

    private String canonicalizeJson(ObjectNode objectNode) {
        try {
            String jsonString = objectMapper.writeValueAsString(objectNode);
            JsonCanonicalizer jsonCanonicalizer = new JsonCanonicalizer(jsonString);
            return jsonCanonicalizer.getEncodedString();
        } catch (JsonProcessingException e) {
            throw new RuntimeException("Failed to serialize ObjectNode to JSON", e);
        } catch (IOException e) {
            throw new RuntimeException("Failed to canonicalize JSON", e);
        }
    }

    private String calculateMd5(String input) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] hashBytes = md.digest(input.getBytes(StandardCharsets.UTF_8));
            StringBuilder sb = new StringBuilder();
            for (byte b : hashBytes) {
                sb.append(String.format("%02x", b));
            }
            return sb.toString();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("MD5 algorithm not available", e);
        }
    }

    private ProvisionData createShadowFromTemplate(String networkId, String serial) {

        ObjectNode template = fetchShadowTemplate()
                .map(ObjectNode::deepCopy)
                .orElseThrow(() -> new RuntimeException("No template found"));

        String canonicalPayload = canonicalizeJson(template);
        String canonicalPayloadMd5 = calculateMd5(canonicalPayload);

        ProvisionData pd = new ProvisionData();
        pd.setNetworkId(networkId);
        pd.setSerial(serial);
        pd.setCacheVersion("0.0.1");
        pd.setCache(canonicalPayload);
        pd.setCacheMd5(canonicalPayloadMd5);
        pd.setShadowVersion("0.0.1");
        pd.setShadow(canonicalPayload);
        pd.setShadowMd5(canonicalPayloadMd5);

        dataAccessService.create(ProvisionData.class, pd);
        return pd;
    }

    public ProvisionData getProvisionDataByNetworkId(String networkId) throws Exception{
        NCSNetwork network = networkRepo.getNetworkById(networkId);
        if (ObjectUtils.isEmpty(network)) {
            throw new ValidationException(HttpStatus.NOT_FOUND.value(), "Network not found with ID: " + networkId);
        }
        String serial = manageCommonService.getGatewaySerialByUserId(networkId).orElse(STRING_EMPTY);
        if (CustomStringUtils.isEmpty(serial)) {
            logger.error("Gateway serial not found for network: {}", networkId);
            throw new ValidationException(HttpStatus.NOT_FOUND.value(), "Gateway serial not found for network: " + networkId);
        }
        ProvisionData pd = provisionDataRepo.findByNetworkIdAndSerial(networkId, serial);
        if (pd == null) {
            pd = createShadowFromTemplate(networkId, serial);
        }

        return pd;
    }

    public boolean updateShadow(ProvisionData provisionData, ObjectNode shadowObj) {
        String canonicalShadow = canonicalizeJson(shadowObj);
        String canonicalShadowMd5 = calculateMd5(canonicalShadow);

        provisionData.setShadow(canonicalShadow);
        provisionData.setShadowMd5(canonicalShadowMd5);
        provisionData.setTainted(true);
        dataAccessService.update(ProvisionData.class, provisionData);
        return true;
    }

    public ObjectNode getshadowDataDeepCopy(ProvisionData provisionData) throws Exception {
        ObjectNode shadowNode;
        try {
            shadowNode = (ObjectNode) objectMapper.readTree(provisionData.getShadow());
        } catch (IOException e) {
            throw new RuntimeException("Invalid shadow JSON in DB", e);
        }
        return shadowNode.deepCopy();
    }

    public ObjectNode getShadow(String networkId, String serial) {

        ProvisionData pd = provisionDataRepo.findByNetworkIdAndSerial(networkId, serial);
        if (pd == null) {
            pd = createShadowFromTemplate(networkId, serial);
        }

        ObjectNode shadowNode;
        try {
            shadowNode = (ObjectNode) objectMapper.readTree(pd.getShadow());
        } catch (IOException e) {
            throw new RuntimeException("Invalid shadow JSON in DB", e);
        }
        return shadowNode;
    }

    public boolean setShadow(String networkId, String serial, ObjectNode shadowObj) {
        ProvisionData pd = provisionDataRepo.findByNetworkIdAndSerial(networkId, serial);
        if (pd == null) {
            logger.info("No shadow found for network id {} and serial {}", networkId, serial);
            return false;
        }

        String canonicalShadow = canonicalizeJson(shadowObj);
        String canonicalShadowMd5 = calculateMd5(canonicalShadow);

        pd.setShadow(canonicalShadow);
        pd.setShadowMd5(canonicalShadowMd5);
        pd.setTainted(true);
        dataAccessService.update(ProvisionData.class, pd);
        return true;
    }
}
