package com.actiontec.optim.platform.service;

import com.actiontec.optim.platform.api.v5.exception.OptimApiException;
import com.actiontec.optim.platform.api.v6.dto.LegalDocumentCreateRequest;
import com.actiontec.optim.platform.api.v6.dto.LegalDocumentDetailsResponse;
import com.actiontec.optim.platform.api.v6.dto.LegalDocumentResponse;
import com.actiontec.optim.platform.api.v6.dto.LegalDocumentUpdateRequest;
import com.actiontec.optim.platform.constant.ApplicationConstants;
import com.actiontec.optim.platform.exception.NoSuchEntityException;
import com.actiontec.optim.platform.model.FwFile;
import com.actiontec.optim.platform.repository.LegalDocumentRepo;
import com.actiontec.optim.util.CustomStringUtils;
import com.incs83.app.entities.LegalDocument;
import com.incs83.app.entities.OptimFile;
import com.incs83.exceptions.handler.ValidationException;
import com.incs83.util.CommonUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.apache.commons.collections4.CollectionUtils;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class LegalDocumentService {
    private final Logger logger = LogManager.getLogger(this.getClass());

    @Autowired
    private LegalDocumentRepo legalDocumentRepo;

    @Autowired
    private FwFileService fwFileService;
    public List<LegalDocumentDetailsResponse> getAllLegalDocuments(String serverUrl) throws Exception {
        if (!CommonUtils.isSysAdmin()) {
            throw new ValidationException(HttpStatus.FORBIDDEN.value(), "Insufficient permissions to access this resource");
        }
        List<LegalDocument> legalDocumentList = legalDocumentRepo.getAll();

        if (CollectionUtils.isEmpty(legalDocumentList)) {
            return new ArrayList<>();

        }

        return legalDocumentList.stream()
                .map(doc -> toDetailsResponse(doc, serverUrl))
                .collect(Collectors.toList());
    }

    public LegalDocumentResponse createLegalDocument(LegalDocumentCreateRequest request) throws Exception{
        if (!CommonUtils.isSysAdmin()) {
            throw new ValidationException(HttpStatus.FORBIDDEN.value(), "Insufficient permissions to access this resource");
        }

        if (legalDocumentRepo.existsByLegalDocumentName(request.getName())) {
            throw new OptimApiException(HttpStatus.BAD_REQUEST, "Cannot create legal document. Legal document name already exists.");
        }

        LegalDocument doc = new LegalDocument();
        doc.setId(CommonUtils.generateUUID());
        doc.setName(request.getName());
        doc.setMd5Checksum(request.getMd5Checksum());
        doc.setCreatedAt(new Timestamp(System.currentTimeMillis()));
        doc.setUpdatedAt(new Timestamp(System.currentTimeMillis()));

        OptimFile optimFile = new OptimFile();
        optimFile.setId(CommonUtils.generateUUID());
        optimFile.setFileName(request.getFileName());
        optimFile.setType(ApplicationConstants.LEGAL_DOCUMENT_TYPE);
        optimFile.setFileStatus(ApplicationConstants.FILE_STATUS_INITIAL);

        doc.setOptimFile(optimFile);
        legalDocumentRepo.create(doc);

        return new LegalDocumentResponse(doc.getId(), CustomStringUtils.toBatchEquipmentFileEntry(optimFile.getId()));
    }

    public LegalDocumentDetailsResponse getLegalDocumentById(String documentId, String serverUrl) throws Exception{
        if (!CommonUtils.isSysAdmin()) {
            throw new ValidationException(HttpStatus.FORBIDDEN.value(), "Insufficient permissions to access this resource");
        }
        LegalDocument doc = legalDocumentRepo.getById(documentId);

        if(doc == null) {
            throw new NoSuchEntityException();
        }

        return toDetailsResponse(doc, serverUrl);
    }

    public boolean updateLegalDocument(String documentId, LegalDocumentUpdateRequest request) throws Exception {
        if (!CommonUtils.isSysAdmin()) {
            throw new ValidationException(HttpStatus.FORBIDDEN.value(), "Insufficient permissions to access this resource");
        }
        LegalDocument doc = legalDocumentRepo.getById(documentId);

        if(doc == null) {
            throw new NoSuchEntityException();
        }

        doc.setMd5Checksum(request.getMd5Checksum());
        doc.setVersion(doc.getVersion() + 1);
        doc.setUpdatedAt(new Timestamp(System.currentTimeMillis()));
        doc.getOptimFile().setFileName(request.getFileName());
        legalDocumentRepo.update(doc);

        return true;
    }

    public boolean deleteLegalDocument(String documentId) throws Exception {
        if (!CommonUtils.isSysAdmin()) {
            throw new ValidationException(HttpStatus.FORBIDDEN.value(), "Insufficient permissions to access this resource");
        }
        LegalDocument doc = legalDocumentRepo.getById(documentId);

        if(doc == null) {
            throw new NoSuchEntityException();
        }
        legalDocumentRepo.delete(doc);

        return true;
    }

    public FwFile unauthGetFwFileByDocumentId(String documentId) throws Exception {
        LegalDocument doc = legalDocumentRepo.getById(documentId);

        if(doc == null) {
            throw new NoSuchEntityException();
        }
        FwFile fwFile = fwFileService.findFwFileById(doc.getOptimFile().getId());
        if (fwFile == null) {
            logger.info("failed to find file, fileId:[{}]", doc.getOptimFile().getId());
            throw new OptimApiException(HttpStatus.BAD_REQUEST, "Get file fail documentId=" + doc.getOptimFile().getId());
        }

        return fwFile;
    }

    private LegalDocumentDetailsResponse toDetailsResponse(LegalDocument doc, String serverUrl) {
        return new LegalDocumentDetailsResponse(
                doc.getId(),
                CustomStringUtils.toBatchEquipmentFileEntry(doc.getOptimFile().getId()),
                doc.getName(),
                doc.getVersion(),
                serverUrl + "/legalDocument/" + doc.getId(),
                doc.getOptimFile() != null ? doc.getOptimFile().getFileName() : null,
                doc.getMd5Checksum(),
                doc.getUpdatedAt() != null ? doc.getUpdatedAt().getTime() : null
        );
    }
}