package com.actiontec.optim.platform.service;

import com.actiontec.optim.platform.adapter.At3Adapter;
import com.actiontec.optim.platform.constant.ApplicationConstants;
import com.actiontec.optim.platform.exception.NoSuchEntityException;
import com.actiontec.optim.platform.exception.RpcException;
import com.actiontec.optim.platform.mapper.SmmContainerMapper;
import com.actiontec.optim.platform.model.SmmApplication;
import com.actiontec.optim.platform.model.SmmContainer;
import com.actiontec.optim.platform.api.v5.model.SmmContainerRequest;
import com.actiontec.optim.platform.model.SmmContainerPayloadService;
import com.actiontec.optim.platform.repository.SmmContainerRepo;
import com.actiontec.optim.rpc.core.RpcResponse;
import com.actiontec.optim.rpc.facade.RpcServiceFacade;
import com.actiontec.optim.service.CpeRpcService;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.incs83.app.business.v2.ManageCommonService;
import com.incs83.app.entities.Equipment;
import com.incs83.app.service.data.MongoServiceImpl;
import com.incs83.constants.ApplicationCommonConstants;
import com.incs83.exceptions.handler.ValidationException;
import com.incs83.service.CommonService;
import com.incs83.util.CommonUtils;
import com.mongodb.BasicDBObject;
import com.mongodb.DBObject;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;

import static com.incs83.app.constants.misc.ActiontecConstants.EQUIPMENT_DIAGNOSTIC_CONFIG;
import static com.incs83.app.constants.misc.ActiontecConstants.RPC_POLL_COUNT;
import static com.incs83.app.constants.misc.ApplicationConstants.DESC;
import static com.incs83.app.constants.misc.ApplicationConstants.TIMESTAMP;
import static com.incs83.app.constants.misc.RpcConstants.*;

@Service
public class SmmContainerService {
    private final Logger logger = LogManager.getLogger(this.getClass());
    @Autowired
    private  CpeRpcService cpeRpcService;
    @Autowired
    private At3Adapter at3Adapter;
    @Autowired
    private SmmAppService smmAppService;
    @Autowired
    private SmmServicesService smmServicesService;
    @Autowired
    private SmmContainerMapper smmContainerMapper;
    @Autowired
    private MongoServiceImpl mongoService;
    @Autowired
    private ObjectMapper objectMapper;
    @Autowired
    private CommonService commonService;
    @Autowired
    private ManageCommonService manageCommonService;
    @Autowired
    private SmmContainerRepo smmContainerRepo;
    @Autowired
    private RpcServiceFacade rpcServiceFacade;

    public SmmContainerPayloadService getPayloadService(String userId, String equipmentId, SmmContainerRequest.Service service) throws Exception {
        String version = Objects.nonNull(service.getVersion())? service.getVersion(): smmServicesService.getSmmSeviceById(service.getServiceId()).getProductionVersion();
        HashMap<String, Object> params = new HashMap<>();
        params.put("userId", userId);
        params.put("serialNumber", equipmentId);
        BasicDBObject mongoFieldOptions = new BasicDBObject();
        mongoFieldOptions.clear();
        mongoFieldOptions.put("_id", 0);
        DBObject aPDetails = mongoService.findOne(params, new HashMap<>(), ApplicationCommonConstants.AP_DETAIL, TIMESTAMP, DESC, mongoFieldOptions);

        if (Objects.isNull(aPDetails) || Objects.isNull(aPDetails.get("modelName")))
            throw new NoSuchEntityException();

        String modelName = aPDetails.get("modelName").toString();
        List<SmmApplication> smmApplicationList = smmAppService.findAppByVersion(service.getServiceId(), version);
        smmApplicationList = smmApplicationList.stream().filter(p -> p.getDependencies().stream().anyMatch(dependency -> dependency.getEquipmentTypeId().equals(modelName))).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(smmApplicationList)) {
            throw new NoSuchEntityException();
        }

        return smmContainerMapper.toPayloadService(smmApplicationList.get(0), service);
    }

    private Map<String, Object> getPayLoad(String userId, String equipmentId, SmmContainerRequest smmContainerRequest) throws Exception {
        Map<String, Object> payloadMap = new HashMap<>();
        payloadMap.put("actionId", CommonUtils.generateUUID());

        if (smmContainerRequest.getActions().getStatusAction().equals("None") || smmContainerRequest.getActions().getActionFlagsValue()) {
            payloadMap.put("actions", objectMapper.convertValue(smmContainerRequest.getActions(), HashMap.class));
            if (Objects.nonNull(smmContainerRequest.getService())) {
                payloadMap.put("service", objectMapper.convertValue(getPayloadService(userId, equipmentId, smmContainerRequest.getService()), HashMap.class));
            }
        } else {
            HashMap<String, Object> actions = new HashMap<>();
            actions.put("statusAction", smmContainerRequest.getActions().getStatusAction());
            payloadMap.put("actions", objectMapper.convertValue(actions, HashMap.class));
        }

        return  payloadMap;
    }

    private int getRpcTimeout() {
        HashMap<String, String> equipmentProps = commonService.read(EQUIPMENT_DIAGNOSTIC_CONFIG);
        return Integer.parseInt(equipmentProps.get(RPC_POLL_COUNT));
    }

    public List<SmmContainer> getAllContainers(String stn, String equipmentId, boolean rpcRequest) throws Exception {
        Equipment userEquipment = manageCommonService.getUserAPFromSubscriberIdOrApId(stn);
        if(StringUtils.isEmpty(userEquipment.getRgwSerial())) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "RGW Serial cannot be empty");
        }

        manageCommonService.subscriberDataExistInMongo(userEquipment);
        manageCommonService.subscriberGroupMisMatch(userEquipment);
        manageCommonService.checkAPSerialNumBelongsToSubscriber(equipmentId, userEquipment);
        List<SmmContainer> smmContainerList = new ArrayList<>();
        String networkId = at3Adapter.getRgwSerialByStn(stn);

        if(rpcRequest) {
            int RPC_TIMEOUT_SECONDS = getRpcTimeout();

            try {
                CompletableFuture<RpcResponse> future = rpcServiceFacade.sendContainerActionRequest(networkId, equipmentId, CPE_API_METHOD_GET, null, false, new HashMap<>(), RPC_TIMEOUT_SECONDS);

                RpcResponse rpcResponse = future.get(RPC_TIMEOUT_SECONDS, TimeUnit.SECONDS);

                Map<String, Object> data = rpcResponse.getData();
                int rpcRetCode = Integer.parseInt(data.get("code").toString());
                if (rpcRetCode != 200) {
                    throw new RpcException(HttpStatus.BAD_REQUEST.value(), "RPC returned error code: " + rpcRetCode);
                }

                if (data.get("payload") != null) {
                    smmContainerList = objectMapper.convertValue(data.get("payload"), at3Adapter.getCollectionType(List.class, SmmContainer.class));

                }
            } catch (ExecutionException e) {
                Throwable cause = e.getCause();
                if (cause instanceof TimeoutException) {
                    logger.error("Container API timeout for network:[{}]", networkId);
                    throw new RpcException(HttpStatus.REQUEST_TIMEOUT.value(), "Request timeout");
                } else {
                    logger.error("Container API execution failed for network:[{}]", networkId);
                    throw new RpcException(HttpStatus.BAD_REQUEST.value(), e.getMessage());
                }
            } catch (Exception e) {
                logger.error("Container API error for network:[{}]", networkId);
                throw new RpcException(HttpStatus.BAD_REQUEST.value(), "Unexpected error");
            }
        } else {
            smmContainerList = smmContainerRepo.findByUserIdAndSerial(networkId, equipmentId);
            smmContainerList = smmContainerList.size() != 0? smmContainerList.stream().filter(p->p.getStatus().equals("Deleted") == false).collect(Collectors.toList()) : smmContainerList;
        }

        return  smmContainerList;
    }

    public String createContainer(String stn, String equipmentId, SmmContainerRequest smmContainerRequest) throws Exception{
        Equipment userEquipment = manageCommonService.getUserAPFromSubscriberIdOrApId(stn);
        if(Objects.isNull(userEquipment.getRgwSerial()) || userEquipment.getRgwSerial().isEmpty())
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "RGW Serial cannot be empty");
        manageCommonService.subscriberDataExistInMongo(userEquipment);
        manageCommonService.subscriberGroupMisMatch(userEquipment);
        manageCommonService.checkAPSerialNumBelongsToSubscriber(equipmentId, userEquipment);
        String networkId = at3Adapter.getRgwSerialByStn(stn);
        String id = null;

        HashMap<String, Object> payloadMap = new HashMap<>();
        payloadMap.put("id", smmContainerRequest.getService().getServiceId());
        payloadMap.put("actions", objectMapper.convertValue(smmContainerRequest.getActions(), HashMap.class));
        payloadMap.put("service", objectMapper.convertValue(getPayloadService(networkId, equipmentId, smmContainerRequest.getService()), HashMap.class));
        payloadMap.put("actionId", CommonUtils.generateUUID());
        int RPC_TIMEOUT_SECONDS = getRpcTimeout();

        try {
            CompletableFuture<RpcResponse> future = rpcServiceFacade.sendContainerActionRequest(networkId, equipmentId, CPE_API_METHOD_POST, null, false, payloadMap, RPC_TIMEOUT_SECONDS);

            RpcResponse rpcResponse = future.get(RPC_TIMEOUT_SECONDS, TimeUnit.SECONDS);

            Map<String, Object> data = rpcResponse.getData();
            int rpcRetCode = Integer.parseInt(data.get("code").toString());
            if (rpcRetCode != 200) {
                throw new RpcException(HttpStatus.BAD_REQUEST.value(), "RPC returned error code: " + rpcRetCode);
            }

            if (data.get("payload") != null) {
                List<HashMap<String, String>> payloadList = objectMapper.convertValue(data.get("payload"), new TypeReference<List<HashMap<String, String>>>(){});
                id = String.valueOf(payloadList.get(0).get("id"));
            }
        } catch (ExecutionException e) {
            Throwable cause = e.getCause();
            if (cause instanceof TimeoutException) {
                logger.error("Container API timeout for network:[{}]", networkId);
                throw new RpcException(HttpStatus.REQUEST_TIMEOUT.value(), "Request timeout");
            } else {
                logger.error("Container API execution failed for network:[{}]", networkId);
                throw new RpcException(HttpStatus.BAD_REQUEST.value(), e.getMessage());
            }
        } catch (Exception e) {
            logger.error("Container API error for network:[{}]", networkId);
            throw new RpcException(HttpStatus.BAD_REQUEST.value(), "Unexpected error");
        }

        return id;
    }

    public List<SmmContainer> getContainerById(String stn, String equipmentId, String containerId, boolean rpcRequest) throws Exception {
        Equipment userEquipment = manageCommonService.getUserAPFromSubscriberIdOrApId(stn);
        if(Objects.isNull(userEquipment.getRgwSerial()) || userEquipment.getRgwSerial().isEmpty())
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "RGW Serial cannot be empty");
        manageCommonService.subscriberDataExistInMongo(userEquipment);
        manageCommonService.subscriberGroupMisMatch(userEquipment);
        manageCommonService.checkAPSerialNumBelongsToSubscriber(equipmentId, userEquipment);
        String networkId = at3Adapter.getRgwSerialByStn(stn);

        List<SmmContainer> smmContainerList = new ArrayList<>();

        if(rpcRequest) {
            int RPC_TIMEOUT_SECONDS = getRpcTimeout();

            try {
                CompletableFuture<RpcResponse> future = rpcServiceFacade.sendContainerActionRequest(networkId, equipmentId, CPE_API_METHOD_GET, containerId, false, new HashMap<>(), RPC_TIMEOUT_SECONDS);

                RpcResponse rpcResponse = future.get(RPC_TIMEOUT_SECONDS, TimeUnit.SECONDS);

                Map<String, Object> data = rpcResponse.getData();
                int rpcRetCode = Integer.parseInt(data.get("code").toString());
                if (rpcRetCode != 200) {
                    throw new RpcException(HttpStatus.BAD_REQUEST.value(), "RPC returned error code: " + rpcRetCode);
                }

                if (data.get("payload") != null) {
                    smmContainerList = objectMapper.convertValue(data.get("payload"), at3Adapter.getCollectionType(List.class, SmmContainer.class));
                }
            } catch (ExecutionException e) {
                Throwable cause = e.getCause();
                if (cause instanceof TimeoutException) {
                    logger.error("Container API timeout for network:[{}]", networkId);
                    throw new RpcException(HttpStatus.REQUEST_TIMEOUT.value(), "Request timeout");
                } else {
                    logger.error("Container API execution failed for network:[{}]", networkId);
                    throw new RpcException(HttpStatus.BAD_REQUEST.value(), e.getMessage());
                }
            } catch (Exception e) {
                logger.error("Container API error for network:[{}]", networkId);
                throw new RpcException(HttpStatus.BAD_REQUEST.value(), "Unexpected error");
            }
        } else {
            smmContainerList = smmContainerRepo.findByUserIdAndSerialAndContainerId(networkId, equipmentId, containerId);
            smmContainerList = smmContainerList.size() != 0 ? smmContainerList.stream().filter(p->p.getStatus().equals("Deleted") == false).collect(Collectors.toList()) : smmContainerList;
        }

        return  smmContainerList;
    }

    public void removeContainer(String stn, String equipmentId, String containerId) throws  Exception{
        Equipment userEquipment = manageCommonService.getUserAPFromSubscriberIdOrApId(stn);
        if(Objects.isNull(userEquipment.getRgwSerial()) || userEquipment.getRgwSerial().isEmpty())
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "RGW Serial cannot be empty");
        manageCommonService.subscriberDataExistInMongo(userEquipment);
        manageCommonService.subscriberGroupMisMatch(userEquipment);
        manageCommonService.checkAPSerialNumBelongsToSubscriber(equipmentId, userEquipment);
        String networkId = at3Adapter.getRgwSerialByStn(stn);
        int RPC_TIMEOUT_SECONDS = getRpcTimeout();

        try {
            CompletableFuture<RpcResponse> future = rpcServiceFacade.sendContainerActionRequest(networkId, equipmentId, CPE_API_METHOD_DELETE, containerId, false, new HashMap<>(), RPC_TIMEOUT_SECONDS);

            RpcResponse rpcResponse = future.get(RPC_TIMEOUT_SECONDS, TimeUnit.SECONDS);

            Map<String, Object> data = rpcResponse.getData();
            int rpcRetCode = Integer.parseInt(data.get("code").toString());
            if (rpcRetCode != 200) {
                throw new RpcException(HttpStatus.BAD_REQUEST.value(), "RPC returned error code: " + rpcRetCode);
            }
        } catch (ExecutionException e) {
            Throwable cause = e.getCause();
            if (cause instanceof TimeoutException) {
                logger.error("Container API timeout for network:[{}]", networkId);
                throw new RpcException(HttpStatus.REQUEST_TIMEOUT.value(), "Request timeout");
            } else {
                logger.error("Container API execution failed for network:[{}]", networkId);
                throw new RpcException(HttpStatus.BAD_REQUEST.value(), e.getMessage());
            }
        } catch (Exception e) {
            logger.error("Container API error for network:[{}]", networkId);
            throw new RpcException(HttpStatus.BAD_REQUEST.value(), "Unexpected error");
        }
    }

    public String postContainer(String stn, String equipmentId, String containerId, SmmContainerRequest smmContainerRequest) throws Exception{
        Equipment userEquipment = manageCommonService.getUserAPFromSubscriberIdOrApId(stn);
        if(Objects.isNull(userEquipment.getRgwSerial()) || userEquipment.getRgwSerial().isEmpty())
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "RGW Serial cannot be empty");
        manageCommonService.subscriberDataExistInMongo(userEquipment);
        manageCommonService.subscriberGroupMisMatch(userEquipment);
        manageCommonService.checkAPSerialNumBelongsToSubscriber(equipmentId, userEquipment);
        String networkId = at3Adapter.getRgwSerialByStn(stn);

        String id = null;
        Map<String, Object> payloadMap = getPayLoad(networkId, equipmentId, smmContainerRequest);

        int RPC_TIMEOUT_SECONDS = getRpcTimeout();

        try {
            CompletableFuture<RpcResponse> future = rpcServiceFacade.sendContainerActionRequest(networkId, equipmentId, CPE_API_METHOD_POST, containerId, true, payloadMap, RPC_TIMEOUT_SECONDS);

            RpcResponse rpcResponse = future.get(RPC_TIMEOUT_SECONDS, TimeUnit.SECONDS);

            Map<String, Object> data = rpcResponse.getData();
            int rpcRetCode = Integer.parseInt(data.get("code").toString());
            if (rpcRetCode != 200) {
                throw new RpcException(HttpStatus.BAD_REQUEST.value(), "RPC returned error code: " + rpcRetCode);
            }

            if (data.get("payload") != null) {
                List<HashMap<String, String>> payloadList = objectMapper.convertValue(data.get("payload"), new TypeReference<List<HashMap<String, String>>>(){});
                id = String.valueOf(payloadList.get(0).get("id"));
            }
        } catch (ExecutionException e) {
            Throwable cause = e.getCause();
            if (cause instanceof TimeoutException) {
                logger.error("Container API timeout for network:[{}]", networkId);
                throw new RpcException(HttpStatus.REQUEST_TIMEOUT.value(), "Request timeout");
            } else {
                logger.error("Container API execution failed for network:[{}]", networkId);
                throw new RpcException(HttpStatus.BAD_REQUEST.value(), e.getMessage());
            }
        } catch (Exception e) {
            logger.error("Container API error for network:[{}]", networkId);
            throw new RpcException(HttpStatus.BAD_REQUEST.value(), "Unexpected error");
        }

        return  id;
    }
}
