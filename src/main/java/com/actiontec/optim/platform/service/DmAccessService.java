package com.actiontec.optim.platform.service;

import com.actiontec.optim.platform.exception.RpcException;
import com.actiontec.optim.rpc.core.RpcResponse;
import com.actiontec.optim.rpc.facade.RpcServiceFacade;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.incs83.app.annotation.Auditable;
import com.incs83.app.constants.misc.AuditorConstants;
import com.incs83.app.utils.EquipmentUtils;
import com.incs83.service.CommonService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

import static com.incs83.app.constants.misc.ActiontecConstants.EQUIPMENT_DIAGNOSTIC_CONFIG;
import static com.incs83.app.constants.misc.ActiontecConstants.RPC_POLL_COUNT;

@Service
public class DmAccessService {

    private final Logger logger = LogManager.getLogger(this.getClass());

    @Autowired
    private ObjectMapper mapper;

    @Autowired
    private CommonService commonService;

    @Autowired
    private EquipmentUtils equipmentUtils;

    @Autowired
    private RpcServiceFacade rpcServiceFacade;

    @Autowired
    private ObjectMapper objectMapper;

    @Auditable(method = RequestMethod.POST, operation = AuditorConstants.DATA_MODEL_ACCESS)
    public Map<String, Object> doDmAccess(String equipmentId, Map<String, Object> payloadMap) throws Exception {
        String networkId = equipmentUtils.getEquipmentUserId(equipmentId);

        HashMap<String, String> equipmentProps = commonService.read(EQUIPMENT_DIAGNOSTIC_CONFIG);
        int RPC_TIMEOUT_SECONDS =  Integer.parseInt(equipmentProps.get(RPC_POLL_COUNT));

        try {
            CompletableFuture<RpcResponse> future = rpcServiceFacade.sendDmAccessRequest(networkId, equipmentId, payloadMap, RPC_TIMEOUT_SECONDS);

            RpcResponse rpcResponse = future.get(RPC_TIMEOUT_SECONDS, TimeUnit.SECONDS);

            Map<String, Object> data = rpcResponse.getData();
            int rpcRetCode = Integer.parseInt(data.get("code").toString());
            if (rpcRetCode != 200) {
                throw new RpcException(HttpStatus.BAD_REQUEST.value(), "RPC returned error code: " + rpcRetCode);
            }

            Map<String, Object> result;
            if (data.get("payload") != null) {
                List<HashMap<String, Object>> payloadList = objectMapper.convertValue(data.get("payload"), new TypeReference<List<HashMap<String, Object>>>() {});
                result = payloadList.get(0);
                result.put("lastUpdateTime", data.get("timestamp"));
            } else {
                result = new HashMap<>();
            }
            return result;

        } catch (ExecutionException e) {
            Throwable cause = e.getCause();
            if (cause instanceof TimeoutException) {
                logger.error("DmAccess API timeout for network:[{}]", networkId);
                throw new RpcException(HttpStatus.REQUEST_TIMEOUT.value(), "Request timeout");
            } else {
                logger.error("DmAccess API execution failed for network:[{}]", networkId);
                throw new RpcException(HttpStatus.BAD_REQUEST.value(), e.getMessage());
            }
        } catch (Exception e) {
            logger.error("DmAccess API error for network:[{}]", networkId);
            throw new RpcException(HttpStatus.BAD_REQUEST.value(), "Unexpected error");
        }
    }
}
