package com.actiontec.optim.platform.service;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.eclipse.paho.client.mqttv3.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import com.actiontec.optim.platform.api.v5.exception.OptimApiException;
import com.actiontec.optim.platform.api.v6.dto.CustomerProfileDTO;
import com.actiontec.optim.platform.api.v6.dto.CustomerProfileRequest;
import com.actiontec.optim.platform.api.v6.dto.IdDTO;
import com.actiontec.optim.platform.api.v6.dto.PaginationDTO;
import com.actiontec.optim.platform.api.v6.dto.PaginationResponse;
import com.actiontec.optim.platform.repository.EquipmentModelFirmwareRepo;
import com.actiontec.optim.platform.repository.EquipmentModelIspConfigRepo;
import com.actiontec.optim.platform.repository.EquipmentModelRepo;
import com.actiontec.optim.platform.repository.FirmwareRepo;
import com.incs83.app.business.v2.ISPService;
import com.incs83.app.business.v2.ManageCommonService;
import com.incs83.app.entities.EquipmentModel;
import com.incs83.app.entities.EquipmentModelIspConfig;
import com.incs83.app.entities.Firmware;
import com.incs83.app.enums.ApiResponseEnum;
import com.incs83.app.responsedto.v2.isp.ISPDTO;
import com.incs83.dao.Page;
import com.incs83.exceptions.handler.ValidationException;
import com.incs83.util.CommonUtils;

@Service
public class CustomerProfileService {

    private final Logger logger = LogManager.getLogger(this.getClass());

    @Autowired
    EquipmentModelIspConfigRepo equipmentModelIspConfigRepo;

    @Autowired
    EquipmentModelFirmwareRepo equipmentModelFirmwareRepo;

    @Autowired
    private ManageCommonService manageCommonService;

    @Autowired
    private EquipmentModelRepo equipmentModelRepo;

    @Autowired
    private FirmwareRepo firmwareRepo;

    @Autowired
    private ISPService ispService;

    public PaginationResponse<CustomerProfileDTO> getAllCustomerProfiles(
            CustomerProfileRequest request) throws Exception {

        List<CustomerProfileDTO> customerProfiles = null;

        PaginationResponse<CustomerProfileDTO> response = new PaginationResponse<>();
        Page<EquipmentModelIspConfig> page = null;

        if (CommonUtils.isSysAdmin()) {
            page = equipmentModelIspConfigRepo.findAll(request.getOffset(), request.getLimit());
        } else if (CommonUtils.isGroupAdmin()) {
            String ispId = manageCommonService.getIspByGroupId(CommonUtils.getGroupIdOfLoggedInUser());
            page = equipmentModelIspConfigRepo.findByIspId(ispId, request.getOffset(), request.getLimit());
        }
        // Handle empty or null page case
        if (Objects.isNull(page) || CollectionUtils.isEmpty(page.getObjects())) {
            response.setData(Collections.emptyList());
            PaginationDTO pagination = PaginationDTO.builder()
                    .listSize(response.getData().size())
                    .total(0)
                    .limit(Objects.isNull(request.getLimit()) ? 50 : request.getLimit())
                    .offset(Objects.isNull(request.getOffset()) ? 0 : request.getOffset())
                    .build();

            response.setPagination(pagination);
            return response;
        }

        // map build customerProfiles
        customerProfiles = page.getObjects().stream()
                .map(config -> {
                    String requiredFirmwareId = !Objects.isNull(config.getMinVersionFirmware())
                            ? config.getMinVersionFirmware().getVersion()
                            : null;

                    return CustomerProfileDTO.builder()
                            .id(config.getId())
                            .ispId(config.getIspId())
                            .modelId(config.getEquipmentModelId())
                            .requiredFirmwareId(requiredFirmwareId)
                            .lastChangedTime(config.getUpdatedAt().getTime())
                            .build();
                }).collect(Collectors.toList());

        response.setData(customerProfiles);

        PaginationDTO pagination = PaginationDTO.builder()
                .listSize(response.getData().size())
                .total(page.getTotalCount())
                .limit(Objects.isNull(request.getLimit()) ? 50 : request.getLimit())
                .offset(Objects.isNull(request.getOffset()) ? 0 : request.getOffset())
                .build();

        response.setPagination(pagination);
        return response;
    }

    public CustomerProfileDTO getCustomerProfile(String profileId) throws Exception {

        if(StringUtils.isBlank(profileId)) {
            throw new ValidationException(
                    ApiResponseEnum.CUSTOMER_PROFILE_INVALID_PROFILE_ID.getCode(),
                    ApiResponseEnum.CUSTOMER_PROFILE_INVALID_PROFILE_ID.getMessage());
        }

        EquipmentModelIspConfig config = equipmentModelIspConfigRepo.findById(profileId);

        if (Objects.isNull(config)) {
            throw new OptimApiException(HttpStatus.NOT_FOUND,
                    ApiResponseEnum.CUSTOMER_PROFILE_NOT_FOUND.getMessage());
        }

        return CustomerProfileDTO.builder()
                .id(config.getId())
                .ispId(config.getIspId())
                .modelId(config.getEquipmentModelId())
                .requiredFirmwareId(config.getMinVersionFirmware().getVersion())
                .lastChangedTime(config.getUpdatedAt().getTime())
                .build();
    }

    public IdDTO createCustomerProfile(CustomerProfileRequest request) throws Exception {

        // validate isp exist
        validateAndGetIsp(request.getIspId());

        // validate model exist
        validateAndGetEquipmentModel(request.getModelId()).get();

        // check firmware id exist
        Firmware fwImage = validateAndGetFirmware(request.getRequiredFirmwareId(),
                request.getModelId()).get();

        String id = CommonUtils.generateUUID();

        // check if config already exists
        if (isEquipmentModelIspConfigExist(request.getIspId(), request.getModelId())) {
            logger.info("CustomerProfile already exists for id: {}", id);
            throw new ValidationException(
                    ApiResponseEnum.CUSTOMER_PROFILE_CONFLICT.getCode(),
                    ApiResponseEnum.CUSTOMER_PROFILE_CONFLICT.getMessage());
        }

        // create equipment model isp config
        if (!equipmentModelIspConfigRepo.create(id, request.getIspId(),
                request.getModelId(), fwImage, CommonUtils.getUserIdOfLoggedInUser())) {
            logger.warn("Failed to create CustomerProfile for id: {}, ispId: {}, modelId: {}, firmwareId: {}",
                    id, request.getIspId(), request.getModelId(), request.getRequiredFirmwareId());
            throw new Exception("Failed to create CustomerProfile");
        }

        return IdDTO.builder().id(id).build();
    }

    public IdDTO updateCustomerProfile(String profileId, CustomerProfileRequest request) throws Exception {
        if(StringUtils.isBlank(profileId)) {
            throw new ValidationException(
                    ApiResponseEnum.CUSTOMER_PROFILE_INVALID_PROFILE_ID.getCode(),
                    ApiResponseEnum.CUSTOMER_PROFILE_INVALID_PROFILE_ID.getMessage());
        }
        // validate isp exist
        validateAndGetIsp(request.getIspId());

        // validate model exist
        validateAndGetEquipmentModel(request.getModelId()).get();

        // check firmware id exist
        Firmware firmware = validateAndGetFirmware(request.getRequiredFirmwareId(),
                request.getModelId()).get();

        // check if config already exists
        List<EquipmentModelIspConfig> configs = equipmentModelIspConfigRepo
                .findByIspIdAndEquipmentModelId(request.getIspId(), request.getModelId());

        // handle conflict if ispId and modelId exist
        configs.stream()
            .filter(config -> !Objects.isNull(config))
            .filter(config -> !profileId.equals(config.getId()))
            .findAny()
            .ifPresent(config -> {
                logger.info("CustomerProfile already exists for id: {}", profileId);
                throw new ValidationException(
                    ApiResponseEnum.CUSTOMER_PROFILE_CONFLICT.getCode(),
                    ApiResponseEnum.CUSTOMER_PROFILE_CONFLICT.getMessage());
            });

        EquipmentModelIspConfig config = equipmentModelIspConfigRepo
                .update(profileId, request.getModelId(), request.getIspId(),
                        firmware, CommonUtils.getUserIdOfLoggedInUser());

        return IdDTO.builder().id(config.getId()).build();
    }

    public IdDTO deleteCustomerProfile(String profileId) throws Exception {
        
        if(StringUtils.isBlank(profileId)) {
            throw new ValidationException(
                    ApiResponseEnum.CUSTOMER_PROFILE_INVALID_PROFILE_ID.getCode(),
                    ApiResponseEnum.CUSTOMER_PROFILE_INVALID_PROFILE_ID.getMessage());
        }

        if (Objects.isNull(equipmentModelIspConfigRepo.findById(profileId))) {
            throw new OptimApiException(HttpStatus.NOT_FOUND,
                    ApiResponseEnum.CUSTOMER_PROFILE_NOT_FOUND.getMessage());
        }

        if (equipmentModelIspConfigRepo.deleteById(profileId)) {
            return IdDTO.builder().id(profileId).build();
        }

        throw new Exception("Delete CustomerProfile failed");
    }

    private Optional<ISPDTO> validateAndGetIsp(String ispId) throws Exception {
        ISPDTO ispDto = null;
        try {
            ispDto = ispService.getISPById(ispId);
        } catch (Exception e) {
        }

        if (Objects.isNull(ispDto)) {
            throw new ValidationException(
                    ApiResponseEnum.CUSTOMER_PROFILE_INVALID_ISP_ID.getCode(),
                    ApiResponseEnum.CUSTOMER_PROFILE_INVALID_ISP_ID.getMessage());
        }
        return Optional.of(ispDto);
    }

    private Optional<EquipmentModel> validateAndGetEquipmentModel(String modelId) throws Exception {
        EquipmentModel equipmentModel = equipmentModelRepo.findEquipmentModelById(modelId);
        if (Objects.isNull(equipmentModel)) {
            throw new ValidationException(
                    ApiResponseEnum.CUSTOMER_PROFILE_INVALID_MODEL_ID.getCode(),
                    ApiResponseEnum.CUSTOMER_PROFILE_INVALID_MODEL_ID.getMessage());
        }

        return Optional.of(equipmentModel);
    }

    private Optional<Firmware> validateAndGetFirmware(String firmwareId, String equipmentModelId)
            throws Exception {

        List<Firmware> firmwareList = firmwareRepo.findByModelId(equipmentModelId);

        logger.info("Model Id: {}, Firmware sizes: {}", equipmentModelId, firmwareList.size());

        if (CollectionUtils.isEmpty(firmwareList)) {
            throw new ValidationException(
                    ApiResponseEnum.CUSTOMER_PROFILE_INVALID_FW_ID.getCode(),
                    ApiResponseEnum.CUSTOMER_PROFILE_INVALID_FW_ID.getMessage());
        }

        Firmware firmware = firmwareList.stream()
                .filter(fw -> fw.getId().equals(firmwareId))
                .findAny()
                .orElseThrow(() -> new ValidationException(
                        ApiResponseEnum.CUSTOMER_PROFILE_INVALID_FW_ID.getCode(),
                        ApiResponseEnum.CUSTOMER_PROFILE_INVALID_FW_ID.getMessage()));

        return Optional.of(firmware);
    }

    private boolean isEquipmentModelIspConfigExist(String ispId, String equipmentModelId) {
        List<EquipmentModelIspConfig> config = equipmentModelIspConfigRepo
            .findByIspIdAndEquipmentModelId(ispId,equipmentModelId);
        return !CollectionUtils.isEmpty(config);
    }
}
