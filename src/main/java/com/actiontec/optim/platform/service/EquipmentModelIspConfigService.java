package com.actiontec.optim.platform.service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import com.actiontec.optim.platform.api.v6.dto.EquipmentModelIspConfigDTO;
import com.actiontec.optim.platform.api.v6.dto.EquipmentModelIspConfigRequest;
import com.actiontec.optim.platform.api.v6.dto.IdDTO;
import com.actiontec.optim.platform.api.v6.dto.IdListDTO;
import com.actiontec.optim.platform.api.v6.dto.PaginationDTO;
import com.actiontec.optim.platform.api.v6.dto.PaginationResponse;
import com.actiontec.optim.platform.model.EquipmentModel;
import com.actiontec.optim.platform.repository.EquipmentModelIspConfigRepo;
import com.actiontec.optim.util.CustomStringUtils;
import com.actiontec.optim.util.ValidationUtils;
import com.incs83.annotation.Transactional;
import com.incs83.app.business.v2.ISPService;
import com.incs83.app.business.v2.ManageCommonService;
import com.incs83.app.entities.EquipmentModelIspConfig;
import com.incs83.app.entities.Firmware;
import com.incs83.app.enums.ErrorResponseEnum;
import com.incs83.app.responsedto.v2.isp.ISPDTO;
import com.incs83.dao.Page;
import com.incs83.exceptions.handler.ValidationException;
import com.incs83.util.CommonUtils;

@Service
public class EquipmentModelIspConfigService {

    private final Logger logger = LogManager.getLogger(this.getClass());

    @Autowired
    EquipmentModelIspConfigRepo equipmentModelIspConfigRepo;

    @Autowired
    private ManageCommonService manageCommonService;

    @Autowired
    private EquipmentModelService equipmentModelService;

    @Autowired
    private FirmwareService firmwareService;

    @Autowired
    private ISPService ispService;

    @Autowired
    private UserService userService;

    public PaginationResponse<EquipmentModelIspConfigDTO> getAllEquipmentModelIspConfigs(
            EquipmentModelIspConfigRequest request) throws Exception {

        List<EquipmentModelIspConfigDTO> equipmentModelIspConfigs = null;

        PaginationResponse<EquipmentModelIspConfigDTO> response = new PaginationResponse<>();
        Page<EquipmentModelIspConfig> page = null;

        if (CommonUtils.isSysAdmin()) {
            page = equipmentModelIspConfigRepo.findAll(request.getOffset(), request.getLimit());
        } else if (CommonUtils.isGroupAdmin()) {
            String ispId = manageCommonService.getIspByGroupId(CommonUtils.getGroupIdOfLoggedInUser());
            page = equipmentModelIspConfigRepo.findByIspId(ispId, request.getOffset(), request.getLimit());
        }
        // Handle empty or null page case
        if (ObjectUtils.isEmpty(page) || ObjectUtils.isEmpty(page.getObjects())) {
            response.setData(Collections.emptyList());
            PaginationDTO pagination = PaginationDTO.builder()
                    .listSize(0)
                    .total(0)
                    .limit(request.getLimit())
                    .offset(request.getOffset())
                    .build();

            response.setPagination(pagination);
            return response;
        }

        // map build equipmentModelIspConfigs
        equipmentModelIspConfigs = page.getObjects().stream()
                .map(config -> EquipmentModelIspConfigDTO.buildEquipmentModelIspConfigDTO(config,
                        userService.getFullNameById(config.getUpdatedBy())))
                .collect(Collectors.toList());

        response.setData(equipmentModelIspConfigs);

        PaginationDTO pagination = PaginationDTO.builder()
                .listSize(response.getData().size())
                .total(page.getTotalCount())
                .limit(request.getLimit())
                .offset(request.getOffset())
                .build();

        response.setPagination(pagination);
        return response;
    }

    public EquipmentModelIspConfigDTO getEquipmentModelIspConfig(String configId) throws Exception {

        if (StringUtils.isBlank(configId)) {
            ValidationUtils.throwValidationException(
                    ErrorResponseEnum.EQUIPMENT_MODEL_ISP_CONFIG_INVALID_CONFIG_ID);
        }

        EquipmentModelIspConfig config = equipmentModelIspConfigRepo.findById(configId);

        if (ObjectUtils.isEmpty(config)) {
            ValidationUtils.throwNotFoundException(
                    ErrorResponseEnum.EQUIPMENT_MODEL_ISP_CONFIG_NOT_FOUND,
                    String.format("EquipmentModelIspConfig not found for id: %s", configId));
        }

        return EquipmentModelIspConfigDTO.buildEquipmentModelIspConfigDTO(config,
                userService.getFullNameById(config.getUpdatedBy()));
    }

    @Transactional
    public IdListDTO createEquipmentModelIspConfigs(List<EquipmentModelIspConfigRequest> requests) throws Exception {

        List<EquipmentModelIspConfig> equipmentModelIspConfigs = new ArrayList<>();

        // check requests modelIds and ispIds bundle are unique
        Map<String, String> modelIdsAndIspIds = new HashMap<>();
        for (EquipmentModelIspConfigRequest request : requests) {
            // check firmware id exist
            Firmware fwImage = validateCreateEquipmentModelIspConfigAndGetFirmware(request).get();

            if (modelIdsAndIspIds.containsKey(request.getModelId() + request.getIspId())) {
                ValidationUtils.throwConflictException(
                        ErrorResponseEnum.EQUIPMENT_MODEL_ISP_CONFIG_CONFLICT,
                        String.format(
                                "EquipmentModelIspConfig request duplication for ispId: %s, modelId: %s is already exists",
                                request.getIspId(), request.getModelId()));
            }
            modelIdsAndIspIds.put(request.getModelId() + request.getIspId(), StringUtils.EMPTY);

            equipmentModelIspConfigs.add(EquipmentModelIspConfig.builder()
                    .id(CommonUtils.generateUUID())
                    .ispId(request.getIspId())
                    .equipmentModelId(request.getModelId())
                    .minVersionFirmware(fwImage)
                    .createdBy(CommonUtils.getUserIdOfLoggedInUser())
                    .updatedBy(CommonUtils.getUserIdOfLoggedInUser())
                    .build());

        }

        if (!equipmentModelIspConfigRepo.create(equipmentModelIspConfigs)) {
            throw new Exception("Failed to create EquipmentModelIspConfigs");
        }

        return IdListDTO.builder()
                .ids(equipmentModelIspConfigs.stream()
                        .map(config -> config.getId())
                        .collect(Collectors.toList()))
                .build();
    }

    public IdDTO createEquipmentModelIspConfig(EquipmentModelIspConfigRequest request) throws Exception {

        Firmware fwImage = validateCreateEquipmentModelIspConfigAndGetFirmware(request).get();

        String id = CommonUtils.generateUUID();

        // create equipment model isp config
        if (!equipmentModelIspConfigRepo.create(CommonUtils.generateUUID(), request.getIspId(),
                request.getModelId(), fwImage, CommonUtils.getUserIdOfLoggedInUser())) {
            logger.warn("Failed to create EquipmentModelIspConfig for id: {}, ispId: {}, modelId: {}, firmwareId: {}",
                    id, request.getIspId(), request.getModelId(), request.getRequiredFirmwareId());
            throw new Exception("Failed to create EquipmentModelIspConfig");
        }

        return IdDTO.builder().id(id).build();
    }

    public IdDTO updateEquipmentModelIspConfig(String configId, EquipmentModelIspConfigRequest request)
            throws Exception {
        if (StringUtils.isBlank(configId)) {
            ValidationUtils.throwValidationException(
                    ErrorResponseEnum.EQUIPMENT_MODEL_ISP_CONFIG_INVALID_CONFIG_ID);
        }

        EquipmentModelIspConfig equipmentModelIspConfig = equipmentModelIspConfigRepo.findById(configId);
        if (ObjectUtils.isEmpty(equipmentModelIspConfig)) {
            ValidationUtils.throwNotFoundException(
                    ErrorResponseEnum.EQUIPMENT_MODEL_ISP_CONFIG_NOT_FOUND,
                    String.format("EquipmentModelIspConfig not found for id: %s", configId));
        }

        Firmware firmware = validateUpdateEquipmentModelIspConfigAndGetFirmware(configId, request);

        EquipmentModelIspConfig config = equipmentModelIspConfigRepo
                .update(configId, request.getModelId(), request.getIspId(),
                        firmware, CommonUtils.getUserIdOfLoggedInUser());

        return IdDTO.builder().id(config.getId()).build();
    }

    @Transactional
    public IdListDTO updateEquipmentModelIspConfigs(List<EquipmentModelIspConfigRequest> requests) throws Exception {

        // 1. verify that isp and modelId combination is unique
        for (EquipmentModelIspConfigRequest req : requests) {
            long count = requests.stream()
                    .filter(r -> r.getIspId().equals(req.getIspId())
                            && r.getModelId().equals(req.getModelId()))
                    .count();

            if (count > 1) {
                ValidationUtils.throwConflictException(
                        ErrorResponseEnum.EQUIPMENT_MODEL_ISP_CONFIG_CONFLICT,
                        String.format(
                                "EquipmentModelIspConfig request duplication for ispId: %s, modelId: %s is already exists",
                                req.getIspId(), req.getModelId()));
            }
        }

        List<String> requestConfigIds = requests.stream()
                .map(req -> req.getId())
                .collect(Collectors.toList());

        // 2. find all configs
        List<EquipmentModelIspConfig> existingConfigs = equipmentModelIspConfigRepo.findByIspIds(
                requests.stream()
                        .map(req -> req.getIspId())
                        .collect(Collectors.toList()));

        Map<String, String> existingConfigIdMap = existingConfigs.stream()
                .collect(Collectors.toMap(config -> config.getId(), config -> config.getId()));

        Map<String, String> existingIspIdAndModelIdMap = existingConfigs.stream()
                .collect(Collectors.toMap(config -> config.getIspId() + config.getEquipmentModelId(),
                        config -> config.getId()));

        List<EquipmentModelIspConfig> configsToUpdate = new ArrayList<>();
        try {
            for (EquipmentModelIspConfigRequest request : requests) {
                // 2.1 ensure all id is exist in db
                if (!existingConfigIdMap.containsKey(request.getId())) {
                    ValidationUtils.throwNotFoundException(
                            ErrorResponseEnum.EQUIPMENT_MODEL_ISP_CONFIG_NOT_FOUND,
                            String.format("EquipmentModelIspConfig not found for id: %s", request.getId()));
                }

                String existingIspIdAndModelId = existingIspIdAndModelIdMap
                        .get(request.getIspId() + request.getModelId());
                // 2.2 ensure all isp and modelId is not conflicted with other existing record
                if (!CustomStringUtils.isEmpty(existingIspIdAndModelId)
                        && !existingIspIdAndModelId.equals(request.getId())) {
                    ValidationUtils.throwConflictException(
                            ErrorResponseEnum.EQUIPMENT_MODEL_ISP_CONFIG_CONFLICT,
                            String.format(
                                    "EquipmentModelIspConfig request duplication for ispId: %s, modelId: %s is already exists",
                                    request.getIspId(), request.getModelId()));
                }

                // 2.3 ensure all dependency is correct
                Firmware firmware = validateUpdateEquipmentModelIspConfigAndGetFirmware(request);
                configsToUpdate.add(EquipmentModelIspConfig.builder()
                        .id(request.getId())
                        .ispId(request.getIspId())
                        .equipmentModelId(request.getModelId())
                        .minVersionFirmware(firmware)
                        .updatedBy(CommonUtils.getUserIdOfLoggedInUser())
                        .build());

                // 3. perform update
                equipmentModelIspConfigRepo.update(
                        request.getId(), request.getModelId(), request.getIspId(),
                        firmware, CommonUtils.getUserIdOfLoggedInUser()

                );
            }
        } catch (Exception e) {
            logger.error("Failed to update EquipmentModelIspConfig");
            throw e;
        }

        return IdListDTO.builder()
                .ids(requestConfigIds)
                .build();
    }

    @Transactional
    public IdListDTO deleteEquipmentModelIspConfigs(List<String> configIds) throws Exception {

        List<EquipmentModelIspConfig> equipmentModelIspConfigs = equipmentModelIspConfigRepo.findByIds(configIds);

        Map<String, String> modelIdsAndIspIds = equipmentModelIspConfigs.stream()
                .collect(Collectors.toMap(config -> config.getId(),
                        config -> config.getEquipmentModelId() + config.getIspId()));

        // Validate the configIds one by one
        for (String configId : configIds) {
            if (StringUtils.isBlank(configId)) {
                ValidationUtils.throwValidationException(
                        ErrorResponseEnum.EQUIPMENT_MODEL_ISP_CONFIG_INVALID_CONFIG_ID);
            }

            if (!modelIdsAndIspIds.containsKey(configId)) {
                ValidationUtils.throwNotFoundException(
                        ErrorResponseEnum.EQUIPMENT_MODEL_ISP_CONFIG_NOT_FOUND);
            }
        }

        if (equipmentModelIspConfigRepo.deleteByIds(configIds)) {
            List<String> idDtoList = equipmentModelIspConfigs.stream()
                    .map(config -> config.getId())
                    .collect(Collectors.toList());
            return IdListDTO.builder().ids(idDtoList).build();
        }

        throw new Exception("Delete EquipmentModelIspConfig failed");
    }

    public IdDTO deleteEquipmentModelIspConfig(String profileId) throws Exception {

        if (StringUtils.isBlank(profileId)) {
            ValidationUtils.throwValidationException(
                    ErrorResponseEnum.EQUIPMENT_MODEL_ISP_CONFIG_INVALID_CONFIG_ID);
        }

        if (ObjectUtils.isEmpty(equipmentModelIspConfigRepo.findById(profileId))) {
            ValidationUtils.throwNotFoundException(
                    ErrorResponseEnum.EQUIPMENT_MODEL_ISP_CONFIG_NOT_FOUND);
        }

        if (equipmentModelIspConfigRepo.deleteById(profileId)) {
            return IdDTO.builder().id(profileId).build();
        }

        throw new Exception("Delete EquipmentModelIspConfig failed");
    }

    private Optional<ISPDTO> validateAndGetIsp(String ispId) throws Exception {
        ISPDTO ispDto = null;
        try {
            ispDto = ispService.getISPById(ispId);
        } catch (Exception e) {
        }

        if (ObjectUtils.isEmpty(ispDto)) {
            ValidationUtils.throwValidationException(
                    ErrorResponseEnum.EQUIPMENT_MODEL_ISP_CONFIG_INVALID_ISP_ID,
                    String.format("ISP not found for id: %s", ispId));
        }
        return Optional.of(ispDto);
    }

    private Optional<EquipmentModel> validateAndGetEquipmentModel(String modelId) throws Exception {
        EquipmentModel equipmentModel = equipmentModelService.findModelById(modelId);
        if (ObjectUtils.isEmpty(equipmentModel)) {
            ValidationUtils.throwValidationException(
                    ErrorResponseEnum.EQUIPMENT_MODEL_ISP_CONFIG_INVALID_MODEL_ID);
        }

        return Optional.of(equipmentModel);
    }

    private Optional<Firmware> validateAndGetFirmware(String firmwareId, String equipmentModelId)
            throws Exception {

        List<Firmware> firmwareList = firmwareService.findByModelId(equipmentModelId);

        logger.info("Model Id: {}, Firmware sizes: {}", equipmentModelId, firmwareList.size());

        if (CollectionUtils.isEmpty(firmwareList)) {
            ValidationUtils.throwValidationException(
                    ErrorResponseEnum.EQUIPMENT_MODEL_ISP_CONFIG_INVALID_FW_ID);
        }

        Firmware firmware = firmwareList.stream()
                .filter(fw -> fw.getId().equals(firmwareId))
                .findAny()
                .orElseThrow(() -> new ValidationException(
                        ErrorResponseEnum.EQUIPMENT_MODEL_ISP_CONFIG_INVALID_FW_ID.getCode(),
                        ErrorResponseEnum.EQUIPMENT_MODEL_ISP_CONFIG_INVALID_FW_ID.getMessage()));

        return Optional.of(firmware);
    }

    private boolean isEquipmentModelIspConfigExist(String ispId, String equipmentModelId) {
        List<EquipmentModelIspConfig> config = equipmentModelIspConfigRepo
                .findByIspIdAndEquipmentModelId(ispId, equipmentModelId);
        return !CollectionUtils.isEmpty(config);
    }

    private Optional<Firmware> validateBaseEquipmentModelIspConfig(EquipmentModelIspConfigRequest request)
            throws Exception {
        // validate isp exist
        validateAndGetIsp(request.getIspId());

        // validate model exist
        validateAndGetEquipmentModel(request.getModelId());

        // check firmware id exist
        return validateAndGetFirmware(request.getRequiredFirmwareId(), request.getModelId());
    }

    private Optional<Firmware> validateCreateEquipmentModelIspConfigAndGetFirmware(
            EquipmentModelIspConfigRequest request)
            throws Exception {

        Firmware fwImage = validateBaseEquipmentModelIspConfig(request).get();

        // check if config already exists
        if (isEquipmentModelIspConfigExist(request.getIspId(), request.getModelId())) {
            ValidationUtils.throwConflictException(
                    ErrorResponseEnum.EQUIPMENT_MODEL_ISP_CONFIG_CONFLICT,
                    String.format("EquipmentModelIspConfig already exists for ispId: %s, modelId: %s",
                            request.getIspId(), request.getModelId()));
        }

        return Optional.of(fwImage);
    }

    private Firmware validateUpdateEquipmentModelIspConfigAndGetFirmware(EquipmentModelIspConfigRequest request)
            throws Exception {
        return validateUpdateEquipmentModelIspConfigAndGetFirmware(request.getId(), request);
    }

    private Firmware validateUpdateEquipmentModelIspConfigAndGetFirmware(String id,
            EquipmentModelIspConfigRequest request) throws Exception {

        Firmware firmware = validateBaseEquipmentModelIspConfig(request).get();

        // check if config already exists
        List<EquipmentModelIspConfig> configs = equipmentModelIspConfigRepo
                .findByIspIdAndEquipmentModelId(request.getIspId(), request.getModelId());

        // handle conflict if ispId and modelId exist
        configs.stream()
                .filter(config -> !ObjectUtils.isEmpty(config) && !id.equals(config.getId()))
                .findAny()
                .ifPresent(config -> {
                    ValidationUtils.throwConflictException(
                            ErrorResponseEnum.EQUIPMENT_MODEL_ISP_CONFIG_CONFLICT,
                            String.format("EquipmentModelIspConfig already exists for ispId: %s, modelId: %s",
                                    request.getIspId(), request.getModelId()));
                });
        return firmware;
    }
}
