package com.actiontec.optim.rpc.processor;

import com.actiontec.optim.rpc.core.RpcContext;
import com.actiontec.optim.rpc.core.RpcResponse;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Component;

import java.util.Map;

import static com.incs83.app.constants.misc.RpcConstants.CPE_API_EXTENDER_PAIRING;

@Component
public class ExtenderPairingRpcProcessor implements RpcProcessor {
    private final Logger logger = LogManager.getLogger(this.getClass());

    @Override
    public boolean canProcess(RpcContext context) {
        return CPE_API_EXTENDER_PAIRING.equals(context.getUri());
    }

    @Override
    public void preProcess(RpcContext context) throws Exception {
        RpcProcessor.super.preProcess(context);
    }

    @Override
    public RpcResponse postProcess(RpcContext context, Map<String, Object> rawResponse) throws Exception {
        logger.debug("Post-processing Extender Pairing API response for tid:[{}], method:[{}]",
                context.getTid(), context.getMethod());
        return RpcProcessor.super.postProcess(context, rawResponse);
    }

    @Override
    public RpcResponse handleError(RpcContext context, Exception error) {
        logger.error("Extender Pairing API error for tid:[{}], method:[{}]", context.getTid(), context.getMethod(), error);
        return RpcProcessor.super.handleError(context, error);
    }
}