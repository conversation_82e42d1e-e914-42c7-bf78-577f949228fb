package com.actiontec.optim.rpc.facade;

import com.actiontec.optim.platform.api.v5.model.FirmwareActionRequest;
import com.actiontec.optim.platform.api.v6.dto.CpeApiRequest;
import com.actiontec.optim.platform.model.FwImage;
import com.actiontec.optim.platform.model.firmware.RequestPayload;
import com.actiontec.optim.platform.service.FwImageService;
import com.actiontec.optim.rpc.core.RpcConfig;
import com.actiontec.optim.rpc.core.RpcContext;
import com.actiontec.optim.rpc.core.RpcManager;
import com.actiontec.optim.rpc.core.RpcResponse;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.incs83.util.CommonUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;

import static com.incs83.app.constants.misc.RpcConstants.*;

@Service
public class RpcServiceFacade {

    private final Logger logger = LogManager.getLogger(this.getClass());

    @Autowired
    private RpcManager rpcManager;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private FwImageService fwImageService;

    public CompletableFuture<RpcResponse> sendCpeApiRequest(String networkId, String serial, CpeApiRequest request, int timeout) {
        try {
            HashMap<String, Object> payloadMap = new HashMap<>();
            payloadMap.put("paths", request.getEndpoints());
            String payload = objectMapper.writeValueAsString(payloadMap);

            RpcContext context = RpcContext.builder()
                    .userId(networkId)
                    .serial(serial)
                    .uri(NCS_NETWORK_CPE_URI)
                    .method(CPE_API_METHOD_POST)
                    .payload(payload)
                    .build();

            RpcConfig config = RpcConfig.builder()
                    .timeoutSeconds(timeout)
                    .persistRequest(true)
                    .build();

            return rpcManager.sendRpc(context, config);

        } catch (Exception e) {
            return CompletableFuture.completedFuture(
                    RpcResponse.error(CommonUtils.generateUUID(), e.getMessage())
            );
        }
    }

    public CompletableFuture<RpcResponse> sendFirmwaresRequest(String networkId, String serial, int timeout) {
        try {
            String payload = "{}";

            RpcContext context = RpcContext.builder()
                    .userId(networkId)
                    .serial(serial)
                    .uri(FW_URL)
                    .method(CPE_API_METHOD_GET)
                    .payload(payload)
                    .build();

            RpcConfig config = RpcConfig.builder()
                    .timeoutSeconds(timeout)
                    .persistRequest(true)
                    .build();

            return rpcManager.sendRpc(context, config);

        } catch (Exception e) {
            return CompletableFuture.completedFuture(
                    RpcResponse.error(CommonUtils.generateUUID(), e.getMessage())
            );
        }
    }

    public CompletableFuture<RpcResponse> sendFwActionRequest(String networkId, String serial, String method, String actionId, FirmwareActionRequest actionRequest, int timeout) {
        try {
            String uri = FW_ACTIONS_URL + (actionId != null ? "/" + actionId : "");

            String payload;
            if (method.equals(CPE_API_METHOD_POST)) {
                FwImage fwImage = fwImageService.findFwImageById(actionRequest.getImageId());
                if (Objects.isNull(fwImage)) {
                    throw new IllegalArgumentException("No such file for imageId:" + actionRequest.getImageId());
                }

                RequestPayload requestPayload = new RequestPayload();
                requestPayload.setAction(actionRequest.getAction().toLowerCase());
                requestPayload.setActionId(actionId);
                if (StringUtils.isNotEmpty(fwImage.getPassword()))
                    requestPayload.getData().setPassword(fwImage.getPassword());
                if (StringUtils.isNotEmpty(fwImage.getUsername()))
                    requestPayload.getData().setUsername(fwImage.getUsername());
                requestPayload.getData().setUrl(fwImage.getSecureUrl());

                payload = objectMapper.writeValueAsString(requestPayload);
            } else {
                payload = "{}";
            }

            RpcContext context = RpcContext.builder()
                    .userId(networkId)
                    .serial(serial)
                    .uri(uri)
                    .method(method)
                    .payload(payload)
                    .build();

            RpcConfig config = RpcConfig.builder()
                    .timeoutSeconds(timeout)
                    .persistRequest(true)
                    .build();

            return rpcManager.sendRpc(context, config);

        } catch (Exception e) {
            return CompletableFuture.completedFuture(
                    RpcResponse.error(CommonUtils.generateUUID(), e.getMessage())
            );
        }
    }

    public CompletableFuture<RpcResponse> sendDmAccessRequest(String networkId, String serial, Map<String, Object> payloadMap, int timeout) {
        try {
            String payload = objectMapper.writeValueAsString(payloadMap);

            RpcContext context = RpcContext.builder()
                    .userId(networkId)
                    .serial(serial)
                    .uri(DM_ACCESS_URI)
                    .method(CPE_API_METHOD_POST)
                    .payload(payload)
                    .build();

            RpcConfig config = RpcConfig.builder()
                    .timeoutSeconds(timeout)
                    .persistRequest(true)
                    .build();

            return rpcManager.sendRpc(context, config);

        } catch (Exception e) {
            return CompletableFuture.completedFuture(
                    RpcResponse.error(CommonUtils.generateUUID(), e.getMessage())
            );
        }
    }

    public CompletableFuture<RpcResponse> sendEquipmentBlinkRequest(String networkId, String serial, Map<String, Object> payloadMap, int timeout) {
        try {
            String payload = objectMapper.writeValueAsString(payloadMap);

            RpcContext context = RpcContext.builder()
                    .userId(networkId)
                    .serial(serial)
                    .uri(CPE_API_EQUIPMENT_BLINK)
                    .method(CPE_API_METHOD_POST)
                    .payload(payload)
                    .build();

            RpcConfig config = RpcConfig.builder()
                    .timeoutSeconds(timeout)
                    .persistRequest(true)
                    .build();

            return rpcManager.sendRpc(context, config);
        } catch (Exception e) {
            return CompletableFuture.completedFuture(RpcResponse.error(CommonUtils.generateUUID(), e.getMessage()));
        }
    }

    public CompletableFuture<RpcResponse> sendDisclaimNetworkRequest(
            String networkId, String serial, Map<String, Object> payloadMap, int timeout) {
        try {
            String payload = objectMapper.writeValueAsString(payloadMap);

            RpcContext context = RpcContext.builder()
                    .userId(networkId)
                    .serial(serial)
                    .uri(CPE_API_EQUIPMENT_ON_BOARDING_REGISTER)
                    .method(CPE_API_METHOD_DELETE)
                    .payload(payload)
                    .build();

            RpcConfig config = RpcConfig.builder()
                    .timeoutSeconds(timeout)
                    .persistRequest(true)
                    .build();

            return rpcManager.sendRpc(context, config);
        } catch (Exception e) {
            return CompletableFuture.completedFuture(RpcResponse.error(CommonUtils.generateUUID(), e.getMessage()));
        }
    }

    public CompletableFuture<RpcResponse> sendDeviceActionRequest(String networkId, String serial, String deviceId, String action, int timeout) {
        try {
            // Format URI with deviceId parameter
            String rpcUri = String.format(DEVICE_ACTION_URI, deviceId);

            // Prepare payload
            HashMap<String, Object> payloadMap = new HashMap<>();
            payloadMap.put("action", action);

            String payload = objectMapper.writeValueAsString(payloadMap);

            RpcContext context = RpcContext.builder()
                    .userId(networkId)
                    .serial(serial)
                    .uri(rpcUri)
                    .method(CPE_API_METHOD_POST)
                    .payload(payload)
                    .build();

            RpcConfig config = RpcConfig.builder()
                    .timeoutSeconds(timeout)
                    .persistRequest(true)
                    .build();

            return rpcManager.sendRpc(context, config);

        } catch (Exception e) {
            return CompletableFuture.completedFuture(RpcResponse.error(CommonUtils.generateUUID(), e.getMessage()));
        }
    }

    public CompletableFuture<RpcResponse> sendExtenderPairingRequest(String networkId, String serial, Map<String, Object> payloadMap, String requestMethod, int timeout) {
        try {
            String payload = objectMapper.writeValueAsString(payloadMap);

            RpcContext context = RpcContext.builder()
                    .userId(networkId)
                    .serial(serial)
                    .uri(CPE_API_EXTENDER_PAIRING)
                    .method(requestMethod)
                    .payload(payload)
                    .build();

            RpcConfig config = RpcConfig.builder()
                    .timeoutSeconds(timeout)
                    .persistRequest(true)
                    .build();

            return rpcManager.sendRpc(context, config);
        } catch (Exception e) {
            return CompletableFuture.completedFuture(RpcResponse.error(CommonUtils.generateUUID(), e.getMessage()));
        }
    }

    public CompletableFuture<RpcResponse> sendContainerActionRequest(String networkId, String serial,
                                     String method, String containerId, boolean action, Map<String, Object> payloadMap, int timeout) {

        try {
            String rpcUri = containerId == null ? CONTAINER_URL : CONTAINER_URL + "/" + containerId;
            if (action) {
                rpcUri = rpcUri + "/actions";
            }

            String payload = objectMapper.writeValueAsString(payloadMap);

            RpcContext context = RpcContext.builder()
                    .userId(networkId)
                    .serial(serial)
                    .uri(rpcUri)
                    .method(method)
                    .payload(payload)
                    .build();

            RpcConfig config = RpcConfig.builder()
                    .timeoutSeconds(timeout)
                    .persistRequest(true)
                    .build();

            return rpcManager.sendRpc(context, config);

        } catch (Exception e) {
            return CompletableFuture.completedFuture(
                    RpcResponse.error(CommonUtils.generateUUID(), e.getMessage())
            );
        }
    }
}
